using System.Collections;
using System.Collections.Generic;
using UnityEngine;
using UnityEngine.UI;
using UnityEngine.EventSystems;

public class InventorySlot : <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, IDropHandler, IPointerEnterHandler, IPointerExitHandler
{
    private Image slotImage;
    private Color defaultColor;
    private Vector2Int gridPosition;
    private InventoryUI inventoryUI;

    [Header("Slot Settings")]
    [SerializeField] private float occupiedSlotAlpha = 0.3f; // Alpha value when slot is occupied
    [SerializeField] private float emptySlotAlpha = 0.8f;    // Alpha value when slot is empty
    
    private void Awake()
    {
        slotImage = GetComponent<Image>();
        defaultColor = slotImage.color;
        defaultColor.a = emptySlotAlpha;
        slotImage.color = defaultColor;
        inventoryUI = InventoryUI.Instance;
    }
    
    // Set the grid position for this slot
    public void SetGridPosition(int x, int y)
    {
        gridPosition = new Vector2Int(x, y);
    }
    
    // Called by InventoryUI's GenerateGrid method
    public void Initialize(int x, int y)
    {
        gridPosition = new Vector2Int(x, y);

        // Debug logging for top-left slot
        if (x == 0 && y == 0)
        {
            Debug.Log($"=== TOP-LEFT SLOT INITIALIZED ===");
            Debug.Log($"Slot {gameObject.name} initialized with position ({x},{y})");
        }
    }

    public void SetOccupied(bool isOccupied)
    {
        Color newColor = slotImage.color;
        newColor.a = isOccupied ? occupiedSlotAlpha : emptySlotAlpha;
        slotImage.color = newColor;
    }
    
    public void OnPointerEnter(PointerEventData eventData)
    {
        // Check if something is being dragged
        if (eventData.pointerDrag != null)
        {
            InventoryItemUIDrag draggedItem = eventData.pointerDrag.GetComponent<InventoryItemUIDrag>();
            if (draggedItem != null)
            {
                // Feedback is now handled by the InventoryItemUIDrag UpdateSlotHighlights method
                // This could be used for additional hover effects
            }
        }
        else
        {
            // Optional: highlight slot on hover when not dragging
            Color hoverColor = slotImage.color;
            hoverColor.r = 0.9f;
            hoverColor.g = 0.9f;
            hoverColor.b = 0.9f;
            slotImage.color = hoverColor;
        }
    }
    
    public void OnPointerExit(PointerEventData eventData)
    {
        // Only reset if not highlighted by a drag operation
        if (!IsHighlightedByDrag())
        {
            ResetColor();
        }
    }
    
    private bool IsHighlightedByDrag()
    {
        // This is a simple check - in a full implementation you'd check against the active highlight array
        return false;
    }

    public void OnDrop(PointerEventData eventData)
    {
        // When an item is dropped on this slot,
        // get the dragged item's drag handler component.
        InventoryItemUIDrag draggedItem = eventData.pointerDrag.GetComponent<InventoryItemUIDrag>();
        if (draggedItem != null && draggedItem.backendItem != null)
        {
            // Get the rotation state and size
            bool isRotated = draggedItem.backendItem.isRotated;
            Vector2Int itemSize = draggedItem.backendItem.GetRotatedSize();
            
            // Check if valid placement
            InventoryManager inventoryManager = inventoryUI.inventoryManager;
            if (inventoryManager.CanPlaceItem(draggedItem.backendItem, gridPosition, itemSize))
            {
                // Update the item's position in the inventory
                inventoryManager.UpdateItemPosition(
                    draggedItem.backendItem, 
                    gridPosition, 
                    itemSize,
                    isRotated
                );
                
                Debug.Log($"Item dropped and placed at grid position: {gridPosition}");
            }
            else
            {
                Debug.Log($"Cannot place item at grid position: {gridPosition}");
            }
        }
    }
    
    // Used to reset the slot color
    public void ResetColor()
    {
        Color newColor = defaultColor;
        // Maintain the current alpha value when resetting color
        newColor.a = slotImage.color.a;
        slotImage.color = newColor;
    }
    
    // Used to highlight the slot
    public void SetHighlight(Color highlightColor)
    {
        slotImage.color = highlightColor;
    }

    public Vector2Int GetGridPosition()
    {
        // Debug logging for top-left slot
        if (gridPosition.x == 0 && gridPosition.y == 0)
        {
            Debug.Log($"=== TOP-LEFT SLOT GetGridPosition() ===");
            Debug.Log($"Slot {gameObject.name} returning position {gridPosition}");
        }

        return gridPosition;
    }
}

