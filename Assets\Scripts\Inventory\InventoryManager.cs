using System.Collections;
using System.Collections.Generic;
using UnityEngine;

public class InventoryManager : MonoBehaviour
{
    [Head<PERSON>("Inventory Grid Size")]
    public int width = 6;
    public int height = 8;

    [Header("Item Drop Settings")]
    [Tooltip("Minimum distance from player to drop items")]
    public float minDropDistance = 0.8f;
    [Tooltip("Maximum distance from player to drop items")]
    public float maxDropDistance = 1.5f;
    [Tooltip("Maximum spread angle in degrees (creates a cone in front of player)")]
    public float maxSpreadAngle = 45f;

    // Event for inventory changes
    public delegate void UpdateInventoryChange();
    public event UpdateInventoryChange OnInventoryChanged;

    // Internal data storage
    private InventoryItem[,] inventoryGrid;
    public List<InventoryItem> items = new List<InventoryItem>();

    private void Awake()
    {
        // Initialize the grid
        inventoryGrid = new InventoryItem[width, height];
    }

    #region Item Management

    // Drop an item back into the world
    public void DropItem(InventoryItem item, Vector3 dropPosition)
    {
        DropItem(item, dropPosition, Vector3.zero);
    }

    // Drop an item back into the world with animation from player position
    public void DropItem(InventoryItem item, Vector3 dropPosition, Vector3 playerPosition)
    {
        if (item == null) return;

        // Calculate a random drop position in front of the player
        Vector3 randomDropPosition = CalculateRandomDropPosition(playerPosition);

        bool wasInHotbar = item.wasInHotbar;
        Debug.Log($"Dropping item {item.GetName()} at {randomDropPosition}, wasInHotbar: {wasInHotbar}");

        // Special handling for items that have been in hotbar
        if (wasInHotbar)
        {
            Debug.Log($"Using special handling for item {item.GetName()} that was in hotbar");

            // Force remove from items list first
            if (items.Contains(item))
            {
                items.Remove(item);
                Debug.Log($"Removed {item.GetName()} from items list");
            }

            // Clear all grid cells that might reference this item
            for (int x = 0; x < width; x++)
            {
                for (int y = 0; y < height; y++)
                {
                    if (inventoryGrid[x, y] == item)
                    {
                        inventoryGrid[x, y] = null;
                    }
                }
            }

            // Re-enable the item in the world
            item.transform.SetParent(null); // Unparent from inventory
            item.OnDrop(randomDropPosition, playerPosition);

            // Clean up any remaining references to this item in the items list
            // (OnDrop resets the item state, so it should no longer be tracked)
            items.RemoveAll(i => i == item || i == null);

            // Notify listeners of inventory change (item is now properly reset)
            OnInventoryChanged?.Invoke();
            return;
        }

        // Standard handling for normal items

        // First make sure the item is properly removed from the grid
        RemoveItemFromGrid(item);

        // Remove from item list
        if (items.Contains(item))
        {
            items.Remove(item);
            Debug.Log($"Removed {item.GetName()} from items list. Items count: {items.Count}");
        }
        else
        {
            Debug.LogWarning($"Item {item.GetName()} not found in items list when dropping");
        }

        // Re-enable the item in the world
        item.transform.SetParent(null); // Unparent from inventory
        item.OnDrop(randomDropPosition, playerPosition);

        // Clean up any remaining references to this item in the items list
        // (OnDrop resets the item state, so it should no longer be tracked)
        items.RemoveAll(i => i == item || i == null);

        // Log inventory state after dropping
        Debug.Log($"Inventory state after drop: {items.Count} items");

        // Force clear any cell references to this item, just to be safe
        for (int x = 0; x < width; x++)
        {
            for (int y = 0; y < height; y++)
            {
                if (inventoryGrid[x, y] == item)
                {
                    Debug.LogWarning($"Found stray reference to {item.GetName()} at ({x},{y}) - clearing");
                    inventoryGrid[x, y] = null;
                }
            }
        }

        // Notify listeners of inventory change
        OnInventoryChanged?.Invoke();
    }

    // Try to add an item at a specific position
    public bool TryAddItem(InventoryItem item, Vector2Int position, bool rotated)
    {
        // Get size based on rotation
        Vector2Int size = item.GetSize(rotated);

        // Verify position is valid
        if (!CanPlaceItem(item, position, size))
            return false;

        // Place the item in the grid
        PlaceItemInGrid(item, position, size);

        // Update item properties
        item.position = position;

        // Set the rotation state - important for rotated fits
        if (rotated != item.isRotated)
            item.SetRotation(rotated);

        // Add to inventory
        items.Add(item);
        item.transform.SetParent(transform);
        item.OnPickup();

        Debug.Log($"Added item {item.GetName()} to position {position}, rotation: {rotated}");

        return true;
    }

    // Add an item to the first available position
    public bool PickupItem(InventoryItem item)
    {
        if (item == null)
        {
            Debug.LogError("Cannot pickup null item");
            return false;
        }

        if (item.itemData == null)
        {
            Debug.LogError($"Item {item.name} has no itemData attached");
            return false;
        }

        // Check for available position with rotation optimization
        FindPositionResult result = FindFirstAvailablePositionWithRotation(item);

        // Check if position is valid
        if (!result.found)
        {
            Debug.Log($"No space available for item {item.itemData.itemName}, even with rotation");
            return false;
        }

        // Try to add the item with the appropriate rotation
        try
        {
            if (TryAddItem(item, result.position, result.shouldRotate))
            {
                // Trigger UI update
                OnInventoryChanged?.Invoke();
                return true;
            }
        }
        catch (System.Exception e)
        {
            Debug.LogError($"Error adding item to inventory: {e.Message}");
        }

        return false;
    }

    // Remove an item from the inventory
    public void RemoveItem(InventoryItem item)
    {
        // Clear grid cells
        RemoveItemFromGrid(item);

        // Remove from item list
        items.Remove(item);

        // Notify listeners
        OnInventoryChanged?.Invoke();
    }

    // Update an item's position and/or rotation
    public bool UpdateItemPosition(InventoryItem item, Vector2Int newPosition, Vector2Int size, bool rotated)
    {
        // Debug logging for top-left corner
        bool isTopLeft = (newPosition.x == 0 && newPosition.y == 0);
        if (isTopLeft)
        {
            Debug.Log($"=== UpdateItemPosition DEBUG for (0,0) ===");
            Debug.Log($"Item: {item.GetName()}, newPosition: {newPosition}, size: {size}, rotated: {rotated}");
        }

        // Store original state for rollback
        Vector2Int originalPosition = item.position;
        bool originalRotation = item.isRotated;

        if (isTopLeft)
        {
            Debug.Log($"Original position: {originalPosition}, original rotation: {originalRotation}");
        }

        // Remove from current position
        RemoveItemFromGrid(item);

        if (isTopLeft)
        {
            Debug.Log($"Item removed from grid, now checking if new position is valid...");
        }

        // Check if new position is valid
        if (!CanPlaceItemWithRotation(item, newPosition, rotated))
        {
            if (isTopLeft)
            {
                Debug.Log($"FAILED: Second validation check failed in UpdateItemPosition");
            }

            // Revert to original position
            Vector2Int originalSize = originalRotation ?
                new Vector2Int(item.itemData.size.y, item.itemData.size.x) :
                item.itemData.size;

            PlaceItemInGrid(item, originalPosition, originalSize);

            // Restore original state
            item.position = originalPosition;
            item.SetRotation(originalRotation);

            return false;
        }

        if (isTopLeft)
        {
            Debug.Log($"Second validation passed, placing item at new position...");
        }

        // Update item properties
        item.position = newPosition;
        item.SetRotation(rotated);

        // Place in new position
        PlaceItemInGrid(item, newPosition, size);

        // Notify listeners
        OnInventoryChanged?.Invoke();

        if (isTopLeft)
        {
            Debug.Log($"UpdateItemPosition completed successfully for (0,0)");
        }

        return true;
    }

    #endregion

    #region Grid Operations

    // Check if an item can be placed
    public bool CanPlaceItem(InventoryItem item, Vector2Int position, Vector2Int size)
    {
        // Check boundaries
        if (position.x < 0 || position.y < 0) return false;
        if (position.x + size.x > width) return false;
        if (position.y + size.y > height) return false;

        // Check grid occupancy
        for (int x = 0; x < size.x; x++)
        {
            for (int y = 0; y < size.y; y++)
            {
                if (inventoryGrid[position.x + x, position.y + y] != null)
                    return false;
            }
        }

        return true;
    }

    // Check if an item can be placed with rotation
    public bool CanPlaceItemWithRotation(InventoryItem item, Vector2Int position, bool rotated)
    {
        // Get size based on rotation
        Vector2Int size = rotated ?
            new Vector2Int(item.itemData.size.y, item.itemData.size.x) :
            item.itemData.size;

        // Debug logging specifically for top-left corner
        bool isTopLeft = (position.x == 0 && position.y == 0);
        if (isTopLeft)
        {
            Debug.Log($"=== CanPlaceItemWithRotation DEBUG for (0,0) ===");
            Debug.Log($"Item: {item.GetName()}, Position: {position}, Rotated: {rotated}, Size: {size}");
            Debug.Log($"Grid dimensions: {width}x{height}");
        }

        // Check boundaries
        if (position.x < 0 || position.y < 0)
        {
            if (isTopLeft) Debug.Log($"FAILED: Negative position check - x:{position.x}, y:{position.y}");
            return false;
        }
        if (position.x + size.x > width)
        {
            if (isTopLeft) Debug.Log($"FAILED: Width boundary check - pos.x:{position.x} + size.x:{size.x} = {position.x + size.x} > width:{width}");
            return false;
        }
        if (position.y + size.y > height)
        {
            if (isTopLeft) Debug.Log($"FAILED: Height boundary check - pos.y:{position.y} + size.y:{size.y} = {position.y + size.y} > height:{height}");
            return false;
        }

        if (isTopLeft) Debug.Log($"Boundary checks passed");

        // Check grid occupancy (excluding the item itself)
        for (int x = 0; x < size.x; x++)
        {
            for (int y = 0; y < size.y; y++)
            {
                int gridX = position.x + x;
                int gridY = position.y + y;
                InventoryItem itemAtPosition = inventoryGrid[gridX, gridY];

                if (isTopLeft)
                {
                    Debug.Log($"Checking grid[{gridX},{gridY}]: {(itemAtPosition != null ? itemAtPosition.GetName() : "null")} (same item: {itemAtPosition == item})");
                }

                if (itemAtPosition != null && itemAtPosition != item)
                {
                    if (isTopLeft) Debug.Log($"FAILED: Grid occupancy check at [{gridX},{gridY}] - occupied by {itemAtPosition.GetName()}");
                    return false;
                }
            }
        }

        if (isTopLeft) Debug.Log($"All checks passed - placement should be valid!");
        return true;
    }

    // Struct to hold position finding results
    private struct FindPositionResult
    {
        public Vector2Int position;
        public bool shouldRotate;
        public bool found;

        public FindPositionResult(Vector2Int pos, bool rotate, bool isFound)
        {
            position = pos;
            shouldRotate = rotate;
            found = isFound;
        }
    }

    // Find first available position, trying both orientations
    private FindPositionResult FindFirstAvailablePositionWithRotation(InventoryItem item)
    {
        if (item == null || item.itemData == null)
            return new FindPositionResult(Vector2Int.zero, false, false);

        Debug.Log($"Searching for position for {item.GetName()} - Size: {item.itemData.size}, Can rotate: {item.itemData.canRotate}");

        // First try without rotation
        Vector2Int normalPosition = FindFirstAvailablePosition(item, false);
        if (normalPosition.x >= 0 && normalPosition.y >= 0)
        {
            Debug.Log($"Found normal fit at {normalPosition} for {item.GetName()}");
            return new FindPositionResult(normalPosition, false, true);
        }

        // If item can be rotated, try with rotation
        if (item.itemData.canRotate)
        {
            Vector2Int rotatedSize = new Vector2Int(item.itemData.size.y, item.itemData.size.x);
            Debug.Log($"Trying rotated fit for {item.GetName()} - Rotated size: {rotatedSize}");

            Vector2Int rotatedPosition = FindFirstAvailablePosition(item, true);
            if (rotatedPosition.x >= 0 && rotatedPosition.y >= 0)
            {
                Debug.Log($"Found rotated fit at {rotatedPosition} for {item.GetName()}");
                return new FindPositionResult(rotatedPosition, true, true);
            }
        }

        Debug.Log($"No position found for {item.GetName()}, even with rotation");
        return new FindPositionResult(Vector2Int.zero, false, false);
    }

    // Find first available position for an item with a specific rotation
    private Vector2Int FindFirstAvailablePosition(InventoryItem item, bool useRotated)
    {
        Vector2Int size = item.GetSize(useRotated);

        // Check each position in the grid
        for (int x = 0; x < width; x++)
        {
            for (int y = 0; y < height; y++)
            {
                Vector2Int testPosition = new Vector2Int(x, y);
                if (CanPlaceItem(item, testPosition, size))
                    return testPosition;
            }
        }

        // No valid position found
        return new Vector2Int(-1, -1);
    }

    // Make this method public so HotbarSlot can call it
    public void RemoveItemFromGrid(InventoryItem item)
    {
        if (item == null) return;

        Vector2Int size = item.GetRotatedSize();
        Vector2Int position = item.position;

        // Validate position and size
        if (position.x < 0 || position.y < 0) return;
        if (position.x + size.x > width) return;
        if (position.y + size.y > height) return;

        // Clear the item references in the grid
        for (int x = 0; x < size.x; x++)
        {
            for (int y = 0; y < size.y; y++)
            {
                int gridX = position.x + x;
                int gridY = position.y + y;

                // Skip invalid indices
                if (gridX < 0 || gridY < 0 || gridX >= width || gridY >= height)
                    continue;

                // Only clear cells that contain this specific item
                if (inventoryGrid[gridX, gridY] == item)
                    inventoryGrid[gridX, gridY] = null;
            }
        }
    }

    // Place an item in the grid
    private void PlaceItemInGrid(InventoryItem item, Vector2Int position, Vector2Int size)
    {
        for (int x = 0; x < size.x; x++)
            for (int y = 0; y < size.y; y++)
                inventoryGrid[position.x + x, position.y + y] = item;
    }

    #endregion

    #region Accessors

    // Get the grid data
    public InventoryItem[,] GetInventoryGrid()
    {
        return inventoryGrid;
    }

    // Get all items
    public List<InventoryItem> GetAllItems()
    {
        return items;
    }

    #endregion

    #region Debug

    // Debug method to log inventory state
    public void LogInventoryState()
    {
        Debug.Log($"Inventory size: {width}x{height}, Items: {items.Count}");

        string grid = "Grid state:\n";
        for (int y = 0; y < height; y++)
        {
            string row = "";
            for (int x = 0; x < width; x++)
            {
                var item = inventoryGrid[x, y];
                row += item == null ? "□ " : "■ ";
            }
            grid += row + "\n";
        }
        Debug.Log(grid);
    }

    #endregion

    #region Public Event Triggers

    /// <summary>
    /// Public method to trigger inventory changed event (for external systems like WeaponSystem)
    /// </summary>
    public void TriggerInventoryChanged()
    {
        OnInventoryChanged?.Invoke();
    }

    #endregion

    #region Drop Position Calculation

    /// <summary>
    /// Calculate a random drop position in front of the player
    /// </summary>
    /// <param name="playerPosition">The player's current position</param>
    /// <returns>A random position in front of the player</returns>
    private Vector3 CalculateRandomDropPosition(Vector3 playerPosition)
    {
        // Get player facing direction from mouse position
        Vector3 playerFacingDirection = GetPlayerFacingDirection(playerPosition);

        // Calculate random distance and angle using serialized settings
        float randomDistance = Random.Range(minDropDistance, maxDropDistance);
        float randomAngle = Random.Range(-maxSpreadAngle, maxSpreadAngle);

        // Convert player facing direction to angle
        float baseAngle = Mathf.Atan2(playerFacingDirection.y, playerFacingDirection.x) * Mathf.Rad2Deg;

        // Add random spread to base angle
        float finalAngle = (baseAngle + randomAngle) * Mathf.Deg2Rad;

        // Calculate final position
        Vector3 dropOffset = new Vector3(
            Mathf.Cos(finalAngle) * randomDistance,
            Mathf.Sin(finalAngle) * randomDistance,
            0f
        );

        return playerPosition + dropOffset;
    }

    /// <summary>
    /// Get the player's facing direction based on mouse position
    /// </summary>
    /// <param name="playerPosition">The player's current position</param>
    /// <returns>Normalized facing direction vector</returns>
    private Vector3 GetPlayerFacingDirection(Vector3 playerPosition)
    {
        // Try to get the player controller to determine facing direction
        PlayerController playerController = FindObjectOfType<PlayerController>();
        if (playerController != null && playerController.cam != null)
        {
            // Get mouse world position
            Vector3 mouseWorldPos = playerController.cam.ScreenToWorldPoint(Input.mousePosition);
            mouseWorldPos.z = 0f;

            // Calculate direction from player to mouse
            Vector3 facingDirection = (mouseWorldPos - playerPosition).normalized;
            return facingDirection;
        }

        // Fallback: face downward if no player controller found
        return Vector3.down;
    }

    #endregion
}
