fileFormatVersion: 2
guid: c7b12578ccf8d9143b5953c7f52f3ea1
TextureImporter:
  internalIDToNameTable: []
  externalObjects: {}
  serializedVersion: 13
  mipmaps:
    mipMapMode: 0
    enableMipMap: 0
    sRGBTexture: 1
    linearTexture: 0
    fadeOut: 0
    borderMipMap: 0
    mipMapsPreserveCoverage: 0
    alphaTestReferenceValue: 0.5
    mipMapFadeDistanceStart: 1
    mipMapFadeDistanceEnd: 3
  bumpmap:
    convertToNormalMap: 0
    externalNormalMap: 0
    heightScale: 0.25
    normalMapFilter: 0
    flipGreenChannel: 0
  isReadable: 0
  streamingMipmaps: 0
  streamingMipmapsPriority: 0
  vTOnly: 0
  ignoreMipmapLimit: 0
  grayScaleToAlpha: 0
  generateCubemap: 6
  cubemapConvolution: 0
  seamlessCubemap: 0
  textureFormat: 1
  maxTextureSize: 2048
  textureSettings:
    serializedVersion: 2
    filterMode: 0
    aniso: 1
    mipBias: 0
    wrapU: 1
    wrapV: 1
    wrapW: 1
  nPOTScale: 0
  lightmap: 0
  compressionQuality: 50
  spriteMode: 2
  spriteExtrude: 1
  spriteMeshType: 1
  alignment: 0
  spritePivot: {x: 0.5, y: 0.5}
  spritePixelsToUnits: 32
  spriteBorder: {x: 0, y: 0, z: 0, w: 0}
  spriteGenerateFallbackPhysicsShape: 1
  alphaUsage: 1
  alphaIsTransparency: 1
  spriteTessellationDetail: -1
  textureType: 8
  textureShape: 1
  singleChannelComponent: 0
  flipbookRows: 1
  flipbookColumns: 1
  maxTextureSizeSet: 0
  compressionQualitySet: 0
  textureFormatSet: 0
  ignorePngGamma: 0
  applyGammaDecoding: 0
  swizzle: 50462976
  cookieLightType: 0
  platformSettings:
  - serializedVersion: 3
    buildTarget: DefaultTexturePlatform
    maxTextureSize: 2048
    resizeAlgorithm: 0
    textureFormat: -1
    textureCompression: 0
    compressionQuality: 50
    crunchedCompression: 0
    allowsAlphaSplitting: 0
    overridden: 0
    ignorePlatformSupport: 0
    androidETC2FallbackOverride: 0
    forceMaximumCompressionQuality_BC6H_BC7: 0
  - serializedVersion: 3
    buildTarget: Standalone
    maxTextureSize: 2048
    resizeAlgorithm: 0
    textureFormat: -1
    textureCompression: 1
    compressionQuality: 50
    crunchedCompression: 0
    allowsAlphaSplitting: 0
    overridden: 0
    ignorePlatformSupport: 0
    androidETC2FallbackOverride: 0
    forceMaximumCompressionQuality_BC6H_BC7: 0
  - serializedVersion: 3
    buildTarget: WebGL
    maxTextureSize: 2048
    resizeAlgorithm: 0
    textureFormat: -1
    textureCompression: 1
    compressionQuality: 50
    crunchedCompression: 0
    allowsAlphaSplitting: 0
    overridden: 0
    ignorePlatformSupport: 0
    androidETC2FallbackOverride: 0
    forceMaximumCompressionQuality_BC6H_BC7: 0
  spriteSheet:
    serializedVersion: 2
    sprites:
    - serializedVersion: 2
      name: PlayerGunAim_0
      rect:
        serializedVersion: 2
        x: 0
        y: 108
        width: 24
        height: 36
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: 6586b74ef83b2fa4c95856c006b7df87
      internalID: -1335504787
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: PlayerGunAim_1
      rect:
        serializedVersion: 2
        x: 24
        y: 108
        width: 24
        height: 36
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: 0bd8a7e6c2b34664c9843e58c214ed60
      internalID: 797854428
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: PlayerGunAim_2
      rect:
        serializedVersion: 2
        x: 48
        y: 108
        width: 24
        height: 36
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: f4304bf19ded4b04d886cc330215a6e3
      internalID: 1970970419
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: PlayerGunAim_3
      rect:
        serializedVersion: 2
        x: 72
        y: 108
        width: 24
        height: 36
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: 5922e1bb5ca047e4c8a5403c608af447
      internalID: -462122834
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: PlayerGunAim_4
      rect:
        serializedVersion: 2
        x: 0
        y: 72
        width: 24
        height: 36
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: bcde3a072741db843a6726bf92128f9b
      internalID: 219672411
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: PlayerGunAim_5
      rect:
        serializedVersion: 2
        x: 24
        y: 72
        width: 24
        height: 36
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: fa93da5446eabd64cae4044c21d7c52a
      internalID: -1122590823
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: PlayerGunAim_6
      rect:
        serializedVersion: 2
        x: 48
        y: 72
        width: 24
        height: 36
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: 38b374ff5af587f4dbbc4e9fcff05c3b
      internalID: 2024019591
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: PlayerGunAim_7
      rect:
        serializedVersion: 2
        x: 72
        y: 72
        width: 24
        height: 36
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: 29591a0f77b41a547b68dd485d3a1731
      internalID: 1049309475
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: PlayerGunAim_8
      rect:
        serializedVersion: 2
        x: 0
        y: 36
        width: 24
        height: 36
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: d3e73605f7126574e8ad1e9749479004
      internalID: -392564684
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: PlayerGunAim_9
      rect:
        serializedVersion: 2
        x: 24
        y: 36
        width: 24
        height: 36
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: c3e3b31751eee4647b4905279c73cf44
      internalID: 1526503427
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: PlayerGunAim_10
      rect:
        serializedVersion: 2
        x: 48
        y: 36
        width: 24
        height: 36
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: 277f8e631388b134eb3bc5ea5cdaa7a7
      internalID: -300742968
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: PlayerGunAim_11
      rect:
        serializedVersion: 2
        x: 72
        y: 36
        width: 24
        height: 36
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: da02c0e1a475cd44cb9f34c62a5c05be
      internalID: 1292656253
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: PlayerGunAim_12
      rect:
        serializedVersion: 2
        x: 0
        y: 0
        width: 24
        height: 36
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: 0b943402933ada9488acd12df7723b51
      internalID: 247359206
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: PlayerGunAim_13
      rect:
        serializedVersion: 2
        x: 24
        y: 0
        width: 24
        height: 36
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: c04b454dd82693249b3dfc0445d42d04
      internalID: -1683929152
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: PlayerGunAim_14
      rect:
        serializedVersion: 2
        x: 48
        y: 0
        width: 24
        height: 36
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: 4173ea2fb9d481f478d6b2b83773775c
      internalID: -949371785
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: PlayerGunAim_15
      rect:
        serializedVersion: 2
        x: 72
        y: 0
        width: 24
        height: 36
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: ca84c2c1e4c65984d954afa9237e0d44
      internalID: 488964697
      vertices: []
      indices: 
      edges: []
      weights: []
    outline: []
    physicsShape: []
    bones: []
    spriteID: 5e97eb03825dee720800000000000000
    internalID: 0
    vertices: []
    indices: 
    edges: []
    weights: []
    secondaryTextures: []
    nameFileIdTable:
      PlayerGunAim_0: -1335504787
      PlayerGunAim_1: 797854428
      PlayerGunAim_10: -300742968
      PlayerGunAim_11: 1292656253
      PlayerGunAim_12: 247359206
      PlayerGunAim_13: -1683929152
      PlayerGunAim_14: -949371785
      PlayerGunAim_15: 488964697
      PlayerGunAim_2: 1970970419
      PlayerGunAim_3: -462122834
      PlayerGunAim_4: 219672411
      PlayerGunAim_5: -1122590823
      PlayerGunAim_6: 2024019591
      PlayerGunAim_7: 1049309475
      PlayerGunAim_8: -392564684
      PlayerGunAim_9: 1526503427
  mipmapLimitGroupName: 
  pSDRemoveMatte: 0
  userData: 
  assetBundleName: 
  assetBundleVariant: 
