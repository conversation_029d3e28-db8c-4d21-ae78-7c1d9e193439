# Bullet System Setup Guide

## Overview
The bullet system spawns physical projectiles when weapons are fired, with hit detection, damage, and visual effects.

## Components

### 1. Bullet.cs
- Individual bullet projectile behavior
- Hit detection and damage
- Visual trail effects
- Automatic cleanup

### 2. BulletSpawner.cs
- Manages bullet creation for different weapon types
- Handles shotgun pellet spread
- Configurable fire points and bullet properties

### 3. IDamageable Interface
- Standard interface for objects that can take damage
- Implement on enemies, destructible objects, etc.

## Setup Instructions

### Step 1: Create Bullet Prefabs

#### Basic Bullet Prefab:
1. Create empty GameObject named "Bullet"
2. Add components:
   - **Bullet** script
   - **Rigidbody** (set to kinematic)
   - **Collider** (trigger, small sphere/capsule)
   - **TrailRenderer** (optional, for bullet trail)
3. Configure Bullet script:
   - Speed: 50
   - Max Distance: 100
   - Damage: 25
   - Hit Layers: Everything except player
   - Lifetime: 5 seconds

#### Trail Renderer Setup (Optional):
- Width: 0.1 to 0.05
- Time: 0.3 seconds
- Material: Bright colored material
- Color Gradient: Bright to transparent

#### Create Variants:
- **Pistol Bullet**: Standard settings
- **Rifle Bullet**: Higher speed (75), more damage (30)
- **Shotgun Pellet**: Lower speed (40), less damage (8)

### Step 2: Create Fire Points

#### On Player GameObject:
1. Create empty child objects for fire points:
   ```
   Player
   ├── PistolFirePoint
   ├── RifleFirePoint
   ├── ShotgunFirePoint
   └── DefaultFirePoint
   ```

2. Position fire points:
   - **Position**: Slightly in front of player (0, 1.5, 0.5)
   - **Rotation**: Match player forward direction
   - **Adjust**: Based on your player model/weapon positions

### Step 3: Setup BulletSpawner

#### Create BulletSpawner GameObject:
1. Create empty GameObject named "BulletSpawner"
2. Add **BulletSpawner** script
3. Configure references:
   - **Bullet Prefabs**: Drag your bullet prefabs
   - **Fire Points**: Drag the fire point transforms
   - **Properties**: Set default speed, damage, range

#### Example Configuration:
```
Default Bullet Speed: 50
Default Bullet Damage: 25
Default Bullet Range: 100
Shotgun Pellet Count: 8
Shotgun Spread: 15°
```

### Step 4: Connect to WeaponShootingSystem

1. Find your **WeaponShootingSystem** GameObject
2. In the script component:
   - **Bullet Spawner**: Drag the BulletSpawner GameObject

### Step 5: Setup Hit Layers

#### Create Layers:
1. Go to **Tags & Layers**
2. Create layers:
   - **Player** (layer 8)
   - **Enemy** (layer 9)
   - **Environment** (layer 10)
   - **Destructible** (layer 11)

#### Configure Bullet Hit Layers:
- Include: Enemy, Environment, Destructible
- Exclude: Player (so bullets don't hit the shooter)

### Step 6: Implement Damage System

#### For Enemies:
```csharp
public class Enemy : MonoBehaviour, IDamageable
{
    public float health = 100f;
    
    public void TakeDamage(float damage)
    {
        health -= damage;
        Debug.Log($"{name} took {damage} damage. Health: {health}");
        
        if (health <= 0)
        {
            Die();
        }
    }
    
    private void Die()
    {
        // Handle death
        Destroy(gameObject);
    }
}
```

#### For Destructible Objects:
```csharp
public class DestructibleObject : MonoBehaviour, IDamageable
{
    public float health = 50f;
    public GameObject destroyEffect;
    
    public void TakeDamage(float damage)
    {
        health -= damage;
        
        if (health <= 0)
        {
            if (destroyEffect != null)
                Instantiate(destroyEffect, transform.position, transform.rotation);
            
            Destroy(gameObject);
        }
    }
}
```

## Weapon-Specific Behavior

### Pistol
- **Single bullet** per shot
- **Medium speed** and damage
- **Standard range**

### Rifle
- **Single bullet** per shot
- **High speed** and damage
- **Long range**

### Shotgun
- **Multiple pellets** (8 by default)
- **Spread pattern** in cone
- **Shorter range**
- **Total damage** divided among pellets

## Visual Effects (Optional)

### Impact Effects:
1. Create particle systems for different surfaces:
   - **Metal Impact**: Sparks
   - **Wood Impact**: Splinters
   - **Concrete Impact**: Dust
2. Assign to bullet's **Impact Effect** field

### Muzzle Flash:
1. Create particle system at fire points
2. Trigger when bullet spawns
3. Brief, bright flash effect

## Performance Considerations

### Bullet Pooling (Advanced):
- For high fire rate weapons
- Reuse bullet objects instead of creating/destroying
- Implement object pooling system

### Hit Detection:
- Uses raycast for precise hit detection
- Bullets move with transform for visual
- Raycast prevents bullets passing through thin objects

## Debugging

### Console Output:
- Bullet hit messages with damage values
- Missing component warnings
- Range limit notifications

### Visual Debugging:
- Red gizmo lines show bullet direction in Scene view
- Trail renderers show bullet paths
- Impact effects show hit locations

## Troubleshooting

### Bullets Not Spawning:
- Check BulletSpawner reference in WeaponShootingSystem
- Verify bullet prefabs are assigned
- Ensure fire points are positioned correctly

### Bullets Not Hitting:
- Check hit layer configuration
- Verify colliders on targets
- Ensure bullet speed isn't too high

### Performance Issues:
- Reduce bullet lifetime
- Limit max bullets in scene
- Implement bullet pooling

### No Damage:
- Verify IDamageable implementation on targets
- Check damage values in bullet configuration
- Ensure hit layers include target objects
