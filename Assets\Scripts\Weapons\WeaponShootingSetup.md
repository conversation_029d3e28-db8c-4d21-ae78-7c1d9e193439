# Weapon Shooting System Setup Guide

## Overview
The WeaponShootingSystem provides comprehensive shooting mechanics with different weapon types, accuracy systems, and recoil mechanics.

## Weapon Types

### 1. Pistol (Semi-Automatic)
- **Firing Mode**: One click = one shot
- **Input**: Click Mouse 1 repeatedly
- **Characteristics**: Moderate accuracy, medium fire rate

### 2. Rifle (Fully Automatic)
- **Firing Mode**: Hold to fire continuously
- **Input**: Hold Mouse 1
- **Characteristics**: High fire rate, accuracy decreases with sustained fire

### 3. Shotgun (Pump-Action)
- **Firing Mode**: One click = one shot + pump delay
- **Input**: Click Mouse 1 (with delay between shots)
- **Characteristics**: Lower fire rate due to pump delay

### 4. Grenade (Special)
- **Firing Mode**: Special mechanics (to be implemented)

## Setup Instructions

### 1. Add WeaponShootingSystem to Scene
1. Create an empty GameObject named "WeaponShootingSystem"
2. Add the `WeaponShootingSystem` component
3. Configure references:
   - **Player Transform**: Drag your player GameObject
   - **Weapon System**: Should auto-find existing WeaponSystem
   - **Hotbar**: Should auto-find existing Hotbar

### 2. Configure Weapon Data
For each weapon ItemData asset, configure these new fields:

#### Pistol Example:
- **Weapon Type**: Pistol
- **Fire Rate**: 300 (rounds per minute)
- **Base Accuracy**: 2.0 (degrees)
- **Max Recoil Spread**: 8.0 (degrees)
- **Accuracy Recovery Rate**: 10.0 (degrees/second)
- **Movement Spread Multiplier**: 1.5
- **Pump Delay**: 0.0 (not used for pistols)

#### Rifle Example:
- **Weapon Type**: Rifle
- **Fire Rate**: 600 (rounds per minute)
- **Base Accuracy**: 1.0 (degrees)
- **Max Recoil Spread**: 15.0 (degrees)
- **Accuracy Recovery Rate**: 5.0 (degrees/second)
- **Movement Spread Multiplier**: 2.0
- **Pump Delay**: 0.0 (not used for rifles)

#### Shotgun Example:
- **Weapon Type**: Shotgun
- **Fire Rate**: 120 (rounds per minute)
- **Base Accuracy**: 8.0 (degrees - wider spread)
- **Max Recoil Spread**: 12.0 (degrees)
- **Accuracy Recovery Rate**: 8.0 (degrees/second)
- **Movement Spread Multiplier**: 1.8
- **Pump Delay**: 0.8 (seconds between shots)

### 3. Connect to Player Controller
Add this code to your player controller to update movement state:

```csharp
public class PlayerController : MonoBehaviour
{
    private WeaponShootingSystem shootingSystem;
    
    private void Start()
    {
        shootingSystem = FindObjectOfType<WeaponShootingSystem>();
    }
    
    private void Update()
    {
        // Update movement state for accuracy calculation
        bool isMoving = /* your movement detection logic */;
        if (shootingSystem != null)
        {
            shootingSystem.SetMovementState(isMoving);
        }
    }
}
```

## Controls

### Aiming (Required for Shooting)
- **Mouse 2 (Right Click)**: Hold to aim
- **Note**: Player can only shoot while aiming

### Shooting
- **Mouse 1 (Left Click)**: 
  - Pistol/Shotgun: Click to fire single shots
  - Rifle: Hold to fire continuously

### Reloading
- **Number Keys (1-9)**: Select weapon and reload
- **R Key**: Reload current weapon (if implemented in player controller)

## Accuracy System

### Base Accuracy
- Each weapon has a base accuracy cone in degrees
- 0° = perfect accuracy, higher values = more spread

### Recoil System
- Firing rapidly increases spread up to maximum
- Spread recovers over time when not firing
- Recovery rate configurable per weapon

### Movement Penalty
- Moving while shooting increases spread
- Multiplier applied to base accuracy when moving

### Spread Calculation
```
Total Spread = Base Accuracy + Current Recoil + Movement Penalty
```

## Debugging

### Console Output
The system provides detailed debug information:
- Shot direction and spread values
- Weapon state changes
- Firing attempts and results

### Inspector Values
In the WeaponShootingSystem inspector, you can monitor:
- Current spread value
- Time since last shot
- Firing state
- Pumping state (for shotguns)

## Customization

### Fire Rate
- Measured in rounds per minute (RPM)
- Higher values = faster firing
- Automatic weapons: continuous fire rate
- Semi-auto weapons: maximum click rate

### Accuracy Tuning
- **Base Accuracy**: Starting spread cone
- **Max Recoil Spread**: Maximum penalty from rapid fire
- **Recovery Rate**: How fast accuracy returns
- **Movement Multiplier**: Penalty for shooting while moving

### Pump Delay (Shotguns)
- Time between shots for pump-action weapons
- Simulates racking the pump
- Player cannot fire during pump delay

## Integration Notes

### WeaponSystem Integration
- WeaponShootingSystem calls WeaponSystem.FireWeapon()
- WeaponSystem handles ammo consumption
- Hotbar now only handles reloading when clicked

### Future Enhancements
- Muzzle flash effects
- Bullet trails/projectiles
- Hit detection and damage
- Sound effects
- Screen shake/camera recoil
- Crosshair accuracy indicator
