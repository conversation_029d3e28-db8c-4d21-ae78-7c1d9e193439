%YAML 1.1
%TAG !u! tag:unity3d.com,2011:
--- !u!114 &11400000
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 52d37c52629771a4ebf176d3c8a3a22b, type: 3}
  m_Name: Pistol_Ammo
  m_EditorClassIdentifier: 
  itemName: Pistol Ammo
  size: {x: 1, y: 2}
  itemType: 2
  inventoryIcon: {fileID: 21300000, guid: ea65fd3dc09ad5148a57ef43081d31b2, type: 3}
  worldSprite: {fileID: 21300000, guid: b3e346cb6bba64c49995b1f890995381, type: 3}
  canRotate: 1
  magazineSize: 15
  requiredAmmoType: 0
  weaponType: 0
  fireRate: 150
  baseAccuracy: 2
  maxRecoilSpread: 15
  accuracyRecoveryRate: 5
  movementSpreadMultiplier: 2
  pumpDelay: 0.8
  ammoType: 0
  maxStackSize: 30
