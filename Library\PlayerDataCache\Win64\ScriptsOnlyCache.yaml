ScriptsOnlyBuild:
  usedScripts:
    Assembly-CSharp.dll:
    - AmmoCounterUI
    - AnimationSmoother
    - Bullet
    - BulletSpawner
    - CameraFollow
    - Hotbar
    - InventoryItem
    - InventoryItemUI
    - InventoryItemUIDrag
    - InventoryManager
    - InventorySlot
    - InventoryUI
    - ItemData
    - LegsController
    - PlayerController
    - UIEventSystemManager
    - UIInputDebugger
    - WeaponShootingSystem
    - WeaponSystem
    Unity.2D.Animation.Editor.dll:
    - UnityEditor.U2D.Animation.BoneGizmo
    Unity.2D.Animation.Runtime.dll:
    - UnityEngine.U2D.Animation.BufferManager
    - UnityEngine.U2D.Animation.SpriteSkinComposite
    - UnityEngine.U2D.Animation.SpriteSkinUpdateHelper
    Unity.2D.IK.Editor.dll:
    - UnityEditor.U2D.IK.IKEditorManager
    Unity.2D.Tilemap.Editor.dll:
    - UnityEditor.Tilemaps.GridBrush
    - UnityEditor.Tilemaps.GridBrushEditor
    - UnityEditor.Tilemaps.GridPaintingState
    - UnityEditor.Tilemaps.GridPaletteBrushes
    - UnityEditor.Tilemaps.GridPalettes
    - UnityEditor.Tilemaps.SceneViewGridManager
    - UnityEditor.Tilemaps.SceneViewOpenTilePaletteHelper
    - UnityEditor.Tilemaps.TileDragAndDropManager
    - UnityEditor.Tilemaps.TilemapEditorToolPreferences
    - UnityEditor.Tilemaps.TilemapPrefabStageHelper
    Unity.2D.Tilemap.Extras.Editor.dll:
    - UnityEditor.Tilemaps.GameObjectBrush
    - UnityEditor.Tilemaps.GroupBrush
    - UnityEditor.Tilemaps.LineBrush
    - UnityEditor.Tilemaps.RandomBrush
    Unity.RenderPipelines.Core.Runtime:
    - UnityEngine.Rendering.UI.DebugUIHandlerBitField
    - UnityEngine.Rendering.UI.DebugUIHandlerButton
    - UnityEngine.Rendering.UI.DebugUIHandlerCanvas
    - UnityEngine.Rendering.UI.DebugUIHandlerColor
    - UnityEngine.Rendering.UI.DebugUIHandlerContainer
    - UnityEngine.Rendering.UI.DebugUIHandlerEnumField
    - UnityEngine.Rendering.UI.DebugUIHandlerEnumHistory
    - UnityEngine.Rendering.UI.DebugUIHandlerFloatField
    - UnityEngine.Rendering.UI.DebugUIHandlerFoldout
    - UnityEngine.Rendering.UI.DebugUIHandlerGroup
    - UnityEngine.Rendering.UI.DebugUIHandlerHBox
    - UnityEngine.Rendering.UI.DebugUIHandlerIndirectFloatField
    - UnityEngine.Rendering.UI.DebugUIHandlerIndirectToggle
    - UnityEngine.Rendering.UI.DebugUIHandlerIntField
    - UnityEngine.Rendering.UI.DebugUIHandlerMessageBox
    - UnityEngine.Rendering.UI.DebugUIHandlerObject
    - UnityEngine.Rendering.UI.DebugUIHandlerObjectList
    - UnityEngine.Rendering.UI.DebugUIHandlerObjectPopupField
    - UnityEngine.Rendering.UI.DebugUIHandlerPanel
    - UnityEngine.Rendering.UI.DebugUIHandlerPersistentCanvas
    - UnityEngine.Rendering.UI.DebugUIHandlerProgressBar
    - UnityEngine.Rendering.UI.DebugUIHandlerRow
    - UnityEngine.Rendering.UI.DebugUIHandlerToggle
    - UnityEngine.Rendering.UI.DebugUIHandlerToggleHistory
    - UnityEngine.Rendering.UI.DebugUIHandlerUIntField
    - UnityEngine.Rendering.UI.DebugUIHandlerVBox
    - UnityEngine.Rendering.UI.DebugUIHandlerValue
    - UnityEngine.Rendering.UI.DebugUIHandlerValueTuple
    - UnityEngine.Rendering.UI.DebugUIHandlerVector2
    - UnityEngine.Rendering.UI.DebugUIHandlerVector3
    - UnityEngine.Rendering.UI.DebugUIHandlerVector4
    - UnityEngine.Rendering.UI.UIFoldout
    Unity.RenderPipelines.Universal.Editor.dll:
    - UnityEditor.Rendering.Universal.UniversalProjectSettings
    Unity.RenderPipelines.Universal.Runtime.dll:
    - UnityEngine.Rendering.Universal.Light2D
    - UnityEngine.Rendering.Universal.PostProcessData
    - UnityEngine.Rendering.Universal.Renderer2DData
    - UnityEngine.Rendering.Universal.UniversalAdditionalCameraData
    - UnityEngine.Rendering.Universal.UniversalRenderPipelineAsset
    - UnityEngine.Rendering.Universal.UniversalRenderPipelineGlobalSettings
    Unity.Rider.Editor.dll:
    - Packages.Rider.Editor.UnitTesting.CallbackData
    Unity.TextMeshPro:
    - TMPro.TMP_ColorGradient
    - TMPro.TMP_FontAsset
    - TMPro.TMP_Settings
    - TMPro.TMP_SpriteAsset
    - TMPro.TMP_StyleSheet
    Unity.TextMeshPro.dll:
    - TMPro.TMP_FontAsset
    - TMPro.TMP_Settings
    - TMPro.TMP_SpriteAsset
    - TMPro.TMP_StyleSheet
    - TMPro.TextMeshProUGUI
    UnityEditor.Graphs.dll:
    - UnityEditor.Graphs.AnimationBlendTree.Graph
    - UnityEditor.Graphs.AnimationBlendTree.GraphGUI
    - UnityEditor.Graphs.AnimationStateMachine.AnyStateNode
    - UnityEditor.Graphs.AnimationStateMachine.Graph
    - UnityEditor.Graphs.AnimationStateMachine.GraphGUI
    - UnityEditor.Graphs.AnimationStateMachine.StateNode
    - UnityEditor.Graphs.AnimatorControllerTool
    UnityEditor.TestRunner.dll:
    - UnityEditor.TestTools.TestRunner.Api.CallbacksHolder
    - UnityEditor.TestTools.TestRunner.TestListCacheData
    - UnityEditor.TestTools.TestRunner.TestRun.TestJobDataHolder
    UnityEngine.UI:
    - UnityEngine.EventSystems.EventTrigger
    - UnityEngine.UI.Button
    - UnityEngine.UI.CanvasScaler
    - UnityEngine.UI.ContentSizeFitter
    - UnityEngine.UI.GraphicRaycaster
    - UnityEngine.UI.HorizontalLayoutGroup
    - UnityEngine.UI.Image
    - UnityEngine.UI.LayoutElement
    - UnityEngine.UI.Mask
    - UnityEngine.UI.ScrollRect
    - UnityEngine.UI.Scrollbar
    - UnityEngine.UI.Text
    - UnityEngine.UI.Toggle
    - UnityEngine.UI.VerticalLayoutGroup
    UnityEngine.UI.dll:
    - UnityEngine.UI.CanvasScaler
    - UnityEngine.UI.GraphicRaycaster
    - UnityEngine.UI.GridLayoutGroup
    - UnityEngine.UI.HorizontalLayoutGroup
    - UnityEngine.UI.Image
    - UnityEngine.UI.LayoutElement
  serializedClasses:
    Unity.RenderPipelines.Core.Runtime:
    - UnityEngine.Rendering.UI.DebugUIPrefabBundle
    Unity.RenderPipelines.Universal.Runtime:
    - UnityEngine.Rendering.Universal.Light2DBlendStyle
    - UnityEngine.Rendering.Universal.PostProcessData/ShaderResources
    - UnityEngine.Rendering.Universal.PostProcessData/TextureResources
    - UnityEngine.Rendering.Universal.ScriptableRendererData/DebugShaderResources
    - UnityEngine.Rendering.Universal.TemporalAA/Settings
    - UnityEngine.Rendering.Universal.UniversalRenderPipelineAsset/TextureResources
    Unity.TextMeshPro:
    - TMPro.FaceInfo_Legacy
    - TMPro.FontAssetCreationSettings
    - TMPro.KerningTable
    - TMPro.TMP_Character
    - TMPro.TMP_FontFeatureTable
    - TMPro.TMP_FontWeightPair
    - TMPro.TMP_GlyphAdjustmentRecord
    - TMPro.TMP_GlyphPairAdjustmentRecord
    - TMPro.TMP_GlyphValueRecord
    - TMPro.TMP_Sprite
    - TMPro.TMP_SpriteCharacter
    - TMPro.TMP_SpriteGlyph
    - TMPro.TMP_Style
    - TMPro.VertexGradient
    UnityEngine.CoreModule:
    - UnityEngine.Events.ArgumentCache
    - UnityEngine.Events.PersistentCallGroup
    - UnityEngine.Events.PersistentListenerMode
    - UnityEngine.RectOffset
    UnityEngine.TextCoreFontEngineModule:
    - UnityEngine.TextCore.FaceInfo
    - UnityEngine.TextCore.Glyph
    - UnityEngine.TextCore.GlyphMetrics
    - UnityEngine.TextCore.GlyphRect
    UnityEngine.UI:
    - UnityEngine.EventSystems.EventTrigger/Entry
    - UnityEngine.EventSystems.EventTrigger/TriggerEvent
    - UnityEngine.UI.AnimationTriggers
    - UnityEngine.UI.Button/ButtonClickedEvent
    - UnityEngine.UI.ColorBlock
    - UnityEngine.UI.FontData
    - UnityEngine.UI.MaskableGraphic/CullStateChangedEvent
    - UnityEngine.UI.Navigation
    - UnityEngine.UI.ScrollRect/ScrollRectEvent
    - UnityEngine.UI.Scrollbar/ScrollEvent
    - UnityEngine.UI.SpriteState
    - UnityEngine.UI.Toggle/ToggleEvent
  methodsToPreserve:
  - assembly: Unity.RenderPipelines.Core.Runtime
    fullTypeName: UnityEngine.Rendering.UI.DebugUIHandlerButton
    methodName: OnAction
  - assembly: Unity.RenderPipelines.Core.Runtime
    fullTypeName: UnityEngine.Rendering.UI.DebugUIHandlerPanel
    methodName: OnScrollbarClicked
  - assembly: Unity.RenderPipelines.Core.Runtime
    fullTypeName: UnityEngine.Rendering.UI.DebugUIHandlerPanel
    methodName: OnScrollbarClicked
  - assembly: Unity.RenderPipelines.Core.Runtime
    fullTypeName: UnityEngine.Rendering.UI.DebugUIHandlerIntField
    methodName: OnIncrement
  - assembly: Unity.RenderPipelines.Core.Runtime
    fullTypeName: UnityEngine.Rendering.UI.DebugUIHandlerIntField
    methodName: OnDecrement
  - assembly: Unity.RenderPipelines.Core.Runtime
    fullTypeName: UnityEngine.Rendering.UI.DebugUIHandlerIntField
    methodName: OnDecrement
  - assembly: Unity.RenderPipelines.Core.Runtime
    fullTypeName: UnityEngine.Rendering.UI.DebugUIHandlerIntField
    methodName: OnIncrement
  - assembly: Unity.RenderPipelines.Core.Runtime
    fullTypeName: UnityEngine.Rendering.UI.DebugUIHandlerUIntField
    methodName: OnDecrement
  - assembly: Unity.RenderPipelines.Core.Runtime
    fullTypeName: UnityEngine.Rendering.UI.DebugUIHandlerUIntField
    methodName: OnIncrement
  - assembly: Unity.RenderPipelines.Core.Runtime
    fullTypeName: UnityEngine.Rendering.UI.DebugUIHandlerUIntField
    methodName: OnIncrement
  - assembly: Unity.RenderPipelines.Core.Runtime
    fullTypeName: UnityEngine.Rendering.UI.DebugUIHandlerUIntField
    methodName: OnDecrement
  - assembly: Unity.RenderPipelines.Core.Runtime
    fullTypeName: UnityEngine.Rendering.UI.DebugUIHandlerObjectPopupField
    methodName: OnDecrement
  - assembly: Unity.RenderPipelines.Core.Runtime
    fullTypeName: UnityEngine.Rendering.UI.DebugUIHandlerObjectPopupField
    methodName: OnIncrement
  - assembly: Unity.RenderPipelines.Core.Runtime
    fullTypeName: UnityEngine.Rendering.UI.DebugUIHandlerPanel
    methodName: ResetDebugManager
  sceneClasses:
    Assets/Scenes/PlayerScene.unity:
    - Class: 1
      Script: {instanceID: 0}
    - Class: 4
      Script: {instanceID: 0}
    - Class: 20
      Script: {instanceID: 0}
    - Class: 21
      Script: {instanceID: 0}
    - Class: 28
      Script: {instanceID: 0}
    - Class: 48
      Script: {instanceID: 0}
    - Class: 49
      Script: {instanceID: 0}
    - Class: 50
      Script: {instanceID: 0}
    - Class: 58
      Script: {instanceID: 0}
    - Class: 61
      Script: {instanceID: 0}
    - Class: 74
      Script: {instanceID: 0}
    - Class: 81
      Script: {instanceID: 0}
    - Class: 91
      Script: {instanceID: 0}
    - Class: 95
      Script: {instanceID: 0}
    - Class: 96
      Script: {instanceID: 0}
    - Class: 104
      Script: {instanceID: 0}
    - Class: 114
      Script: {instanceID: 1786}
    - Class: 114
      Script: {instanceID: 1788}
    - Class: 114
      Script: {instanceID: 1790}
    - Class: 114
      Script: {instanceID: 1792}
    - Class: 114
      Script: {instanceID: 1794}
    - Class: 114
      Script: {instanceID: 1796}
    - Class: 114
      Script: {instanceID: 1804}
    - Class: 114
      Script: {instanceID: 1834}
    - Class: 114
      Script: {instanceID: 2156}
    - Class: 114
      Script: {instanceID: 2284}
    - Class: 114
      Script: {instanceID: 2506}
    - Class: 114
      Script: {instanceID: 2682}
    - Class: 114
      Script: {instanceID: 2870}
    - Class: 114
      Script: {instanceID: 3138}
    - Class: 114
      Script: {instanceID: 3332}
    - Class: 114
      Script: {instanceID: 3402}
    - Class: 114
      Script: {instanceID: 3538}
    - Class: 114
      Script: {instanceID: 3566}
    - Class: 114
      Script: {instanceID: 3874}
    - Class: 114
      Script: {instanceID: 4084}
    - Class: 114
      Script: {instanceID: 4698}
    - Class: 114
      Script: {instanceID: 4840}
    - Class: 114
      Script: {instanceID: 4882}
    - Class: 114
      Script: {instanceID: 4944}
    - Class: 114
      Script: {instanceID: 5002}
    - Class: 114
      Script: {instanceID: 5154}
    - Class: 114
      Script: {instanceID: 5170}
    - Class: 114
      Script: {instanceID: 5278}
    - Class: 114
      Script: {instanceID: 5370}
    - Class: 114
      Script: {instanceID: 5636}
    - Class: 114
      Script: {instanceID: 5642}
    - Class: 114
      Script: {instanceID: 5704}
    - Class: 114
      Script: {instanceID: 5802}
    - Class: 114
      Script: {instanceID: 5880}
    - Class: 114
      Script: {instanceID: 6238}
    - Class: 114
      Script: {instanceID: 6290}
    - Class: 114
      Script: {instanceID: 7106}
    - Class: 114
      Script: {instanceID: 7460}
    - Class: 114
      Script: {instanceID: 7906}
    - Class: 114
      Script: {instanceID: 8024}
    - Class: 114
      Script: {instanceID: 8086}
    - Class: 114
      Script: {instanceID: 8220}
    - Class: 114
      Script: {instanceID: 8586}
    - Class: 114
      Script: {instanceID: 8594}
    - Class: 114
      Script: {instanceID: 8924}
    - Class: 114
      Script: {instanceID: 8966}
    - Class: 114
      Script: {instanceID: 9066}
    - Class: 114
      Script: {instanceID: 9220}
    - Class: 114
      Script: {instanceID: 9286}
    - Class: 114
      Script: {instanceID: 9526}
    - Class: 114
      Script: {instanceID: 9616}
    - Class: 114
      Script: {instanceID: 9642}
    - Class: 114
      Script: {instanceID: 9858}
    - Class: 114
      Script: {instanceID: 9928}
    - Class: 114
      Script: {instanceID: 10486}
    - Class: 114
      Script: {instanceID: 10546}
    - Class: 114
      Script: {instanceID: 10594}
    - Class: 114
      Script: {instanceID: 10784}
    - Class: 114
      Script: {instanceID: 11030}
    - Class: 114
      Script: {instanceID: 11094}
    - Class: 114
      Script: {instanceID: 11318}
    - Class: 114
      Script: {instanceID: 11444}
    - Class: 114
      Script: {instanceID: 11526}
    - Class: 114
      Script: {instanceID: 11608}
    - Class: 114
      Script: {instanceID: 11626}
    - Class: 114
      Script: {instanceID: 11720}
    - Class: 114
      Script: {instanceID: 11860}
    - Class: 114
      Script: {instanceID: 12112}
    - Class: 114
      Script: {instanceID: 12446}
    - Class: 114
      Script: {instanceID: 12676}
    - Class: 114
      Script: {instanceID: 12884}
    - Class: 114
      Script: {instanceID: 30096}
    - Class: 114
      Script: {instanceID: 31866}
    - Class: 114
      Script: {instanceID: 31870}
    - Class: 115
      Script: {instanceID: 0}
    - Class: 128
      Script: {instanceID: 0}
    - Class: 157
      Script: {instanceID: 0}
    - Class: 196
      Script: {instanceID: 0}
    - Class: 212
      Script: {instanceID: 0}
    - Class: 213
      Script: {instanceID: 0}
    - Class: 222
      Script: {instanceID: 0}
    - Class: 223
      Script: {instanceID: 0}
    - Class: 224
      Script: {instanceID: 0}
    - Class: 225
      Script: {instanceID: 0}
  scriptHashData:
  - hash:
      serializedVersion: 2
      Hash: bf362e053d05d8a86558bc2be3e03493
    assemblyName: Unity.RenderPipelines.Universal.Runtime
    namespaceName: UnityEngine.Rendering.Universal
    className: UniversalRenderPipelineAsset
  - hash:
      serializedVersion: 2
      Hash: d41ab9b5ea92474f9c0a6937a0607ce1
    assemblyName: UnityEngine.UI
    namespaceName: UnityEngine.UI
    className: ContentSizeFitter
  - hash:
      serializedVersion: 2
      Hash: 8dff8e9853297d341afa0c11f9091b59
    assemblyName: UnityEngine.UI
    namespaceName: UnityEngine.UI
    className: LayoutElement
  - hash:
      serializedVersion: 2
      Hash: b5007673bbbdb9d12597a1d8201370f5
    assemblyName: UnityEngine.UI
    namespaceName: UnityEngine.UI
    className: Text
  - hash:
      serializedVersion: 2
      Hash: 915204dac8acad99f076d6e72ada0a0f
    assemblyName: UnityEngine.UI
    namespaceName: UnityEngine.UI
    className: Button
  - hash:
      serializedVersion: 2
      Hash: 540882762544409cc4530e60db00e951
    assemblyName: UnityEngine.UI
    namespaceName: UnityEngine.UI
    className: CanvasScaler
  - hash:
      serializedVersion: 2
      Hash: d5cd7f207e91ca8336c65a577a6fb2a0
    assemblyName: UnityEngine.UI
    namespaceName: UnityEngine.UI
    className: ScrollRect
  - hash:
      serializedVersion: 2
      Hash: ed2c59b75f8f3cd8df4cd4387d965e71
    assemblyName: Unity.RenderPipelines.Core.Runtime
    namespaceName: UnityEngine.Rendering.UI
    className: DebugUIHandlerVector2
  - hash:
      serializedVersion: 2
      Hash: 55060a7bf226ac385ce19ebd450f199f
    assemblyName: UnityEngine.UI
    namespaceName: UnityEngine.UIElements
    className: PanelRaycaster
  - hash:
      serializedVersion: 2
      Hash: dd9dda1dfd39e605c49e72003342ef5d
    assemblyName: UnityEngine.UI
    namespaceName: UnityEngine.UI
    className: Image
  - hash:
      serializedVersion: 2
      Hash: d75df0b83c74ab3d9088964ef8188507
    assemblyName: Unity.2D.Animation.Runtime
    namespaceName: UnityEngine.U2D.Animation
    className: SpriteSkin
  - hash:
      serializedVersion: 2
      Hash: f45600bcc886206dd55a3208474aad61
    assemblyName: Unity.VisualScripting.Core
    namespaceName: Unity.VisualScripting
    className: UnityOnScrollbarValueChangedMessageListener
  - hash:
      serializedVersion: 2
      Hash: 00bbd9db8634496d079d17711b2ff7c4
    assemblyName: UnityEngine.UI
    namespaceName: UnityEngine.UI
    className: Selectable
  - hash:
      serializedVersion: 2
      Hash: 95c8a8c68e664d4868755f06e7c55997
    assemblyName: Unity.RenderPipelines.Universal.Runtime
    namespaceName: UnityEngine.Rendering.Universal
    className: DepthOfField
  - hash:
      serializedVersion: 2
      Hash: 65d7429f900cd0d7d58933027dfdd825
    assemblyName: Unity.RenderPipelines.Universal.Runtime
    namespaceName: UnityEngine.Rendering.Universal
    className: CompositeShadowCaster2D
  - hash:
      serializedVersion: 2
      Hash: a9a9985bc600ab81586355efc506ee17
    assemblyName: Unity.RenderPipelines.Universal.Runtime
    namespaceName: UnityEngine.Rendering.Universal
    className: WhiteBalance
  - hash:
      serializedVersion: 2
      Hash: cbd6ca56349a9334ff0705e3eafa9b87
    assemblyName: Assembly-CSharp
    namespaceName: 
    className: InventoryItemUIDrag
  - hash:
      serializedVersion: 2
      Hash: 6b2b20d5352b9f1294595326f54bd3c2
    assemblyName: Unity.RenderPipelines.Core.Runtime
    namespaceName: UnityEngine.Rendering.UI
    className: DebugUIHandlerObjectPopupField
  - hash:
      serializedVersion: 2
      Hash: ad49e20f31ed572e586aa8bbaafb591b
    assemblyName: Unity.VisualScripting.Core
    namespaceName: Unity.VisualScripting
    className: UnityOnDropMessageListener
  - hash:
      serializedVersion: 2
      Hash: 00bcfff150a932126a4ea840d0861a6b
    assemblyName: Unity.2D.SpriteShape.Runtime
    namespaceName: UnityEngine.U2D
    className: SpriteShapeDefaultCreator
  - hash:
      serializedVersion: 2
      Hash: 21d396433bc155436b5004301182fbfa
    assemblyName: Unity.2D.PixelPerfect
    namespaceName: UnityEngine.U2D
    className: CinemachinePixelPerfect
  - hash:
      serializedVersion: 2
      Hash: 158ea04b653ef20da50f27da42170155
    assemblyName: Unity.RenderPipelines.Core.Runtime
    namespaceName: UnityEngine.Rendering
    className: ProbeVolumePerSceneData
  - hash:
      serializedVersion: 2
      Hash: b88e78b6860b6061b2c681ea9b80b6bf
    assemblyName: Unity.RenderPipelines.Core.Runtime
    namespaceName: UnityEngine.Rendering.UI
    className: DebugUIHandlerToggle
  - hash:
      serializedVersion: 2
      Hash: fce99de7ab700e0c4c466ebd583aa306
    assemblyName: Unity.RenderPipelines.Universal.Runtime
    namespaceName: UnityEngine.Rendering.Universal
    className: ColorLookup
  - hash:
      serializedVersion: 2
      Hash: 75563a97b24811c153add23104e2638f
    assemblyName: Unity.RenderPipelines.Universal.Runtime
    namespaceName: UnityEngine.Rendering.Universal
    className: XRSystemData
  - hash:
      serializedVersion: 2
      Hash: 1321993db8249e1e05e1e0492b2bbdc3
    assemblyName: Unity.VisualScripting.Core
    namespaceName: Unity.VisualScripting
    className: UnityOnBecameVisibleMessageListener
  - hash:
      serializedVersion: 2
      Hash: 177c970ec844bafc639374eb14ff5bda
    assemblyName: Assembly-CSharp
    namespaceName: 
    className: UIEventSystemManager
  - hash:
      serializedVersion: 2
      Hash: 04b6685b6f724c0d6024e6b6f96c8202
    assemblyName: Unity.Timeline
    namespaceName: UnityEngine.Timeline
    className: TimelineAsset
  - hash:
      serializedVersion: 2
      Hash: 7e12302f66e3c8ed5bcfea4ce65fccea
    assemblyName: Unity.VisualScripting.Core
    namespaceName: Unity.VisualScripting
    className: UnityMessageListener
  - hash:
      serializedVersion: 2
      Hash: ed8bac09ffacadca9994789e381c69ce
    assemblyName: Assembly-CSharp
    namespaceName: TMPro.Examples
    className: TMP_TextSelector_A
  - hash:
      serializedVersion: 2
      Hash: 2541c03d117382d4ebbe0bb0775dd2dc
    assemblyName: Unity.RenderPipelines.Core.Runtime
    namespaceName: UnityEngine.Rendering.UI
    className: DebugUIHandlerProgressBar
  - hash:
      serializedVersion: 2
      Hash: 3851b298bdb429888427e6274e04be2a
    assemblyName: Unity.RenderPipelines.Core.Runtime
    namespaceName: UnityEngine.Rendering
    className: ProbeReferenceVolumeProfile
  - hash:
      serializedVersion: 2
      Hash: b54ec4909f8701c636273ce6a9e8a878
    assemblyName: Assembly-CSharp
    namespaceName: 
    className: RuntimeShaderValidator
  - hash:
      serializedVersion: 2
      Hash: dbe4f05d59170430398f6453d7253fe5
    assemblyName: Unity.RenderPipelines.Core.Runtime
    namespaceName: UnityEngine.Rendering.UI
    className: DebugUIHandlerWidget
  - hash:
      serializedVersion: 2
      Hash: fce9b835d1e8b08e7ecea8da19f21986
    assemblyName: Unity.VisualScripting.Core
    namespaceName: Unity.VisualScripting
    className: CoroutineRunner
  - hash:
      serializedVersion: 2
      Hash: 28fd3d045acc780719a17f02073eb448
    assemblyName: UnityEngine.UI
    namespaceName: UnityEngine.UI
    className: InputField
  - hash:
      serializedVersion: 2
      Hash: 80aeb876b0e95f6256bbfcbf33e26e84
    assemblyName: Unity.2D.IK.Runtime
    namespaceName: UnityEngine.U2D.IK
    className: LimbSolver2D
  - hash:
      serializedVersion: 2
      Hash: 6d394de691f6d0187f61c08889353d0e
    assemblyName: Unity.VisualScripting.Core
    namespaceName: Unity.VisualScripting
    className: UnityOnButtonClickMessageListener
  - hash:
      serializedVersion: 2
      Hash: b98001682cba83786775c9e415b89a61
    assemblyName: Unity.VisualScripting.Core
    namespaceName: Unity.VisualScripting
    className: UnityOnDropdownValueChangedMessageListener
  - hash:
      serializedVersion: 2
      Hash: dc8678ffbada9b036eec210886629ed5
    assemblyName: Assembly-CSharp
    namespaceName: 
    className: InventoryUI
  - hash:
      serializedVersion: 2
      Hash: 1d830bc5cfaaf24301dffea2877f4bea
    assemblyName: Assembly-CSharp
    namespaceName: TMPro.Examples
    className: VertexShakeA
  - hash:
      serializedVersion: 2
      Hash: a5f52e289070cca28d53aa2d26bf266a
    assemblyName: Unity.2D.Animation.Runtime
    namespaceName: UnityEngine.U2D.Animation
    className: Bone
  - hash:
      serializedVersion: 2
      Hash: d90629714fa6b0145fcaffc87f5c6559
    assemblyName: Unity.VisualScripting.Core
    namespaceName: Unity.VisualScripting
    className: UnityOnInputFieldValueChangedMessageListener
  - hash:
      serializedVersion: 2
      Hash: bb8d968ef4474e71aaad4a4dce279f90
    assemblyName: Unity.VisualScripting.Core
    namespaceName: Unity.VisualScripting
    className: UnityOnMouseDownMessageListener
  - hash:
      serializedVersion: 2
      Hash: b6307dfc54cca9ba44f47185c578b0ce
    assemblyName: Unity.VisualScripting.Core
    namespaceName: Unity.VisualScripting
    className: UnityOnMouseDragMessageListener
  - hash:
      serializedVersion: 2
      Hash: 9f355e210e7950e47ad1391c7681435b
    assemblyName: Unity.RenderPipelines.Core.Runtime
    namespaceName: UnityEngine.Rendering.UI
    className: DebugUIHandlerEnumField
  - hash:
      serializedVersion: 2
      Hash: 99431ab619f390cf9400bd06d3e1bdd3
    assemblyName: Unity.RenderPipelines.Universal.Runtime
    namespaceName: UnityEngine.Rendering.Universal
    className: UniversalRenderPipelineGlobalSettings
  - hash:
      serializedVersion: 2
      Hash: 5c21f4846181ee6e245db1fcfb62fc0e
    assemblyName: Unity.RenderPipelines.Core.Runtime
    namespaceName: UnityEngine.Rendering.UI
    className: DebugUIHandlerContainer
  - hash:
      serializedVersion: 2
      Hash: 7bdff9e91edfd9600f92a185def7e34a
    assemblyName: Unity.VisualScripting.Flow
    namespaceName: Unity.VisualScripting
    className: ScriptMachine
  - hash:
      serializedVersion: 2
      Hash: 43ebd945b04bced376c791411cf5a289
    assemblyName: Unity.VisualScripting.Core
    namespaceName: Unity.VisualScripting
    className: UnityOnSelectMessageListener
  - hash:
      serializedVersion: 2
      Hash: 006cf428550f7af7c78bbd72b6f9c495
    assemblyName: Unity.RenderPipelines.Universal.Runtime
    namespaceName: UnityEngine.Rendering.Universal
    className: DecalRendererFeature
  - hash:
      serializedVersion: 2
      Hash: f5df081962c06a171f380eb236cd1f3c
    assemblyName: Unity.RenderPipelines.Core.Runtime
    namespaceName: UnityEngine.Rendering.UI
    className: DebugUIHandlerVBox
  - hash:
      serializedVersion: 2
      Hash: 418b67019f13f9684552f2693bae3971
    assemblyName: Assembly-CSharp
    namespaceName: TMPro.Examples
    className: TextMeshProFloatingText
  - hash:
      serializedVersion: 2
      Hash: 903edd2707c3c1a3ed0ecb2b27ecfab7
    assemblyName: Assembly-CSharp
    namespaceName: 
    className: InventoryManager
  - hash:
      serializedVersion: 2
      Hash: 960935b102b3064f4b924de16426e340
    assemblyName: Unity.TextMeshPro
    namespaceName: TMPro
    className: TextMeshPro
  - hash:
      serializedVersion: 2
      Hash: b2d2a01a2a85d9ed12e80cb33a9a6044
    assemblyName: Unity.Timeline
    namespaceName: UnityEngine.Timeline
    className: AnimationTrack
  - hash:
      serializedVersion: 2
      Hash: 6217b1287eca7eed313cf0b864249a30
    assemblyName: Unity.VisualScripting.Core
    namespaceName: Unity.VisualScripting
    className: UnityOnCollisionExit2DMessageListener
  - hash:
      serializedVersion: 2
      Hash: 709e7a14fd69c48de1a0f460ef868042
    assemblyName: Unity.2D.Tilemap.Extras
    namespaceName: UnityEngine.Tilemaps
    className: AdvancedRuleOverrideTile
  - hash:
      serializedVersion: 2
      Hash: 0a3f328f3ea906bbbcf794f4c27ee59d
    assemblyName: Unity.VisualScripting.Flow
    namespaceName: Unity.VisualScripting
    className: ScriptGraphAsset
  - hash:
      serializedVersion: 2
      Hash: e4b7831f0a9e91f079eb40ea24427631
    assemblyName: Unity.RenderPipelines.Universal.Runtime
    namespaceName: UnityEngine.Rendering.Universal
    className: ColorAdjustments
  - hash:
      serializedVersion: 2
      Hash: 72561d2e822b466169cd12a3b9f9ae0c
    assemblyName: Assembly-CSharp
    namespaceName: TMPro.Examples
    className: VertexShakeB
  - hash:
      serializedVersion: 2
      Hash: fb79e95da8891fc7ac6c36ca6967af23
    assemblyName: Unity.Timeline
    namespaceName: UnityEngine.Timeline
    className: ActivationTrack
  - hash:
      serializedVersion: 2
      Hash: 552dca338f20933ce362ebbe559a6aa6
    assemblyName: Unity.VisualScripting.Core
    namespaceName: Unity.VisualScripting
    className: UnityOnPointerExitMessageListener
  - hash:
      serializedVersion: 2
      Hash: be69ad766a85d6bb658fe24f39d961ab
    assemblyName: Assembly-CSharp
    namespaceName: 
    className: HotbarSlot
  - hash:
      serializedVersion: 2
      Hash: 1deda86fe7ffd8587afcfe68a02d034c
    assemblyName: Assembly-CSharp
    namespaceName: 
    className: EnvMapAnimator
  - hash:
      serializedVersion: 2
      Hash: 300c2738c9d1305f5898c02f4c45e42f
    assemblyName: Assembly-CSharp
    namespaceName: 
    className: WeaponSystem
  - hash:
      serializedVersion: 2
      Hash: 14f8c3da54a840e4c398ed3fa7b6057f
    assemblyName: Unity.2D.SpriteShape.Runtime
    namespaceName: 
    className: SpriteShapeGeometryCache
  - hash:
      serializedVersion: 2
      Hash: 682dfc653b22feea53b38adb3bc66414
    assemblyName: Unity.Timeline
    namespaceName: UnityEngine.Timeline
    className: SignalReceiver
  - hash:
      serializedVersion: 2
      Hash: 919dc7ec08f773fcae2d21a1e018bd99
    assemblyName: Unity.RenderPipelines.Universal.Runtime
    namespaceName: UnityEngine.Rendering.Universal
    className: MotionBlur
  - hash:
      serializedVersion: 2
      Hash: fe0a072dbdcbca34d5003c5299de3aaf
    assemblyName: Unity.RenderPipelines.Core.Runtime
    namespaceName: UnityEngine.Rendering
    className: SceneRenderPipeline
  - hash:
      serializedVersion: 2
      Hash: c933b590b3badb5918fa01e73cd5cc0c
    assemblyName: Unity.TextMeshPro
    namespaceName: TMPro
    className: TMP_Settings
  - hash:
      serializedVersion: 2
      Hash: ca7965796a6fc0e5baad4cb19f5b5ded
    assemblyName: Unity.VisualScripting.Core
    namespaceName: Unity.VisualScripting
    className: UnityOnJointBreak2DMessageListener
  - hash:
      serializedVersion: 2
      Hash: e416782d95a812c5b5b00e9b103a8e94
    assemblyName: Assembly-CSharp
    namespaceName: TMPro.Examples
    className: CameraController
  - hash:
      serializedVersion: 2
      Hash: 9f27b94c3c17b0da0d39632b61a4b711
    assemblyName: Unity.RenderPipelines.Universal.Runtime
    namespaceName: UnityEngine.Rendering.Universal
    className: Bloom
  - hash:
      serializedVersion: 2
      Hash: 96e7bcfd2d072992dab64ac44058b930
    assemblyName: Unity.VisualScripting.Core
    namespaceName: Unity.VisualScripting
    className: UnityOnCancelMessageListener
  - hash:
      serializedVersion: 2
      Hash: 52840a82fb83b3a7df342e354c4815e4
    assemblyName: Assembly-CSharp
    namespaceName: TMPro.Examples
    className: TMPro_InstructionOverlay
  - hash:
      serializedVersion: 2
      Hash: af770ad771658627851c64eafbf797f0
    assemblyName: Unity.2D.Tilemap.Extras
    namespaceName: UnityEngine.Tilemaps
    className: AnimatedTile
  - hash:
      serializedVersion: 2
      Hash: a75a568ce902366f4e82fc98ca6108f5
    assemblyName: Assembly-CSharp
    namespaceName: TMPro.Examples
    className: TMP_ExampleScript_01
  - hash:
      serializedVersion: 2
      Hash: dc31d211c7459841fe4f406ae2e51480
    assemblyName: Assembly-CSharp
    namespaceName: TMPro.Examples
    className: TextConsoleSimulator
  - hash:
      serializedVersion: 2
      Hash: 8fe6d4f095ce838f5396d1867140a9b6
    assemblyName: Unity.RenderPipelines.Core.Runtime
    namespaceName: UnityEngine.Rendering
    className: CameraSwitcher
  - hash:
      serializedVersion: 2
      Hash: 7e9cad7f71f9cc52f699bbd2d2a75734
    assemblyName: Unity.VisualScripting.Core
    namespaceName: Unity.VisualScripting
    className: UnityOnPointerClickMessageListener
  - hash:
      serializedVersion: 2
      Hash: c307188b084d7bae2b5fe7ace22248fb
    assemblyName: Unity.RenderPipelines.Universal.Runtime
    namespaceName: UnityEngine.Rendering.Universal
    className: UniversalRenderPipelineEditorResources
  - hash:
      serializedVersion: 2
      Hash: 14fb963022965ffa004cc4ec810b9fdd
    assemblyName: Unity.2D.Animation.Runtime
    namespaceName: UnityEngine.U2D.Animation
    className: BufferManager
  - hash:
      serializedVersion: 2
      Hash: aa349b22cd61b9e8488d736f82590d6b
    assemblyName: Assembly-CSharp
    namespaceName: TMPro.Examples
    className: TMP_TextInfoDebugTool
  - hash:
      serializedVersion: 2
      Hash: e88d71ef93aba4e7faf243430f6a10ce
    assemblyName: Unity.VisualScripting.Core
    namespaceName: Unity.VisualScripting
    className: MacroScriptableObject
  - hash:
      serializedVersion: 2
      Hash: 8dc40a01667ca02b3354a4e7a61be082
    assemblyName: UnityEngine.UI
    namespaceName: UnityEngine.UI
    className: GridLayoutGroup
  - hash:
      serializedVersion: 2
      Hash: ab1b4cfe05360a72297a8416f118ac83
    assemblyName: Assembly-CSharp
    namespaceName: 
    className: InventoryGrid
  - hash:
      serializedVersion: 2
      Hash: db9b308769d19e1f9e161df0392c23ca
    assemblyName: Unity.VisualScripting.Core
    namespaceName: Unity.VisualScripting
    className: UnityOnTransformChildrenChangedMessageListener
  - hash:
      serializedVersion: 2
      Hash: 6d94138b60a9299798dda96c1ee6725f
    assemblyName: Unity.VisualScripting.Core
    namespaceName: Unity.VisualScripting
    className: UnityOnBeginDragMessageListener
  - hash:
      serializedVersion: 2
      Hash: 415316b9bbc4e339c901a46503e2790b
    assemblyName: Unity.RenderPipelines.Core.Runtime
    namespaceName: UnityEngine.Rendering
    className: VolumeProfile
  - hash:
      serializedVersion: 2
      Hash: c833d8ddc320fe5153f6a88cef23c858
    assemblyName: Unity.RenderPipelines.Core.Runtime
    namespaceName: UnityEngine.Rendering.UI
    className: DebugUIHandlerObject
  - hash:
      serializedVersion: 2
      Hash: 10c17b309bdca62b4d7fbaf113ed4ca0
    assemblyName: UnityEngine.UI
    namespaceName: UnityEngine.UI
    className: Toggle
  - hash:
      serializedVersion: 2
      Hash: 73707dd24af053d88322ad9a735a180a
    assemblyName: Unity.2D.Tilemap.Extras
    namespaceName: UnityEngine.Tilemaps
    className: GridInformation
  - hash:
      serializedVersion: 2
      Hash: 33f160a5c07e580c29aba2bd1e4db811
    assemblyName: UnityEngine.UI
    namespaceName: UnityEngine.UI
    className: HorizontalLayoutGroup
  - hash:
      serializedVersion: 2
      Hash: 191cd500c627cd6682ef6f70b8e47f2c
    assemblyName: Unity.VisualScripting.Core
    namespaceName: Unity.VisualScripting
    className: UnityOnCollisionEnter2DMessageListener
  - hash:
      serializedVersion: 2
      Hash: 5f70b16cd9acc9c1dd88b51b6ed89669
    assemblyName: Unity.RenderPipelines.Core.Runtime
    namespaceName: UnityEngine.Rendering.UI
    className: DebugUIHandlerIndirectToggle
  - hash:
      serializedVersion: 2
      Hash: 0dd14e314956b0d0d83ffcffe60aa46f
    assemblyName: Assembly-CSharp
    namespaceName: TMPro
    className: TMP_DigitValidator
  - hash:
      serializedVersion: 2
      Hash: 3e1ba87a30ba656beab510dc2fa7cdc3
    assemblyName: Unity.RenderPipelines.Core.Runtime
    namespaceName: UnityEngine.Rendering
    className: ProbeVolumeAsset
  - hash:
      serializedVersion: 2
      Hash: 5874b789cd9832847c61ebfa803213af
    assemblyName: UnityEngine.UI
    namespaceName: UnityEngine.UIElements
    className: PanelEventHandler
  - hash:
      serializedVersion: 2
      Hash: e8fd74e57e72e8e4ef28792881adb864
    assemblyName: Unity.RenderPipelines.Core.Runtime
    namespaceName: UnityEngine.Rendering.UI
    className: DebugUIHandlerUIntField
  - hash:
      serializedVersion: 2
      Hash: 58570e231c93c2f96918856759b81286
    assemblyName: Unity.RenderPipelines.Core.Runtime
    namespaceName: UnityEngine.Rendering.UI
    className: DebugUIHandlerPanel
  - hash:
      serializedVersion: 2
      Hash: 9dd54ce33440072d6692a67e0c384ad1
    assemblyName: Unity.Timeline
    namespaceName: UnityEngine.Timeline
    className: GroupTrack
  - hash:
      serializedVersion: 2
      Hash: 5c2a5af4658ede896d072a8a641d706e
    assemblyName: Unity.RenderPipelines.Universal.Runtime
    namespaceName: UnityEngine.Rendering.Universal
    className: ColorCurves
  - hash:
      serializedVersion: 2
      Hash: bf0842d88681628b29beb00b5e2ab785
    assemblyName: Unity.VisualScripting.Core
    namespaceName: Unity.VisualScripting
    className: UnityOnDeselectMessageListener
  - hash:
      serializedVersion: 2
      Hash: 91d494c96669837f3255d7bd090b89de
    assemblyName: Assembly-CSharp
    namespaceName: 
    className: AmmoCounterUI
  - hash:
      serializedVersion: 2
      Hash: 1af7e9f99d251dfa1961d4b644ced380
    assemblyName: Unity.RenderPipelines.Universal.Runtime
    namespaceName: UnityEngine.Experimental.Rendering.Universal
    className: PixelPerfectCamera
  - hash:
      serializedVersion: 2
      Hash: a9f0153fc675e1f4ade818b83e4ff0c1
    assemblyName: Unity.RenderPipelines.Core.Runtime
    namespaceName: UnityEngine.Rendering
    className: VolumeComponent
  - hash:
      serializedVersion: 2
      Hash: ec77534f3498535da656056e35fb6798
    assemblyName: Assembly-CSharp
    namespaceName: 
    className: UIInputDebugger
  - hash:
      serializedVersion: 2
      Hash: 4166580c620718548959a77fa7b43fc1
    assemblyName: Assembly-CSharp
    namespaceName: TMPro.Examples
    className: TextMeshSpawner
  - hash:
      serializedVersion: 2
      Hash: 0e663c222bb83f92bce24cf73a0e299f
    assemblyName: Unity.RenderPipelines.Core.Runtime
    namespaceName: UnityEngine.Rendering
    className: ProbeTouchupVolume
  - hash:
      serializedVersion: 2
      Hash: ef38c1407dba8c1f5d69f5db61a694fe
    assemblyName: Unity.VisualScripting.Core
    namespaceName: Unity.VisualScripting
    className: AnimatorMessageListener
  - hash:
      serializedVersion: 2
      Hash: 47f78e94e3df8d612cefa1307a19e2ce
    assemblyName: Unity.RenderPipelines.Core.Runtime
    namespaceName: UnityEngine.Rendering.UI
    className: DebugUIHandlerValue
  - hash:
      serializedVersion: 2
      Hash: 9637dcdf3068a91dfe653e61bb82f13d
    assemblyName: Unity.TextMeshPro
    namespaceName: TMPro
    className: TextMeshProUGUI
  - hash:
      serializedVersion: 2
      Hash: c0a2b4d13675f08bfb211e044c334e68
    assemblyName: Unity.RenderPipelines.Core.Runtime
    namespaceName: UnityEngine.Rendering.UI
    className: DebugUIHandlerIntField
  - hash:
      serializedVersion: 2
      Hash: d771cabeaa363771c54f6c41824f7abb
    assemblyName: Unity.RenderPipelines.Universal.Runtime
    namespaceName: UnityEngine.Rendering.Universal
    className: LiftGammaGain
  - hash:
      serializedVersion: 2
      Hash: cde402e184a6ab530144506bc9d6a0af
    assemblyName: Unity.RenderPipelines.Core.Runtime
    namespaceName: UnityEngine.Rendering.UI
    className: DebugUIHandlerVector4
  - hash:
      serializedVersion: 2
      Hash: cb49ce80d4b69cb2ed30d83bfcb146ae
    assemblyName: Unity.RenderPipelines.Core.Runtime
    namespaceName: UnityEngine.Rendering.UI
    className: DebugUIHandlerBitField
  - hash:
      serializedVersion: 2
      Hash: 4f353524ce6564e40764f1ea080b2d85
    assemblyName: Unity.VisualScripting.Core
    namespaceName: Unity.VisualScripting
    className: UnityOnTriggerStay2DMessageListener
  - hash:
      serializedVersion: 2
      Hash: b4d459b3b1738aa4d408ebddbd5a7b22
    assemblyName: Assembly-CSharp
    namespaceName: 
    className: CameraPan
  - hash:
      serializedVersion: 2
      Hash: 13f262cc0741566a7ab72682984bc3e1
    assemblyName: Unity.TextMeshPro
    namespaceName: TMPro
    className: TMP_SubMesh
  - hash:
      serializedVersion: 2
      Hash: 672716ae97f6ddb615fa5ac3ec9da349
    assemblyName: UnityEngine.TestRunner
    namespaceName: UnityEngine.TestTools
    className: BeforeAfterTestCommandState
  - hash:
      serializedVersion: 2
      Hash: b87547877f7bea0dd59c10bfb798b858
    assemblyName: Unity.2D.Tilemap.Extras
    namespaceName: UnityEngine
    className: IsometricRuleTile
  - hash:
      serializedVersion: 2
      Hash: 40b5f3e3045df0d14e85d815f40f4fe5
    assemblyName: Unity.2D.SpriteShape.Runtime
    namespaceName: UnityEngine.U2D
    className: SpriteShapeController
  - hash:
      serializedVersion: 2
      Hash: f669be52680c88846b2093fd41d1e06e
    assemblyName: Unity.VisualScripting.Core
    namespaceName: Unity.VisualScripting
    className: UnityOnDragMessageListener
  - hash:
      serializedVersion: 2
      Hash: 20a120e55ad3ab65556328d5fd8309dd
    assemblyName: UnityEngine.UI
    namespaceName: UnityEngine.UI
    className: Outline
  - hash:
      serializedVersion: 2
      Hash: 2deddcdd5f664164bb803747210cbba2
    assemblyName: Unity.RenderPipelines.Core.Runtime
    namespaceName: UnityEngine.Rendering.UI
    className: DebugUIHandlerRow
  - hash:
      serializedVersion: 2
      Hash: 47247e29ff0f727264f83a7f754b229c
    assemblyName: Unity.RenderPipelines.Core.Runtime
    namespaceName: UnityEngine.Rendering
    className: LensFlareDataSRP
  - hash:
      serializedVersion: 2
      Hash: 4878e735441f4041539853759559c86f
    assemblyName: UnityEngine.UI
    namespaceName: UnityEngine.UI
    className: VerticalLayoutGroup
  - hash:
      serializedVersion: 2
      Hash: e0cf060d3a0d1afae0c661ac42606ab8
    assemblyName: Unity.RenderPipelines.Core.Runtime
    namespaceName: UnityEngine.Rendering
    className: ProbeVolume
  - hash:
      serializedVersion: 2
      Hash: 18b5a039687bef2846a340437da871a6
    assemblyName: Assembly-CSharp
    namespaceName: 
    className: ShaderBasedPickupIndicator
  - hash:
      serializedVersion: 2
      Hash: d2ecd94550edf1b56aff07b1f4e41c57
    assemblyName: Unity.VisualScripting.Core
    namespaceName: Unity.VisualScripting
    className: UnityOnTriggerEnter2DMessageListener
  - hash:
      serializedVersion: 2
      Hash: 8668b70b18eb7f5cd2dce04386848007
    assemblyName: Assembly-CSharp
    namespaceName: TMPro.Examples
    className: VertexJitter
  - hash:
      serializedVersion: 2
      Hash: e8d390bd9ecacc453a7500b90d0c0292
    assemblyName: UnityEngine.UI
    namespaceName: UnityEngine.EventSystems
    className: Physics2DRaycaster
  - hash:
      serializedVersion: 2
      Hash: afb7b65d28d5a7423e69a1b076be0064
    assemblyName: UnityEngine.UI
    namespaceName: UnityEngine.UI
    className: PositionAsUV1
  - hash:
      serializedVersion: 2
      Hash: 04f2d6a4db8a670aeb2c87d9ffd08865
    assemblyName: Assembly-CSharp
    namespaceName: TMPro
    className: TMP_TextEventHandler
  - hash:
      serializedVersion: 2
      Hash: a89b0448af4e1cd103a77f9fec0a961f
    assemblyName: Unity.VisualScripting.Core
    namespaceName: Unity.VisualScripting
    className: UnityOnSubmitMessageListener
  - hash:
      serializedVersion: 2
      Hash: 5f27242e1ad2aadb1faa604dd779a464
    assemblyName: Unity.2D.Animation.Runtime
    namespaceName: UnityEngine.U2D.Animation
    className: SpriteLibrary
  - hash:
      serializedVersion: 2
      Hash: 5ebc58ec194dd44630b25fcc270ad4af
    assemblyName: UnityEngine.UI
    namespaceName: UnityEngine.UI
    className: Dropdown
  - hash:
      serializedVersion: 2
      Hash: 4728e3d21cc2a12e391e71ec9b7eed0b
    assemblyName: Unity.Timeline
    namespaceName: UnityEngine.Timeline
    className: SignalTrack
  - hash:
      serializedVersion: 2
      Hash: 2f43860e745f8edade7bd381809e537b
    assemblyName: Unity.RenderPipelines.Universal.Runtime
    namespaceName: UnityEngine.Rendering.Universal
    className: UniversalAdditionalLightData
  - hash:
      serializedVersion: 2
      Hash: 2cc5af0f17cef9c717df88c8c7e4c9aa
    assemblyName: Unity.RenderPipelines.Core.Runtime
    namespaceName: UnityEngine
    className: LightAnchor
  - hash:
      serializedVersion: 2
      Hash: e20a6de5f1fd1dfae6f1186e8f77c3d9
    assemblyName: Unity.2D.Animation.Runtime
    namespaceName: UnityEngine.U2D.Animation
    className: SpriteResolver
  - hash:
      serializedVersion: 2
      Hash: d0392b7f3f52d5af537ade186b4f51f1
    assemblyName: Unity.Timeline
    namespaceName: UnityEngine.Timeline
    className: ControlTrack
  - hash:
      serializedVersion: 2
      Hash: c7461ddb2926b22edcbffe17bf941824
    assemblyName: Unity.RenderPipelines.Universal.Runtime
    namespaceName: UnityEngine.Rendering.Universal
    className: DecalProjector
  - hash:
      serializedVersion: 2
      Hash: 9a174471d4db2c0c6d3ac47d2f818db1
    assemblyName: Unity.VisualScripting.Core
    namespaceName: Unity.VisualScripting
    className: UnityOnParticleCollisionMessageListener
  - hash:
      serializedVersion: 2
      Hash: 22130d378b859a59f2cb973162c02f89
    assemblyName: Unity.2D.PixelPerfect
    namespaceName: UnityEngine.U2D
    className: PixelPerfectCamera
  - hash:
      serializedVersion: 2
      Hash: 0f5a086d4d028363983ba6ca30a01397
    assemblyName: Unity.VisualScripting.Core
    namespaceName: Unity.VisualScripting
    className: UnityOnMouseOverMessageListener
  - hash:
      serializedVersion: 2
      Hash: 24af424a437762b0c98a2238a41b2825
    assemblyName: Unity.RenderPipelines.Core.Runtime
    namespaceName: UnityEngine.Rendering.UI
    className: DebugUIHandlerButton
  - hash:
      serializedVersion: 2
      Hash: 2c583ad0ac85d03e8700dd05fdcb46cb
    assemblyName: Unity.VisualScripting.Core
    namespaceName: Unity.VisualScripting
    className: Variables
  - hash:
      serializedVersion: 2
      Hash: 502a0d788634601faaefeacfda55e23a
    assemblyName: Assembly-CSharp
    namespaceName: 
    className: DropdownSample
  - hash:
      serializedVersion: 2
      Hash: 9d9613c91f29a6f631f40f1e532fb7f6
    assemblyName: Unity.RenderPipelines.Universal.Runtime
    namespaceName: UnityEngine.Rendering.Universal
    className: CinemachineUniversalPixelPerfect
  - hash:
      serializedVersion: 2
      Hash: b30c8635462e66ac20f4241106119f77
    assemblyName: Unity.VisualScripting.State
    namespaceName: Unity.VisualScripting
    className: StateMachine
  - hash:
      serializedVersion: 2
      Hash: c4de0ff7e075dd89c9fed5913dd775d2
    assemblyName: Assembly-CSharp
    namespaceName: TMPro
    className: TMP_PhoneNumberValidator
  - hash:
      serializedVersion: 2
      Hash: c4ae0ec287bc3cde0bc98e1a65f9cef7
    assemblyName: Assembly-CSharp
    namespaceName: TMPro.Examples
    className: VertexColorCycler
  - hash:
      serializedVersion: 2
      Hash: 7ec8b9099254520135a0697569b2d0a9
    assemblyName: Unity.RenderPipelines.Universal.Runtime
    namespaceName: UnityEngine.Rendering.Universal
    className: UniversalRendererData
  - hash:
      serializedVersion: 2
      Hash: 1553f209ae0b2d5d3a35685fca55f9c3
    assemblyName: Unity.TextMeshPro
    namespaceName: TMPro
    className: TMP_ScrollbarEventHandler
  - hash:
      serializedVersion: 2
      Hash: 29362692ab8b7c46bfb7c8cb42bfe5b4
    assemblyName: Unity.2D.Animation.Runtime
    namespaceName: UnityEngine.U2D.Animation
    className: SpriteLibrarySourceAsset
  - hash:
      serializedVersion: 2
      Hash: 9eccc4340fcd994586e0dcb134c4612e
    assemblyName: Assembly-CSharp
    namespaceName: TMPro.Examples
    className: TeleType
  - hash:
      serializedVersion: 2
      Hash: 1362033cd491d66ad3cc206af29cc0de
    assemblyName: Unity.RenderPipelines.Core.Runtime
    namespaceName: UnityEngine.Rendering
    className: Volume
  - hash:
      serializedVersion: 2
      Hash: ff76cf6cf52b4db7b95af6ffadca9288
    assemblyName: Unity.Timeline
    namespaceName: UnityEngine.Timeline
    className: SignalEmitter
  - hash:
      serializedVersion: 2
      Hash: 02a47340661d2ea7c6d30e292cfef403
    assemblyName: Unity.VisualScripting.Core
    namespaceName: Unity.VisualScripting
    className: UnityOnToggleValueChangedMessageListener
  - hash:
      serializedVersion: 2
      Hash: 8bf800a764665b085fe469e8aea7f167
    assemblyName: Unity.VisualScripting.Core
    namespaceName: Unity.VisualScripting
    className: UnityOnMouseEnterMessageListener
  - hash:
      serializedVersion: 2
      Hash: 216f6c07bfa250e44b3e8d62572474e8
    assemblyName: Assembly-CSharp
    namespaceName: TMPro.Examples
    className: Benchmark01
  - hash:
      serializedVersion: 2
      Hash: 4fcf0bb4aff1d14a1dfcdedc4b07bd05
    assemblyName: UnityEngine.UI
    namespaceName: UnityEngine.EventSystems
    className: TouchInputModule
  - hash:
      serializedVersion: 2
      Hash: 3ca7f2be93d5156a91693dfd4bd38e3f
    assemblyName: Unity.2D.IK.Runtime
    namespaceName: UnityEngine.U2D.IK
    className: FabrikSolver2D
  - hash:
      serializedVersion: 2
      Hash: de2ca1c3321a87f5b291b5b7f45bb004
    assemblyName: Assembly-CSharp
    namespaceName: 
    className: InventoryItemUI
  - hash:
      serializedVersion: 2
      Hash: 8d7ab1303b1c14da5fdd62bf96ca6e89
    assemblyName: Unity.RenderPipelines.Core.Runtime
    namespaceName: UnityEngine.Rendering
    className: FreeCamera
  - hash:
      serializedVersion: 2
      Hash: a3901985eb076c1c9b7562fca82ca3e0
    assemblyName: Unity.Timeline
    namespaceName: UnityEngine.Timeline
    className: SignalAsset
  - hash:
      serializedVersion: 2
      Hash: 3586e7f4fd3cb20328f9458141273dab
    assemblyName: Unity.RenderPipelines.Universal.Runtime
    namespaceName: UnityEngine.Rendering.Universal
    className: ScreenSpaceAmbientOcclusion
  - hash:
      serializedVersion: 2
      Hash: d0caa48b5fe6778ce3f0de7aec45c4e6
    assemblyName: Assembly-CSharp
    namespaceName: TMPro.Examples
    className: SkewTextExample
  - hash:
      serializedVersion: 2
      Hash: 8703b6ae2b0eefd9dafbc44d8d333fb1
    assemblyName: UnityEngine.UI
    namespaceName: UnityEngine.UI
    className: Shadow
  - hash:
      serializedVersion: 2
      Hash: f778e23b6ff26b8de4721be0eb1fa240
    assemblyName: Unity.VisualScripting.Core
    namespaceName: Unity.VisualScripting
    className: UnityOnCollisionStayMessageListener
  - hash:
      serializedVersion: 2
      Hash: f854af586011dd194574f89546642e29
    assemblyName: Unity.VisualScripting.Core
    namespaceName: Unity.VisualScripting
    className: UnityOnMouseExitMessageListener
  - hash:
      serializedVersion: 2
      Hash: 11a492830b189fafb9ed2f7afa464c56
    assemblyName: Assembly-CSharp
    namespaceName: TMPro.Examples
    className: SimpleScript
  - hash:
      serializedVersion: 2
      Hash: 64d2a7f1ae5a761fdb44d1100b04d321
    assemblyName: Assembly-CSharp
    namespaceName: 
    className: InventorySlot
  - hash:
      serializedVersion: 2
      Hash: 75c6c9197c7a58bd598182947369a142
    assemblyName: Unity.RenderPipelines.Universal.Runtime
    namespaceName: UnityEngine.Experimental.Rendering.Universal
    className: RenderObjects
  - hash:
      serializedVersion: 2
      Hash: b12640dfdb5966813e9dd838893e6c4a
    assemblyName: Unity.VisualScripting.Core
    namespaceName: Unity.VisualScripting
    className: UnityOnTriggerEnterMessageListener
  - hash:
      serializedVersion: 2
      Hash: 0c6de72eaf8c1070da824268c4bfad9d
    assemblyName: Unity.TextMeshPro
    namespaceName: TMPro
    className: TMP_StyleSheet
  - hash:
      serializedVersion: 2
      Hash: 1bc6ff8d02d2d68019aa6a028f8d409d
    assemblyName: Unity.VisualScripting.Core
    namespaceName: Unity.VisualScripting
    className: UnityOnPointerUpMessageListener
  - hash:
      serializedVersion: 2
      Hash: 7e93247d600e115dac0cdd880de94e99
    assemblyName: UnityEngine.TestRunner
    namespaceName: UnityEngine.TestTools.TestRunner
    className: PlaymodeTestsController
  - hash:
      serializedVersion: 2
      Hash: 83cfda82082c233d96cc677c19f57e07
    assemblyName: Unity.VisualScripting.Core
    namespaceName: Unity.VisualScripting
    className: UnityOnJointBreakMessageListener
  - hash:
      serializedVersion: 2
      Hash: b72d9c057c0f9460abf04218f4cc4f0e
    assemblyName: Unity.RenderPipelines.Core.Runtime
    namespaceName: UnityEngine.Rendering.UI
    className: DebugUIHandlerMessageBox
  - hash:
      serializedVersion: 2
      Hash: 1ab136900c0edaeeb49424c852595030
    assemblyName: UnityEngine.UI
    namespaceName: UnityEngine.UI
    className: RawImage
  - hash:
      serializedVersion: 2
      Hash: 674f22ab166efb9fbd2bbc5a88542c7e
    assemblyName: Unity.2D.IK.Runtime
    namespaceName: UnityEngine.U2D.IK
    className: IKManager2D
  - hash:
      serializedVersion: 2
      Hash: 27fcf46f05da57849d306826fb32eb18
    assemblyName: Unity.RenderPipelines.Universal.Runtime
    namespaceName: UnityEngine.Rendering.Universal
    className: SplitToning
  - hash:
      serializedVersion: 2
      Hash: ec59bc56f69d3d13d1fc9d3e087fb56d
    assemblyName: Unity.2D.Animation.Runtime
    namespaceName: UnityEngine.U2D.Animation
    className: SkeletonAsset
  - hash:
      serializedVersion: 2
      Hash: da281dc879c7932e0c9669da88ae40c0
    assemblyName: Unity.RenderPipelines.Core.Runtime
    namespaceName: UnityEngine.Rendering.UI
    className: DebugUIHandlerGroup
  - hash:
      serializedVersion: 2
      Hash: 37dbaff32a49c70ff37b8bb15bd24c18
    assemblyName: Unity.RenderPipelines.Core.Runtime
    namespaceName: UnityEngine.Rendering
    className: LensFlareComponentSRP
  - hash:
      serializedVersion: 2
      Hash: c9b1ea3bf987a2e780a24849503ca72e
    assemblyName: Assembly-CSharp
    namespaceName: TMPro.Examples
    className: Benchmark01_UGUI
  - hash:
      serializedVersion: 2
      Hash: e987953190eb4c12cf19df790566dc2c
    assemblyName: UnityEngine.UI
    namespaceName: UnityEngine.EventSystems
    className: EventSystem
  - hash:
      serializedVersion: 2
      Hash: 52db48e3be0a2b4f8b06f918fd1ebfbc
    assemblyName: Unity.TextMeshPro
    namespaceName: TMPro
    className: TMP_Dropdown
  - hash:
      serializedVersion: 2
      Hash: f295412bc00b58b9095a71e9764cc886
    assemblyName: Unity.Timeline
    namespaceName: UnityEngine.Timeline
    className: AudioTrack
  - hash:
      serializedVersion: 2
      Hash: ada873da50974d1e7ce247ffb296b0f3
    assemblyName: Unity.VisualScripting.Core
    namespaceName: Unity.VisualScripting
    className: UnityOnCollisionEnterMessageListener
  - hash:
      serializedVersion: 2
      Hash: e9db29e31ba60222fa1690a9445f34f5
    assemblyName: Unity.2D.Tilemap.Extras
    namespaceName: UnityEngine.Tilemaps
    className: RuleOverrideTile
  - hash:
      serializedVersion: 2
      Hash: 6ddf94363c6ce4b02d9fdd51290cb0f9
    assemblyName: UnityEngine.TestRunner
    namespaceName: UnityEngine.TestRunner.Utils
    className: TestRunCallbackListener
  - hash:
      serializedVersion: 2
      Hash: 5a4aa2850fe11587f47e34c270c7fd22
    assemblyName: UnityEngine.UI
    namespaceName: UnityEngine.UI
    className: GraphicRaycaster
  - hash:
      serializedVersion: 2
      Hash: df72e2ce65946d4a1f96517bcf10c0f3
    assemblyName: Assembly-CSharp
    namespaceName: 
    className: InventoryItem
  - hash:
      serializedVersion: 2
      Hash: 3b5263a98bcc71d9a9d1d78a31b34ced
    assemblyName: Unity.TextMeshPro
    namespaceName: TMPro
    className: TMP_SelectionCaret
  - hash:
      serializedVersion: 2
      Hash: 8e6dc3d79712ca4934f6d50c80f010b1
    assemblyName: Unity.TextMeshPro
    namespaceName: TMPro
    className: TMP_SpriteAnimator
  - hash:
      serializedVersion: 2
      Hash: e23d2210701e34e6850023cabe554c1f
    assemblyName: Unity.VisualScripting.Core
    namespaceName: Unity.VisualScripting
    className: UnityOnInputFieldEndEditMessageListener
  - hash:
      serializedVersion: 2
      Hash: bf40c7a9dc15bc219687c9838aa9b41f
    assemblyName: UnityEngine.TestRunner
    namespaceName: UnityEngine.TestTools.TestRunner.Callbacks
    className: TestResultRendererCallback
  - hash:
      serializedVersion: 2
      Hash: aa325cf8525986b1f48a28230d9f514a
    assemblyName: Unity.RenderPipelines.Universal.Runtime
    namespaceName: UnityEngine.Rendering.Universal
    className: ShadowCaster2D
  - hash:
      serializedVersion: 2
      Hash: a1ac4c50e55874de16eee3db71aa9784
    assemblyName: UnityEngine.UI
    namespaceName: UnityEngine.UI
    className: ToggleGroup
  - hash:
      serializedVersion: 2
      Hash: 593a641cd7822a10b9a54aa9eb5c3d5e
    assemblyName: Assembly-CSharp
    namespaceName: TMPro.Examples
    className: Benchmark04
  - hash:
      serializedVersion: 2
      Hash: 7a0bde3ac6d739f92105cfe5613df2e0
    assemblyName: Unity.RenderPipelines.Universal.Runtime
    namespaceName: UnityEngine.Rendering.Universal
    className: UniversalAdditionalCameraData
  - hash:
      serializedVersion: 2
      Hash: d7c8092511063e0dae8106bfc8ed63dd
    assemblyName: Unity.VisualScripting.Core
    namespaceName: Unity.VisualScripting
    className: UnityOnScrollMessageListener
  - hash:
      serializedVersion: 2
      Hash: 27eb68f2313c3902309cd8f2f24bc200
    assemblyName: Assembly-CSharp
    namespaceName: 
    className: LegsController
  - hash:
      serializedVersion: 2
      Hash: 715f7928b39881049ce8c46350cb8681
    assemblyName: Unity.VisualScripting.Core
    namespaceName: Unity.VisualScripting
    className: UnityOnTransformParentChangedMessageListener
  - hash:
      serializedVersion: 2
      Hash: c6369b4cc3be620d6c9300e920bc3d52
    assemblyName: Unity.VisualScripting.Core
    namespaceName: Unity.VisualScripting
    className: VariablesAsset
  - hash:
      serializedVersion: 2
      Hash: 4b04d79da2c302eb6f2fab1cd448f9f2
    assemblyName: Assembly-CSharp
    namespaceName: 
    className: AnimationSmoother
  - hash:
      serializedVersion: 2
      Hash: b810dfd26c061161a980f9a956f9a116
    assemblyName: Unity.RenderPipelines.Universal.Runtime
    namespaceName: UnityEngine.Rendering.Universal
    className: ScreenSpaceShadows
  - hash:
      serializedVersion: 2
      Hash: 5b8b7230ad968399f6726aa1269dcdcb
    assemblyName: UnityEngine.TestRunner
    namespaceName: UnityEngine.TestTools.TestRunner.Callbacks
    className: PlayerQuitHandler
  - hash:
      serializedVersion: 2
      Hash: d3bece132a2b48e63d78047dece48ba7
    assemblyName: Assembly-CSharp
    namespaceName: 
    className: ChatController
  - hash:
      serializedVersion: 2
      Hash: cf32d98954e63b8e30e9334b152e9b8e
    assemblyName: UnityEngine.UI
    namespaceName: UnityEngine.EventSystems
    className: BaseInput
  - hash:
      serializedVersion: 2
      Hash: e6f8333cc8a800e2866870a5ef7efc35
    assemblyName: Unity.VisualScripting.Core
    namespaceName: Unity.VisualScripting
    className: UnityOnControllerColliderHitMessageListener
  - hash:
      serializedVersion: 2
      Hash: 10ffd5579f941e0f4946756d18f1a9df
    assemblyName: Unity.RenderPipelines.Core.Runtime
    namespaceName: UnityEngine.Rendering.UI
    className: DebugUIHandlerValueTuple
  - hash:
      serializedVersion: 2
      Hash: be60f316c7805f2fafe2115ca4c7dafc
    assemblyName: Unity.RenderPipelines.Universal.Runtime
    namespaceName: UnityEngine.Rendering.Universal
    className: ForwardRendererData
  - hash:
      serializedVersion: 2
      Hash: 5382156409d7c462c67822a5ee8d6c85
    assemblyName: Unity.RenderPipelines.Core.Runtime
    namespaceName: UnityEngine.Rendering.UI
    className: DebugUIHandlerToggleHistory
  - hash:
      serializedVersion: 2
      Hash: 665da785348a8806bf1524fe10742a60
    assemblyName: Assembly-CSharp
    namespaceName: TMPro.Examples
    className: TMP_UiFrameRateCounter
  - hash:
      serializedVersion: 2
      Hash: b747f5d40674055dd70e18552f1b0447
    assemblyName: UnityEngine.UI
    namespaceName: UnityEngine.UI
    className: Mask
  - hash:
      serializedVersion: 2
      Hash: 9b2ea8216cb0170c94e36714d841ceb6
    assemblyName: Assembly-CSharp
    namespaceName: 
    className: SpaceStationGenerator
  - hash:
      serializedVersion: 2
      Hash: 5dc51b0140815ee83ba613dce32f5b12
    assemblyName: Unity.RenderPipelines.Core.Runtime
    namespaceName: UnityEngine.Rendering.UI
    className: DebugUIHandlerPersistentCanvas
  - hash:
      serializedVersion: 2
      Hash: 4718916586b3818eb69bf65df61f0f3a
    assemblyName: Unity.VisualScripting.Core
    namespaceName: Unity.VisualScripting
    className: UnityOnCollisionStay2DMessageListener
  - hash:
      serializedVersion: 2
      Hash: 185e7bacfbbc5defefa609c70f7ff4ce
    assemblyName: Unity.TextMeshPro
    namespaceName: TMPro
    className: TMP_ColorGradient
  - hash:
      serializedVersion: 2
      Hash: 9dc274eab7228631e90d4757b13b768a
    assemblyName: Unity.RenderPipelines.Universal.Runtime
    namespaceName: UnityEngine.Rendering.Universal
    className: ShadowsMidtonesHighlights
  - hash:
      serializedVersion: 2
      Hash: c78ed13f6b5a632d70d4f73f56ad81f2
    assemblyName: Unity.VisualScripting.Core
    namespaceName: Unity.VisualScripting
    className: VariablesSaver
  - hash:
      serializedVersion: 2
      Hash: 7053e305cdc1eb560f4c3be49d3b5136
    assemblyName: UnityEngine.UI
    namespaceName: UnityEngine.EventSystems
    className: PhysicsRaycaster
  - hash:
      serializedVersion: 2
      Hash: 042785b832afb73fd0585521aa7baf43
    assemblyName: Unity.Timeline
    namespaceName: UnityEngine.Timeline
    className: ControlPlayableAsset
  - hash:
      serializedVersion: 2
      Hash: 1a680d9a08de79b0d2c779e78c9843b4
    assemblyName: UnityEngine.UI
    namespaceName: UnityEngine.UI
    className: RectMask2D
  - hash:
      serializedVersion: 2
      Hash: 4ee1e5f285c826248e282a4df1635b19
    assemblyName: Unity.RenderPipelines.Universal.Runtime
    namespaceName: UnityEngine.Rendering.Universal
    className: ChromaticAberration
  - hash:
      serializedVersion: 2
      Hash: 9ebc0a3016506a0cbb7e0306dfcb9497
    assemblyName: Unity.VisualScripting.Core
    namespaceName: Unity.VisualScripting
    className: DictionaryAsset
  - hash:
      serializedVersion: 2
      Hash: 3594d8e9fcbd74e51eec80efbf16c599
    assemblyName: Unity.RenderPipelines.Universal.Runtime
    namespaceName: UnityEngine.Rendering.Universal
    className: LensDistortion
  - hash:
      serializedVersion: 2
      Hash: 7d9543c1f1322bfae34b044b62676a76
    assemblyName: Unity.RenderPipelines.Universal.Runtime
    namespaceName: UnityEngine.Rendering.Universal
    className: Renderer2DData
  - hash:
      serializedVersion: 2
      Hash: e7e95903ca66d58e1a7cbd71f824e16d
    assemblyName: Unity.TextMeshPro
    namespaceName: TMPro
    className: TMP_SubMeshUI
  - hash:
      serializedVersion: 2
      Hash: 0f6b6107168e347e854c0107aa8cb9fb
    assemblyName: Unity.RenderPipelines.Core.Runtime
    namespaceName: UnityEngine.Rendering.UI
    className: DebugUIHandlerEnumHistory
  - hash:
      serializedVersion: 2
      Hash: 9078325c2a6f6a2d9612ade897511860
    assemblyName: Unity.TextMeshPro
    namespaceName: TMPro
    className: TMP_SpriteAsset
  - hash:
      serializedVersion: 2
      Hash: 05afd034bf7a074278a40ad9e97d8db4
    assemblyName: Unity.RenderPipelines.Universal.Runtime
    namespaceName: UnityEngine.Rendering.Universal
    className: Vignette
  - hash:
      serializedVersion: 2
      Hash: 5a1710e1ce9ddf5eb06e0d2693171f49
    assemblyName: Assembly-CSharp
    namespaceName: TMPro.Examples
    className: ObjectSpin
  - hash:
      serializedVersion: 2
      Hash: 0b63f6d3335e11939ce8790b904c8691
    assemblyName: Unity.Timeline
    namespaceName: UnityEngine.Timeline
    className: ActivationPlayableAsset
  - hash:
      serializedVersion: 2
      Hash: e402a382f213255c22bda9254dd831b3
    assemblyName: Unity.VisualScripting.Core
    namespaceName: Unity.VisualScripting
    className: SceneVariables
  - hash:
      serializedVersion: 2
      Hash: ceeb6c51b89f022a3deb7b3408c66745
    assemblyName: Assembly-CSharp
    namespaceName: TMPro.Examples
    className: TMP_TextEventCheck
  - hash:
      serializedVersion: 2
      Hash: f2098fcd8ee983a05ec192f63904298f
    assemblyName: Unity.VisualScripting.Core
    namespaceName: Unity.VisualScripting
    className: UnityOnEndDragMessageListener
  - hash:
      serializedVersion: 2
      Hash: 51b80d3734ea86b38e3101db98029e15
    assemblyName: Unity.Timeline
    namespaceName: UnityEngine.Timeline
    className: AnimationPlayableAsset
  - hash:
      serializedVersion: 2
      Hash: 47ee34eb8b6021efa0289dbbb17a5a85
    assemblyName: Unity.TextMeshPro
    namespaceName: TMPro
    className: TMP_InputField
  - hash:
      serializedVersion: 2
      Hash: ec6cf673540b2d4e42c612611eeafe4d
    assemblyName: Unity.VisualScripting.Core
    namespaceName: Unity.VisualScripting
    className: UnityOnTriggerStayMessageListener
  - hash:
      serializedVersion: 2
      Hash: ab3d19695db8289414a06e6d45fd6c8e
    assemblyName: Assembly-CSharp
    namespaceName: TMPro.Examples
    className: ShaderPropAnimator
  - hash:
      serializedVersion: 2
      Hash: c058ff8faaff3e0da63b5433ebfb40bc
    assemblyName: Unity.RenderPipelines.Universal.Runtime
    namespaceName: UnityEngine.Rendering.Universal
    className: Tonemapping
  - hash:
      serializedVersion: 2
      Hash: c27554f9feec0ec62923b4c200a63b9e
    assemblyName: Unity.2D.IK.Runtime
    namespaceName: UnityEngine.U2D.IK
    className: CCDSolver2D
  - hash:
      serializedVersion: 2
      Hash: 8c3b6e24ca691fc0bf819c86e6b91dff
    assemblyName: Unity.RenderPipelines.Universal.Runtime
    namespaceName: UnityEngine.Rendering.Universal
    className: Light2D
  - hash:
      serializedVersion: 2
      Hash: 522f6865b4ec12522e602206785309f8
    assemblyName: Unity.VisualScripting.Core
    namespaceName: Unity.VisualScripting
    className: GlobalMessageListener
  - hash:
      serializedVersion: 2
      Hash: 53e329840e6d03aebec76623c3b054db
    assemblyName: UnityEngine.UI
    namespaceName: UnityEngine.UI
    className: Slider
  - hash:
      serializedVersion: 2
      Hash: 7d026ae582dc02c5edc3fc23b8c03ff7
    assemblyName: Unity.RenderPipelines.Core.Runtime
    namespaceName: UnityEngine.Rendering.UI
    className: DebugUIHandlerVector3
  - hash:
      serializedVersion: 2
      Hash: 0c0b64d103fe555d830e8ca206abd12a
    assemblyName: Unity.VisualScripting.Core
    namespaceName: Unity.VisualScripting
    className: UnityOnTriggerExitMessageListener
  - hash:
      serializedVersion: 2
      Hash: 1981decccddfde3e97fcfd6689332a6f
    assemblyName: Assembly-CSharp
    namespaceName: TMPro.Examples
    className: WarpTextExample
  - hash:
      serializedVersion: 2
      Hash: 17f82e3e7d8f8b07a270a4aad9bfe79d
    assemblyName: Unity.TextMeshPro
    namespaceName: TMPro
    className: TMP_FontAsset
  - hash:
      serializedVersion: 2
      Hash: 3e21c4fc1790539a1a5e53c6dae8099a
    assemblyName: Unity.RenderPipelines.Universal.Runtime
    namespaceName: UnityEngine.Rendering.Universal
    className: ChannelMixer
  - hash:
      serializedVersion: 2
      Hash: d82086405f46de14829f6b4bcbdd8fc9
    assemblyName: Unity.VisualScripting.Core
    namespaceName: Unity.VisualScripting
    className: UnityOnBecameInvisibleMessageListener
  - hash:
      serializedVersion: 2
      Hash: 953fad5c5ab1e7bb889420c434db93fe
    assemblyName: Unity.RenderPipelines.Core.Runtime
    namespaceName: UnityEngine.Rendering.UI
    className: DebugUIHandlerFloatField
  - hash:
      serializedVersion: 2
      Hash: 41c774bc80b2c66dfea092b2134f4673
    assemblyName: Unity.2D.Animation.Runtime
    namespaceName: UnityEngine.U2D.Animation
    className: SpriteSkinUpdateHelper
  - hash:
      serializedVersion: 2
      Hash: 1ce63d97d1ca25cba2d91d5f32fd5f79
    assemblyName: Unity.VisualScripting.State
    namespaceName: Unity.VisualScripting
    className: StateGraphAsset
  - hash:
      serializedVersion: 2
      Hash: 049f6aa84fe1cb5359c9338e96d0b07e
    assemblyName: Unity.RenderPipelines.Core.Runtime
    namespaceName: UnityEngine.Rendering.UI
    className: UIFoldout
  - hash:
      serializedVersion: 2
      Hash: 35e0b31791c377513b6fc7b9568c50cd
    assemblyName: Assembly-CSharp
    namespaceName: TMPro.Examples
    className: Benchmark02
  - hash:
      serializedVersion: 2
      Hash: 1319c5c67c87e0ab9b82c76ccfe7d90d
    assemblyName: Assembly-CSharp
    namespaceName: 
    className: ItemData
  - hash:
      serializedVersion: 2
      Hash: 33656f83bb0079184b2206a69690ec46
    assemblyName: UnityEngine.UI
    namespaceName: UnityEngine.UI
    className: AspectRatioFitter
  - hash:
      serializedVersion: 2
      Hash: b72839eb6e4350778dc879d98832aa2b
    assemblyName: Unity.RenderPipelines.Core.Runtime
    namespaceName: UnityEngine.Rendering.UI
    className: DebugUIHandlerHBox
  - hash:
      serializedVersion: 2
      Hash: da5a949a0bbd2ff91bc51c5805c2c41f
    assemblyName: Unity.VisualScripting.Core
    namespaceName: Unity.VisualScripting
    className: UnityOnMouseUpAsButtonMessageListener
  - hash:
      serializedVersion: 2
      Hash: ef19274111491b6b8bcc6fd227f656f5
    assemblyName: Unity.RenderPipelines.Core.Runtime
    namespaceName: UnityEngine.Rendering.UI
    className: DebugUIHandlerIndirectFloatField
  - hash:
      serializedVersion: 2
      Hash: fc0a9a681d29388386a9abf3f08b024a
    assemblyName: Assembly-CSharp
    namespaceName: TMPro.Examples
    className: Benchmark03
  - hash:
      serializedVersion: 2
      Hash: 9104447b683bf70406fa01f12ecb564a
    assemblyName: UnityEngine.UI
    namespaceName: UnityEngine.EventSystems
    className: StandaloneInputModule
  - hash:
      serializedVersion: 2
      Hash: a90f6a00b23520788be7d89ee3b0773d
    assemblyName: UnityEngine.UI
    namespaceName: UnityEngine.UI
    className: Scrollbar
  - hash:
      serializedVersion: 2
      Hash: febcfbf0a0d85e37808a3740e3c2c6fa
    assemblyName: Unity.Timeline
    namespaceName: UnityEngine.Timeline
    className: MarkerTrack
  - hash:
      serializedVersion: 2
      Hash: ea5e0cb2f62c5bafc3dfb7bcdc9595e1
    assemblyName: Unity.RenderPipelines.Core.Runtime
    namespaceName: UnityEngine.Rendering
    className: DebugUpdater
  - hash:
      serializedVersion: 2
      Hash: a908b098f2284663c0b9130e88844772
    assemblyName: Unity.RenderPipelines.Core.Runtime
    namespaceName: UnityEngine.Rendering.UI
    className: DebugUIHandlerCanvas
  - hash:
      serializedVersion: 2
      Hash: 90a7558b5fd1c85e1adf7cbcae55cc8a
    assemblyName: Assembly-CSharp
    namespaceName: 
    className: Hotbar
  - hash:
      serializedVersion: 2
      Hash: 8bbce9932f67ad8e7f4eefec87800f94
    assemblyName: Unity.TextMeshPro
    namespaceName: TMPro
    className: TextContainer
  - hash:
      serializedVersion: 2
      Hash: 67ba98f8a8977f3e60e5f0e049923033
    assemblyName: Unity.RenderPipelines.Core.Runtime
    namespaceName: UnityEngine.Rendering.UI
    className: DebugUIHandlerColor
  - hash:
      serializedVersion: 2
      Hash: 7fdd3ee28e9042c3ec83463d0bb3b230
    assemblyName: Assembly-CSharp
    namespaceName: TMPro.Examples
    className: TMP_FrameRateCounter
  - hash:
      serializedVersion: 2
      Hash: f73e3a97805e1e671360c632c4565101
    assemblyName: Unity.RenderPipelines.Universal.Runtime
    namespaceName: UnityEngine.Rendering.Universal
    className: FilmGrain
  - hash:
      serializedVersion: 2
      Hash: a119221a1c3d590645a52df2cbebb41c
    assemblyName: Unity.2D.Tilemap.Extras
    namespaceName: UnityEngine
    className: HexagonalRuleTile
  - hash:
      serializedVersion: 2
      Hash: 17142b03663387290480c82303274c02
    assemblyName: Unity.VisualScripting.Core
    namespaceName: Unity.VisualScripting
    className: UnityOnPointerEnterMessageListener
  - hash:
      serializedVersion: 2
      Hash: f2305da2400dfe6f069770a1a63f0f7c
    assemblyName: Unity.Timeline
    namespaceName: UnityEngine.Timeline
    className: PlayableTrack
  - hash:
      serializedVersion: 2
      Hash: 4392bfff79efe0630144f16ea4d656ab
    assemblyName: Unity.RenderPipelines.Core.Runtime
    namespaceName: UnityEngine.Rendering.UI
    className: DebugUIHandlerObjectList
  - hash:
      serializedVersion: 2
      Hash: c4b85a67513b4f0cf7b5fb2d5ded04b0
    assemblyName: Unity.VisualScripting.Core
    namespaceName: Unity.VisualScripting
    className: UnityOnCollisionExitMessageListener
  - hash:
      serializedVersion: 2
      Hash: bfd4af30d9804c73a547e494b337b534
    assemblyName: Unity.2D.Tilemap.Extras
    namespaceName: UnityEngine
    className: RuleTile
  - hash:
      serializedVersion: 2
      Hash: 47bf9923c97381cd80cbf20f709da987
    assemblyName: Assembly-CSharp
    namespaceName: 
    className: BuiltInOutline
  - hash:
      serializedVersion: 2
      Hash: 7bb32d6e4c7614a97d9723ce7e26588c
    assemblyName: Unity.Timeline
    namespaceName: UnityEngine.Timeline
    className: BasicPlayableBehaviour
  - hash:
      serializedVersion: 2
      Hash: 19824dd35debb3fff0983df49f8359ab
    assemblyName: UnityEngine.TestRunner
    namespaceName: UnityEngine.TestTools.TestRunner.Callbacks
    className: PlayModeRunnerCallback
  - hash:
      serializedVersion: 2
      Hash: 6a0b5b415c00f783202fd1ae2d556cb2
    assemblyName: Unity.Timeline
    namespaceName: UnityEngine.Timeline
    className: AudioPlayableAsset
  - hash:
      serializedVersion: 2
      Hash: b14e186823458f549289789cccbb77ce
    assemblyName: Unity.VisualScripting.Core
    namespaceName: Unity.VisualScripting
    className: UnityOnPointerDownMessageListener
  - hash:
      serializedVersion: 2
      Hash: ee34255e3e86289bde9bad5e07b84cbc
    assemblyName: Unity.VisualScripting.Core
    namespaceName: Unity.VisualScripting
    className: UnityOnScrollRectValueChangedMessageListener
  - hash:
      serializedVersion: 2
      Hash: 830c3b545f6e6ee60a4cebab90516f2c
    assemblyName: UnityEngine.TestRunner
    namespaceName: UnityEngine.TestTools.TestRunner.Callbacks
    className: RemoteTestResultSender
  - hash:
      serializedVersion: 2
      Hash: d0cdd713f9bf4e6a625d9e7804deca42
    assemblyName: UnityEngine.UI
    namespaceName: UnityEngine.EventSystems
    className: EventTrigger
  - hash:
      serializedVersion: 2
      Hash: e2734bcc7b3e94776ade5d59f54fec1e
    assemblyName: Assembly-CSharp
    namespaceName: TMPro.Examples
    className: TMP_TextSelector_B
  - hash:
      serializedVersion: 2
      Hash: fd7983aadddd0b1b9f4ce82ed537bde5
    assemblyName: Unity.2D.SpriteShape.Runtime
    namespaceName: UnityEngine.U2D
    className: SpriteShape
  - hash:
      serializedVersion: 2
      Hash: 67272c9c30c50b68ef1ce72ac0191c57
    assemblyName: Unity.VisualScripting.Core
    namespaceName: Unity.VisualScripting
    className: UnityOnMoveMessageListener
  - hash:
      serializedVersion: 2
      Hash: 6dfbdb53722d39d4e316ef33be90973c
    assemblyName: Unity.RenderPipelines.Universal.Runtime
    namespaceName: UnityEngine.Rendering.Universal
    className: PostProcessData
  - hash:
      serializedVersion: 2
      Hash: 2ce798ba5d17e65353f80be596cb5bc4
    assemblyName: Assembly-CSharp
    namespaceName: 
    className: Inventory
  - hash:
      serializedVersion: 2
      Hash: b3eb5d73cd07eeee371bb1b136d026d6
    assemblyName: Unity.RenderPipelines.Universal.Runtime
    namespaceName: 
    className: FullScreenPassRendererFeature
  - hash:
      serializedVersion: 2
      Hash: a86f3bf4aebc96b1b3a4994832df1fc9
    assemblyName: Assembly-CSharp
    namespaceName: 
    className: PlayerController
  - hash:
      serializedVersion: 2
      Hash: 01727a040aa965d7fae41c3c1e1d9ee5
    assemblyName: Unity.RenderPipelines.Universal.Runtime
    namespaceName: UnityEngine.Rendering.Universal
    className: PaniniProjection
  - hash:
      serializedVersion: 2
      Hash: 47d50f529890b9237021f88334dc1cf6
    assemblyName: Unity.2D.Animation.Runtime
    namespaceName: UnityEngine.U2D.Animation
    className: SpriteLibraryAsset
  - hash:
      serializedVersion: 2
      Hash: 92813e53e5f1c6ebcc014c0f1b3a2139
    assemblyName: Assembly-CSharp
    namespaceName: 
    className: CameraFollow
  - hash:
      serializedVersion: 2
      Hash: 568292fb7ea6c4f7a667e5ff76bc7e85
    assemblyName: Unity.VisualScripting.Core
    namespaceName: Unity.VisualScripting
    className: UnityOnMouseUpMessageListener
  - hash:
      serializedVersion: 2
      Hash: 9fb1c3ab48b256d508ecddd967f98652
    assemblyName: Assembly-CSharp
    namespaceName: TMPro.Examples
    className: VertexZoom
  - hash:
      serializedVersion: 2
      Hash: 711ba963e2b245a25de71beeaeeeb658
    assemblyName: Unity.RenderPipelines.Core.Runtime
    namespaceName: UnityEngine.Rendering.UI
    className: DebugUIHandlerFoldout
  - hash:
      serializedVersion: 2
      Hash: 1f1ee5563d20d7a4ad267bd598c2071c
    assemblyName: Unity.2D.Animation.Runtime
    namespaceName: UnityEngine.U2D.Animation
    className: SpriteSkinComposite
  - hash:
      serializedVersion: 2
      Hash: 00eef92bf387da2c9009267123d9d674
    assemblyName: Unity.VisualScripting.Core
    namespaceName: Unity.VisualScripting
    className: UnityOnTriggerExit2DMessageListener
  - hash:
      serializedVersion: 2
      Hash: 8b15e35eb98fc258ec2e839df7c3c9b0
    assemblyName: Unity.VisualScripting.Core
    namespaceName: Unity.VisualScripting
    className: UnityOnSliderValueChangedMessageListener
  - hash:
      serializedVersion: 2
      Hash: 93d0585e7ef8cc047a6138d8a774b158
    assemblyName: Assembly-CSharp
    namespaceName: 
    className: WeaponShootingSystem
  - hash:
      serializedVersion: 2
      Hash: 5ad414847a5deffb6055122b01c28bd6
    assemblyName: Assembly-CSharp
    namespaceName: 
    className: BulletSpawner
  - hash:
      serializedVersion: 2
      Hash: f73bbb39684d47cbf13551f3d7151d73
    assemblyName: Assembly-CSharp
    namespaceName: 
    className: Bullet
  platform: 19
  scenePathNames:
  - Assets/Scenes/PlayerScene.unity
  playerPath: C:/Users/<USER>/Desktop/Computer/Cascade Games/Void Voyager/Build/Void
    Voyager.exe
