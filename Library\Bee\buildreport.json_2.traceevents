{ "pid": 36728, "tid": 60129542144, "ph": "M", "name": "thread_name", "args": { "name": "ReadEntireBinlogFromIpcAsync" } },
{ "pid": 36728, "tid": 60129542144, "ts": 1749474197714199, "dur": 15670, "ph": "X", "name": "WaitForConnectionAsync", "args": {} },
{ "pid": 36728, "tid": 60129542144, "ts": 1749474197729871, "dur": 42581, "ph": "X", "name": "UpdateFromStreamAsync", "args": {} },
{ "pid": 36728, "tid": 60129542144, "ts": 1749474197729880, "dur": 28, "ph": "X", "name": "ReadAsync 0", "args": {} },
{ "pid": 36728, "tid": 60129542144, "ts": 1749474197729911, "dur": 24828, "ph": "X", "name": "ReadAs<PERSON> 4", "args": {} },
{ "pid": 36728, "tid": 60129542144, "ts": 1749474197754747, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {} },
{ "pid": 36728, "tid": 60129542144, "ts": 1749474197754750, "dur": 97, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 36728, "tid": 60129542144, "ts": 1749474197754849, "dur": 5, "ph": "X", "name": "ProcessMessages 47", "args": {} },
{ "pid": 36728, "tid": 60129542144, "ts": 1749474197754855, "dur": 1548, "ph": "X", "name": "ReadAsync 47", "args": {} },
{ "pid": 36728, "tid": 60129542144, "ts": 1749474197756407, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {} },
{ "pid": 36728, "tid": 60129542144, "ts": 1749474197756409, "dur": 90, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 36728, "tid": 60129542144, "ts": 1749474197756503, "dur": 1, "ph": "X", "name": "ProcessMessages 1263", "args": {} },
{ "pid": 36728, "tid": 60129542144, "ts": 1749474197756505, "dur": 111, "ph": "X", "name": "ReadAsync 1263", "args": {} },
{ "pid": 36728, "tid": 60129542144, "ts": 1749474197756620, "dur": 2, "ph": "X", "name": "ProcessMessages 2142", "args": {} },
{ "pid": 36728, "tid": 60129542144, "ts": 1749474197756623, "dur": 112, "ph": "X", "name": "ReadAsync 2142", "args": {} },
{ "pid": 36728, "tid": 60129542144, "ts": 1749474197756736, "dur": 1, "ph": "X", "name": "ProcessMessages 1564", "args": {} },
{ "pid": 36728, "tid": 60129542144, "ts": 1749474197756739, "dur": 44, "ph": "X", "name": "ReadAsync 1564", "args": {} },
{ "pid": 36728, "tid": 60129542144, "ts": 1749474197756784, "dur": 1, "ph": "X", "name": "ProcessMessages 3839", "args": {} },
{ "pid": 36728, "tid": 60129542144, "ts": 1749474197756786, "dur": 81, "ph": "X", "name": "ReadAsync 3839", "args": {} },
{ "pid": 36728, "tid": 60129542144, "ts": 1749474197756873, "dur": 36, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 36728, "tid": 60129542144, "ts": 1749474197756912, "dur": 1, "ph": "X", "name": "ProcessMessages 1126", "args": {} },
{ "pid": 36728, "tid": 60129542144, "ts": 1749474197756914, "dur": 22, "ph": "X", "name": "ReadAsync 1126", "args": {} },
{ "pid": 36728, "tid": 60129542144, "ts": 1749474197756938, "dur": 22, "ph": "X", "name": "ReadAsync 388", "args": {} },
{ "pid": 36728, "tid": 60129542144, "ts": 1749474197756963, "dur": 21, "ph": "X", "name": "ReadAsync 362", "args": {} },
{ "pid": 36728, "tid": 60129542144, "ts": 1749474197756986, "dur": 21, "ph": "X", "name": "ReadAsync 320", "args": {} },
{ "pid": 36728, "tid": 60129542144, "ts": 1749474197757010, "dur": 27, "ph": "X", "name": "ReadAsync 809", "args": {} },
{ "pid": 36728, "tid": 60129542144, "ts": 1749474197757040, "dur": 26, "ph": "X", "name": "ReadAsync 780", "args": {} },
{ "pid": 36728, "tid": 60129542144, "ts": 1749474197757069, "dur": 1, "ph": "X", "name": "ProcessMessages 1199", "args": {} },
{ "pid": 36728, "tid": 60129542144, "ts": 1749474197757071, "dur": 85, "ph": "X", "name": "ReadAsync 1199", "args": {} },
{ "pid": 36728, "tid": 60129542144, "ts": 1749474197757159, "dur": 1, "ph": "X", "name": "ProcessMessages 1461", "args": {} },
{ "pid": 36728, "tid": 60129542144, "ts": 1749474197757161, "dur": 43, "ph": "X", "name": "ReadAsync 1461", "args": {} },
{ "pid": 36728, "tid": 60129542144, "ts": 1749474197757206, "dur": 1, "ph": "X", "name": "ProcessMessages 2876", "args": {} },
{ "pid": 36728, "tid": 60129542144, "ts": 1749474197757208, "dur": 142, "ph": "X", "name": "ReadAsync 2876", "args": {} },
{ "pid": 36728, "tid": 60129542144, "ts": 1749474197757354, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {} },
{ "pid": 36728, "tid": 60129542144, "ts": 1749474197757356, "dur": 50, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 36728, "tid": 60129542144, "ts": 1749474197757408, "dur": 2, "ph": "X", "name": "ProcessMessages 2599", "args": {} },
{ "pid": 36728, "tid": 60129542144, "ts": 1749474197757411, "dur": 21, "ph": "X", "name": "ReadAsync 2599", "args": {} },
{ "pid": 36728, "tid": 60129542144, "ts": 1749474197757434, "dur": 8, "ph": "X", "name": "ProcessMessages 468", "args": {} },
{ "pid": 36728, "tid": 60129542144, "ts": 1749474197757443, "dur": 31, "ph": "X", "name": "ReadAsync 468", "args": {} },
{ "pid": 36728, "tid": 60129542144, "ts": 1749474197757477, "dur": 28, "ph": "X", "name": "ReadAsync 1512", "args": {} },
{ "pid": 36728, "tid": 60129542144, "ts": 1749474197757506, "dur": 1, "ph": "X", "name": "ProcessMessages 68", "args": {} },
{ "pid": 36728, "tid": 60129542144, "ts": 1749474197757508, "dur": 21, "ph": "X", "name": "ReadAsync 68", "args": {} },
{ "pid": 36728, "tid": 60129542144, "ts": 1749474197757533, "dur": 31, "ph": "X", "name": "ReadAsync 378", "args": {} },
{ "pid": 36728, "tid": 60129542144, "ts": 1749474197757568, "dur": 101, "ph": "X", "name": "ReadAsync 928", "args": {} },
{ "pid": 36728, "tid": 60129542144, "ts": 1749474197757671, "dur": 40, "ph": "X", "name": "ReadAsync 1381", "args": {} },
{ "pid": 36728, "tid": 60129542144, "ts": 1749474197757713, "dur": 2, "ph": "X", "name": "ProcessMessages 4317", "args": {} },
{ "pid": 36728, "tid": 60129542144, "ts": 1749474197757716, "dur": 19, "ph": "X", "name": "ReadAsync 4317", "args": {} },
{ "pid": 36728, "tid": 60129542144, "ts": 1749474197757738, "dur": 20, "ph": "X", "name": "ReadAsync 436", "args": {} },
{ "pid": 36728, "tid": 60129542144, "ts": 1749474197757760, "dur": 29, "ph": "X", "name": "ReadAsync 370", "args": {} },
{ "pid": 36728, "tid": 60129542144, "ts": 1749474197757792, "dur": 20, "ph": "X", "name": "ReadAsync 1081", "args": {} },
{ "pid": 36728, "tid": 60129542144, "ts": 1749474197757814, "dur": 38, "ph": "X", "name": "ReadAsync 970", "args": {} },
{ "pid": 36728, "tid": 60129542144, "ts": 1749474197757854, "dur": 21, "ph": "X", "name": "ReadAsync 797", "args": {} },
{ "pid": 36728, "tid": 60129542144, "ts": 1749474197757878, "dur": 31, "ph": "X", "name": "ReadAsync 943", "args": {} },
{ "pid": 36728, "tid": 60129542144, "ts": 1749474197757912, "dur": 20, "ph": "X", "name": "ReadAsync 1210", "args": {} },
{ "pid": 36728, "tid": 60129542144, "ts": 1749474197757935, "dur": 28, "ph": "X", "name": "ReadAsync 775", "args": {} },
{ "pid": 36728, "tid": 60129542144, "ts": 1749474197757966, "dur": 35, "ph": "X", "name": "ReadAsync 512", "args": {} },
{ "pid": 36728, "tid": 60129542144, "ts": 1749474197758003, "dur": 26, "ph": "X", "name": "ReadAsync 543", "args": {} },
{ "pid": 36728, "tid": 60129542144, "ts": 1749474197758031, "dur": 23, "ph": "X", "name": "ReadAsync 1206", "args": {} },
{ "pid": 36728, "tid": 60129542144, "ts": 1749474197758057, "dur": 19, "ph": "X", "name": "ReadAsync 1039", "args": {} },
{ "pid": 36728, "tid": 60129542144, "ts": 1749474197758079, "dur": 18, "ph": "X", "name": "ReadAsync 849", "args": {} },
{ "pid": 36728, "tid": 60129542144, "ts": 1749474197758100, "dur": 21, "ph": "X", "name": "ReadAsync 588", "args": {} },
{ "pid": 36728, "tid": 60129542144, "ts": 1749474197758123, "dur": 23, "ph": "X", "name": "ReadAsync 824", "args": {} },
{ "pid": 36728, "tid": 60129542144, "ts": 1749474197758149, "dur": 31, "ph": "X", "name": "ReadAsync 796", "args": {} },
{ "pid": 36728, "tid": 60129542144, "ts": 1749474197758182, "dur": 21, "ph": "X", "name": "ReadAsync 1126", "args": {} },
{ "pid": 36728, "tid": 60129542144, "ts": 1749474197758206, "dur": 40, "ph": "X", "name": "ReadAsync 1016", "args": {} },
{ "pid": 36728, "tid": 60129542144, "ts": 1749474197758250, "dur": 16, "ph": "X", "name": "ReadAsync 1110", "args": {} },
{ "pid": 36728, "tid": 60129542144, "ts": 1749474197758268, "dur": 25, "ph": "X", "name": "ReadAsync 159", "args": {} },
{ "pid": 36728, "tid": 60129542144, "ts": 1749474197758296, "dur": 20, "ph": "X", "name": "ReadAsync 921", "args": {} },
{ "pid": 36728, "tid": 60129542144, "ts": 1749474197758319, "dur": 27, "ph": "X", "name": "ReadAsync 748", "args": {} },
{ "pid": 36728, "tid": 60129542144, "ts": 1749474197758348, "dur": 21, "ph": "X", "name": "ReadAsync 999", "args": {} },
{ "pid": 36728, "tid": 60129542144, "ts": 1749474197758372, "dur": 20, "ph": "X", "name": "ReadAsync 920", "args": {} },
{ "pid": 36728, "tid": 60129542144, "ts": 1749474197758395, "dur": 21, "ph": "X", "name": "ReadAsync 544", "args": {} },
{ "pid": 36728, "tid": 60129542144, "ts": 1749474197758418, "dur": 20, "ph": "X", "name": "ReadAsync 691", "args": {} },
{ "pid": 36728, "tid": 60129542144, "ts": 1749474197758441, "dur": 19, "ph": "X", "name": "ReadAsync 686", "args": {} },
{ "pid": 36728, "tid": 60129542144, "ts": 1749474197758462, "dur": 122, "ph": "X", "name": "ReadAsync 764", "args": {} },
{ "pid": 36728, "tid": 60129542144, "ts": 1749474197758586, "dur": 1, "ph": "X", "name": "ProcessMessages 3648", "args": {} },
{ "pid": 36728, "tid": 60129542144, "ts": 1749474197758588, "dur": 19, "ph": "X", "name": "ReadAsync 3648", "args": {} },
{ "pid": 36728, "tid": 60129542144, "ts": 1749474197758610, "dur": 22, "ph": "X", "name": "ReadAsync 528", "args": {} },
{ "pid": 36728, "tid": 60129542144, "ts": 1749474197758635, "dur": 22, "ph": "X", "name": "ReadAsync 591", "args": {} },
{ "pid": 36728, "tid": 60129542144, "ts": 1749474197758660, "dur": 18, "ph": "X", "name": "ReadAsync 837", "args": {} },
{ "pid": 36728, "tid": 60129542144, "ts": 1749474197758680, "dur": 4461, "ph": "X", "name": "ReadAsync 448", "args": {} },
{ "pid": 36728, "tid": 60129542144, "ts": 1749474197763149, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {} },
{ "pid": 36728, "tid": 60129542144, "ts": 1749474197763151, "dur": 38, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 36728, "tid": 60129542144, "ts": 1749474197763194, "dur": 1, "ph": "X", "name": "ProcessMessages 36", "args": {} },
{ "pid": 36728, "tid": 60129542144, "ts": 1749474197763195, "dur": 63, "ph": "X", "name": "ReadAsync 36", "args": {} },
{ "pid": 36728, "tid": 60129542144, "ts": 1749474197763266, "dur": 2, "ph": "X", "name": "ProcessMessages 48", "args": {} },
{ "pid": 36728, "tid": 60129542144, "ts": 1749474197763271, "dur": 33, "ph": "X", "name": "ReadAsync 48", "args": {} },
{ "pid": 36728, "tid": 60129542144, "ts": 1749474197763306, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {} },
{ "pid": 36728, "tid": 60129542144, "ts": 1749474197763309, "dur": 24, "ph": "X", "name": "ReadAsync 64", "args": {} },
{ "pid": 36728, "tid": 60129542144, "ts": 1749474197763337, "dur": 50, "ph": "X", "name": "ReadAsync 16", "args": {} },
{ "pid": 36728, "tid": 60129542144, "ts": 1749474197763393, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {} },
{ "pid": 36728, "tid": 60129542144, "ts": 1749474197763397, "dur": 86, "ph": "X", "name": "ReadAsync 32", "args": {} },
{ "pid": 36728, "tid": 60129542144, "ts": 1749474197763489, "dur": 3, "ph": "X", "name": "ProcessMessages 144", "args": {} },
{ "pid": 36728, "tid": 60129542144, "ts": 1749474197763494, "dur": 75, "ph": "X", "name": "ReadAsync 144", "args": {} },
{ "pid": 36728, "tid": 60129542144, "ts": 1749474197763576, "dur": 4, "ph": "X", "name": "ProcessMessages 192", "args": {} },
{ "pid": 36728, "tid": 60129542144, "ts": 1749474197763582, "dur": 48, "ph": "X", "name": "ReadAsync 192", "args": {} },
{ "pid": 36728, "tid": 60129542144, "ts": 1749474197763632, "dur": 1, "ph": "X", "name": "ProcessMessages 208", "args": {} },
{ "pid": 36728, "tid": 60129542144, "ts": 1749474197763635, "dur": 24, "ph": "X", "name": "ReadAsync 208", "args": {} },
{ "pid": 36728, "tid": 60129542144, "ts": 1749474197763663, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {} },
{ "pid": 36728, "tid": 60129542144, "ts": 1749474197763667, "dur": 74, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 36728, "tid": 60129542144, "ts": 1749474197763750, "dur": 3, "ph": "X", "name": "ProcessMessages 116", "args": {} },
{ "pid": 36728, "tid": 60129542144, "ts": 1749474197763755, "dur": 44, "ph": "X", "name": "ReadAsync 116", "args": {} },
{ "pid": 36728, "tid": 60129542144, "ts": 1749474197763800, "dur": 1, "ph": "X", "name": "ProcessMessages 252", "args": {} },
{ "pid": 36728, "tid": 60129542144, "ts": 1749474197763803, "dur": 38, "ph": "X", "name": "ReadAsync 252", "args": {} },
{ "pid": 36728, "tid": 60129542144, "ts": 1749474197763848, "dur": 1, "ph": "X", "name": "ProcessMessages 132", "args": {} },
{ "pid": 36728, "tid": 60129542144, "ts": 1749474197763850, "dur": 40, "ph": "X", "name": "ReadAsync 132", "args": {} },
{ "pid": 36728, "tid": 60129542144, "ts": 1749474197763893, "dur": 1, "ph": "X", "name": "ProcessMessages 176", "args": {} },
{ "pid": 36728, "tid": 60129542144, "ts": 1749474197763895, "dur": 36, "ph": "X", "name": "ReadAsync 176", "args": {} },
{ "pid": 36728, "tid": 60129542144, "ts": 1749474197763935, "dur": 2, "ph": "X", "name": "ProcessMessages 204", "args": {} },
{ "pid": 36728, "tid": 60129542144, "ts": 1749474197763938, "dur": 36, "ph": "X", "name": "ReadAsync 204", "args": {} },
{ "pid": 36728, "tid": 60129542144, "ts": 1749474197763976, "dur": 1, "ph": "X", "name": "ProcessMessages 196", "args": {} },
{ "pid": 36728, "tid": 60129542144, "ts": 1749474197763979, "dur": 33, "ph": "X", "name": "ReadAsync 196", "args": {} },
{ "pid": 36728, "tid": 60129542144, "ts": 1749474197764014, "dur": 1, "ph": "X", "name": "ProcessMessages 124", "args": {} },
{ "pid": 36728, "tid": 60129542144, "ts": 1749474197764016, "dur": 48, "ph": "X", "name": "ReadAsync 124", "args": {} },
{ "pid": 36728, "tid": 60129542144, "ts": 1749474197764067, "dur": 1, "ph": "X", "name": "ProcessMessages 116", "args": {} },
{ "pid": 36728, "tid": 60129542144, "ts": 1749474197764070, "dur": 49, "ph": "X", "name": "ReadAsync 116", "args": {} },
{ "pid": 36728, "tid": 60129542144, "ts": 1749474197764123, "dur": 2, "ph": "X", "name": "ProcessMessages 272", "args": {} },
{ "pid": 36728, "tid": 60129542144, "ts": 1749474197764126, "dur": 31, "ph": "X", "name": "ReadAsync 272", "args": {} },
{ "pid": 36728, "tid": 60129542144, "ts": 1749474197764159, "dur": 1, "ph": "X", "name": "ProcessMessages 144", "args": {} },
{ "pid": 36728, "tid": 60129542144, "ts": 1749474197764161, "dur": 20, "ph": "X", "name": "ReadAsync 144", "args": {} },
{ "pid": 36728, "tid": 60129542144, "ts": 1749474197764185, "dur": 19, "ph": "X", "name": "ReadAsync 16", "args": {} },
{ "pid": 36728, "tid": 60129542144, "ts": 1749474197764206, "dur": 764, "ph": "X", "name": "ReadAsync 16", "args": {} },
{ "pid": 36728, "tid": 60129542144, "ts": 1749474197764973, "dur": 19, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 36728, "tid": 60129542144, "ts": 1749474197764993, "dur": 1, "ph": "X", "name": "ProcessMessages 8", "args": {} },
{ "pid": 36728, "tid": 60129542144, "ts": 1749474197764996, "dur": 2228, "ph": "X", "name": "ReadAsync 8", "args": {} },
{ "pid": 36728, "tid": 60129542144, "ts": 1749474197767233, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {} },
{ "pid": 36728, "tid": 60129542144, "ts": 1749474197767237, "dur": 41, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 36728, "tid": 60129542144, "ts": 1749474197767280, "dur": 13, "ph": "X", "name": "ProcessMessages 50", "args": {} },
{ "pid": 36728, "tid": 60129542144, "ts": 1749474197767295, "dur": 416, "ph": "X", "name": "ReadAsync 50", "args": {} },
{ "pid": 36728, "tid": 60129542144, "ts": 1749474197767715, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {} },
{ "pid": 36728, "tid": 60129542144, "ts": 1749474197767717, "dur": 28, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 36728, "tid": 60129542144, "ts": 1749474197767748, "dur": 4699, "ph": "X", "name": "ReadAsync 13", "args": {} },
{ "pid": 36728, "tid": 2801, "ts": 1749474197773721, "dur": 328, "ph": "X", "name": "ReadEntireBinlogFromIpcAsync", "args": {} },
{ "pid": 36728, "tid": 55834574848, "ph": "M", "name": "thread_name", "args": { "name": "ReadEntireBinlogFromIpcAsync" } },
{ "pid": 36728, "tid": 55834574848, "ts": 1749474192571005, "dur": 13679, "ph": "X", "name": "WaitForConnectionAsync", "args": {} },
{ "pid": 36728, "tid": 55834574848, "ts": 1749474192584686, "dur": 4967624, "ph": "X", "name": "UpdateFromStreamAsync", "args": {} },
{ "pid": 36728, "tid": 55834574848, "ts": 1749474192584697, "dur": 102, "ph": "X", "name": "ReadAsync 0", "args": {} },
{ "pid": 36728, "tid": 55834574848, "ts": 1749474192584802, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {} },
{ "pid": 36728, "tid": 55834574848, "ts": 1749474192584803, "dur": 23937, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 36728, "tid": 55834574848, "ts": 1749474192608749, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {} },
{ "pid": 36728, "tid": 55834574848, "ts": 1749474192608752, "dur": 111, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 36728, "tid": 55834574848, "ts": 1749474192608864, "dur": 5, "ph": "X", "name": "ProcessMessages 47", "args": {} },
{ "pid": 36728, "tid": 55834574848, "ts": 1749474192608870, "dur": 3891, "ph": "X", "name": "ReadAsync 47", "args": {} },
{ "pid": 36728, "tid": 55834574848, "ts": 1749474192612767, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {} },
{ "pid": 36728, "tid": 55834574848, "ts": 1749474192612769, "dur": 116, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 36728, "tid": 55834574848, "ts": 1749474192612893, "dur": 2, "ph": "X", "name": "ProcessMessages 3468", "args": {} },
{ "pid": 36728, "tid": 55834574848, "ts": 1749474192612897, "dur": 113, "ph": "X", "name": "ReadAsync 3468", "args": {} },
{ "pid": 36728, "tid": 55834574848, "ts": 1749474192613012, "dur": 1, "ph": "X", "name": "ProcessMessages 1139", "args": {} },
{ "pid": 36728, "tid": 55834574848, "ts": 1749474192613013, "dur": 201, "ph": "X", "name": "ReadAsync 1139", "args": {} },
{ "pid": 36728, "tid": 55834574848, "ts": 1749474192613218, "dur": 1, "ph": "X", "name": "ProcessMessages 362", "args": {} },
{ "pid": 36728, "tid": 55834574848, "ts": 1749474192613219, "dur": 165, "ph": "X", "name": "ReadAsync 362", "args": {} },
{ "pid": 36728, "tid": 55834574848, "ts": 1749474192613387, "dur": 3, "ph": "X", "name": "ProcessMessages 7680", "args": {} },
{ "pid": 36728, "tid": 55834574848, "ts": 1749474192613391, "dur": 122, "ph": "X", "name": "ReadAsync 7680", "args": {} },
{ "pid": 36728, "tid": 55834574848, "ts": 1749474192613516, "dur": 2, "ph": "X", "name": "ProcessMessages 4604", "args": {} },
{ "pid": 36728, "tid": 55834574848, "ts": 1749474192613518, "dur": 112, "ph": "X", "name": "ReadAsync 4604", "args": {} },
{ "pid": 36728, "tid": 55834574848, "ts": 1749474192613633, "dur": 2, "ph": "X", "name": "ProcessMessages 3159", "args": {} },
{ "pid": 36728, "tid": 55834574848, "ts": 1749474192613635, "dur": 46, "ph": "X", "name": "ReadAsync 3159", "args": {} },
{ "pid": 36728, "tid": 55834574848, "ts": 1749474192613687, "dur": 1, "ph": "X", "name": "ProcessMessages 2388", "args": {} },
{ "pid": 36728, "tid": 55834574848, "ts": 1749474192613689, "dur": 33, "ph": "X", "name": "ReadAsync 2388", "args": {} },
{ "pid": 36728, "tid": 55834574848, "ts": 1749474192613725, "dur": 113, "ph": "X", "name": "ReadAsync 1527", "args": {} },
{ "pid": 36728, "tid": 55834574848, "ts": 1749474192613840, "dur": 180, "ph": "X", "name": "ReadAsync 406", "args": {} },
{ "pid": 36728, "tid": 55834574848, "ts": 1749474192614024, "dur": 2, "ph": "X", "name": "ProcessMessages 3992", "args": {} },
{ "pid": 36728, "tid": 55834574848, "ts": 1749474192614027, "dur": 60, "ph": "X", "name": "ReadAsync 3992", "args": {} },
{ "pid": 36728, "tid": 55834574848, "ts": 1749474192614090, "dur": 3, "ph": "X", "name": "ProcessMessages 6487", "args": {} },
{ "pid": 36728, "tid": 55834574848, "ts": 1749474192614094, "dur": 27, "ph": "X", "name": "ReadAsync 6487", "args": {} },
{ "pid": 36728, "tid": 55834574848, "ts": 1749474192614124, "dur": 21, "ph": "X", "name": "ReadAsync 1070", "args": {} },
{ "pid": 36728, "tid": 55834574848, "ts": 1749474192614148, "dur": 24, "ph": "X", "name": "ReadAsync 815", "args": {} },
{ "pid": 36728, "tid": 55834574848, "ts": 1749474192614175, "dur": 20, "ph": "X", "name": "ReadAsync 1050", "args": {} },
{ "pid": 36728, "tid": 55834574848, "ts": 1749474192614197, "dur": 23, "ph": "X", "name": "ReadAsync 788", "args": {} },
{ "pid": 36728, "tid": 55834574848, "ts": 1749474192614223, "dur": 34, "ph": "X", "name": "ReadAsync 828", "args": {} },
{ "pid": 36728, "tid": 55834574848, "ts": 1749474192614261, "dur": 1, "ph": "X", "name": "ProcessMessages 793", "args": {} },
{ "pid": 36728, "tid": 55834574848, "ts": 1749474192614263, "dur": 41, "ph": "X", "name": "ReadAsync 793", "args": {} },
{ "pid": 36728, "tid": 55834574848, "ts": 1749474192614308, "dur": 1, "ph": "X", "name": "ProcessMessages 1445", "args": {} },
{ "pid": 36728, "tid": 55834574848, "ts": 1749474192614310, "dur": 30, "ph": "X", "name": "ReadAsync 1445", "args": {} },
{ "pid": 36728, "tid": 55834574848, "ts": 1749474192614342, "dur": 1, "ph": "X", "name": "ProcessMessages 1104", "args": {} },
{ "pid": 36728, "tid": 55834574848, "ts": 1749474192614344, "dur": 22, "ph": "X", "name": "ReadAsync 1104", "args": {} },
{ "pid": 36728, "tid": 55834574848, "ts": 1749474192614369, "dur": 1, "ph": "X", "name": "ProcessMessages 335", "args": {} },
{ "pid": 36728, "tid": 55834574848, "ts": 1749474192614371, "dur": 118, "ph": "X", "name": "ReadAsync 335", "args": {} },
{ "pid": 36728, "tid": 55834574848, "ts": 1749474192614492, "dur": 1, "ph": "X", "name": "ProcessMessages 1372", "args": {} },
{ "pid": 36728, "tid": 55834574848, "ts": 1749474192614494, "dur": 46, "ph": "X", "name": "ReadAsync 1372", "args": {} },
{ "pid": 36728, "tid": 55834574848, "ts": 1749474192614542, "dur": 2, "ph": "X", "name": "ProcessMessages 3989", "args": {} },
{ "pid": 36728, "tid": 55834574848, "ts": 1749474192614545, "dur": 149, "ph": "X", "name": "ReadAsync 3989", "args": {} },
{ "pid": 36728, "tid": 55834574848, "ts": 1749474192614698, "dur": 1, "ph": "X", "name": "ProcessMessages 942", "args": {} },
{ "pid": 36728, "tid": 55834574848, "ts": 1749474192614700, "dur": 72, "ph": "X", "name": "ReadAsync 942", "args": {} },
{ "pid": 36728, "tid": 55834574848, "ts": 1749474192614776, "dur": 4, "ph": "X", "name": "ProcessMessages 6152", "args": {} },
{ "pid": 36728, "tid": 55834574848, "ts": 1749474192614781, "dur": 55, "ph": "X", "name": "ReadAsync 6152", "args": {} },
{ "pid": 36728, "tid": 55834574848, "ts": 1749474192614840, "dur": 1, "ph": "X", "name": "ProcessMessages 1605", "args": {} },
{ "pid": 36728, "tid": 55834574848, "ts": 1749474192614842, "dur": 32, "ph": "X", "name": "ReadAsync 1605", "args": {} },
{ "pid": 36728, "tid": 55834574848, "ts": 1749474192614877, "dur": 1, "ph": "X", "name": "ProcessMessages 965", "args": {} },
{ "pid": 36728, "tid": 55834574848, "ts": 1749474192614879, "dur": 3950, "ph": "X", "name": "ReadAsync 965", "args": {} },
{ "pid": 36728, "tid": 55834574848, "ts": 1749474192618838, "dur": 4, "ph": "X", "name": "ProcessMessages 12", "args": {} },
{ "pid": 36728, "tid": 55834574848, "ts": 1749474192618843, "dur": 56, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 36728, "tid": 55834574848, "ts": 1749474192618904, "dur": 4, "ph": "X", "name": "ProcessMessages 24", "args": {} },
{ "pid": 36728, "tid": 55834574848, "ts": 1749474192618909, "dur": 64, "ph": "X", "name": "ReadAsync 24", "args": {} },
{ "pid": 36728, "tid": 55834574848, "ts": 1749474192618979, "dur": 39, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 36728, "tid": 55834574848, "ts": 1749474192619022, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {} },
{ "pid": 36728, "tid": 55834574848, "ts": 1749474192619024, "dur": 230, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 36728, "tid": 55834574848, "ts": 1749474192619258, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {} },
{ "pid": 36728, "tid": 55834574848, "ts": 1749474192619260, "dur": 47, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 36728, "tid": 55834574848, "ts": 1749474192619310, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {} },
{ "pid": 36728, "tid": 55834574848, "ts": 1749474192619312, "dur": 51, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 36728, "tid": 55834574848, "ts": 1749474192619366, "dur": 3, "ph": "X", "name": "ProcessMessages 32", "args": {} },
{ "pid": 36728, "tid": 55834574848, "ts": 1749474192619370, "dur": 36, "ph": "X", "name": "ReadAsync 32", "args": {} },
{ "pid": 36728, "tid": 55834574848, "ts": 1749474192619411, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {} },
{ "pid": 36728, "tid": 55834574848, "ts": 1749474192619412, "dur": 35, "ph": "X", "name": "ReadAsync 16", "args": {} },
{ "pid": 36728, "tid": 55834574848, "ts": 1749474192619452, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {} },
{ "pid": 36728, "tid": 55834574848, "ts": 1749474192619454, "dur": 21, "ph": "X", "name": "ReadAsync 16", "args": {} },
{ "pid": 36728, "tid": 55834574848, "ts": 1749474192619478, "dur": 63, "ph": "X", "name": "ReadAsync 16", "args": {} },
{ "pid": 36728, "tid": 55834574848, "ts": 1749474192619546, "dur": 34, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 36728, "tid": 55834574848, "ts": 1749474192619584, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {} },
{ "pid": 36728, "tid": 55834574848, "ts": 1749474192619586, "dur": 36, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 36728, "tid": 55834574848, "ts": 1749474192619629, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {} },
{ "pid": 36728, "tid": 55834574848, "ts": 1749474192619632, "dur": 50, "ph": "X", "name": "ReadAsync 16", "args": {} },
{ "pid": 36728, "tid": 55834574848, "ts": 1749474192619687, "dur": 23, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 36728, "tid": 55834574848, "ts": 1749474192619713, "dur": 92, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 36728, "tid": 55834574848, "ts": 1749474192619809, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {} },
{ "pid": 36728, "tid": 55834574848, "ts": 1749474192619810, "dur": 41, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 36728, "tid": 55834574848, "ts": 1749474192619856, "dur": 2, "ph": "X", "name": "ProcessMessages 8", "args": {} },
{ "pid": 36728, "tid": 55834574848, "ts": 1749474192619860, "dur": 240, "ph": "X", "name": "ReadAsync 8", "args": {} },
{ "pid": 36728, "tid": 55834574848, "ts": 1749474192620103, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {} },
{ "pid": 36728, "tid": 55834574848, "ts": 1749474192620105, "dur": 44, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 36728, "tid": 55834574848, "ts": 1749474192620153, "dur": 387, "ph": "X", "name": "ProcessMessages 34", "args": {} },
{ "pid": 36728, "tid": 55834574848, "ts": 1749474192620544, "dur": 44, "ph": "X", "name": "ReadAsync 34", "args": {} },
{ "pid": 36728, "tid": 55834574848, "ts": 1749474192620592, "dur": 2, "ph": "X", "name": "ProcessMessages 20", "args": {} },
{ "pid": 36728, "tid": 55834574848, "ts": 1749474192620595, "dur": 152, "ph": "X", "name": "ReadAsync 20", "args": {} },
{ "pid": 36728, "tid": 55834574848, "ts": 1749474192620753, "dur": 36, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 36728, "tid": 55834574848, "ts": 1749474192620793, "dur": 9, "ph": "X", "name": "ProcessMessages 34", "args": {} },
{ "pid": 36728, "tid": 55834574848, "ts": 1749474192620803, "dur": 2168, "ph": "X", "name": "ReadAsync 34", "args": {} },
{ "pid": 36728, "tid": 55834574848, "ts": 1749474192622982, "dur": 3, "ph": "X", "name": "ProcessMessages 12", "args": {} },
{ "pid": 36728, "tid": 55834574848, "ts": 1749474192622987, "dur": 30, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 36728, "tid": 55834574848, "ts": 1749474192623019, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {} },
{ "pid": 36728, "tid": 55834574848, "ts": 1749474192623021, "dur": 1202, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 36728, "tid": 55834574848, "ts": 1749474192624233, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {} },
{ "pid": 36728, "tid": 55834574848, "ts": 1749474192624237, "dur": 35, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 36728, "tid": 55834574848, "ts": 1749474192624273, "dur": 1, "ph": "X", "name": "ProcessMessages 36", "args": {} },
{ "pid": 36728, "tid": 55834574848, "ts": 1749474192624275, "dur": 46, "ph": "X", "name": "ReadAsync 36", "args": {} },
{ "pid": 36728, "tid": 55834574848, "ts": 1749474192624327, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {} },
{ "pid": 36728, "tid": 55834574848, "ts": 1749474192624330, "dur": 37, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 36728, "tid": 55834574848, "ts": 1749474192624371, "dur": 153, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 36728, "tid": 55834574848, "ts": 1749474192624531, "dur": 36, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 36728, "tid": 55834574848, "ts": 1749474192624570, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {} },
{ "pid": 36728, "tid": 55834574848, "ts": 1749474192624572, "dur": 55, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 36728, "tid": 55834574848, "ts": 1749474192624631, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {} },
{ "pid": 36728, "tid": 55834574848, "ts": 1749474192624633, "dur": 59, "ph": "X", "name": "ReadAsync 32", "args": {} },
{ "pid": 36728, "tid": 55834574848, "ts": 1749474192624696, "dur": 45, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 36728, "tid": 55834574848, "ts": 1749474192624745, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {} },
{ "pid": 36728, "tid": 55834574848, "ts": 1749474192624747, "dur": 20, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 36728, "tid": 55834574848, "ts": 1749474192624770, "dur": 55, "ph": "X", "name": "ReadAsync 16", "args": {} },
{ "pid": 36728, "tid": 55834574848, "ts": 1749474192624830, "dur": 32, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 36728, "tid": 55834574848, "ts": 1749474192624866, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {} },
{ "pid": 36728, "tid": 55834574848, "ts": 1749474192624868, "dur": 47, "ph": "X", "name": "ReadAsync 20", "args": {} },
{ "pid": 36728, "tid": 55834574848, "ts": 1749474192624918, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {} },
{ "pid": 36728, "tid": 55834574848, "ts": 1749474192624920, "dur": 36, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 36728, "tid": 55834574848, "ts": 1749474192624959, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {} },
{ "pid": 36728, "tid": 55834574848, "ts": 1749474192624961, "dur": 44, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 36728, "tid": 55834574848, "ts": 1749474192625018, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {} },
{ "pid": 36728, "tid": 55834574848, "ts": 1749474192625020, "dur": 18, "ph": "X", "name": "ReadAsync 16", "args": {} },
{ "pid": 36728, "tid": 55834574848, "ts": 1749474192625041, "dur": 54, "ph": "X", "name": "ReadAsync 16", "args": {} },
{ "pid": 36728, "tid": 55834574848, "ts": 1749474192625100, "dur": 117, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 36728, "tid": 55834574848, "ts": 1749474192625220, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {} },
{ "pid": 36728, "tid": 55834574848, "ts": 1749474192625222, "dur": 22, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 36728, "tid": 55834574848, "ts": 1749474192625247, "dur": 15, "ph": "X", "name": "ReadAsync 16", "args": {} },
{ "pid": 36728, "tid": 55834574848, "ts": 1749474192625264, "dur": 20, "ph": "X", "name": "ProcessMessages 46", "args": {} },
{ "pid": 36728, "tid": 55834574848, "ts": 1749474192625286, "dur": 38, "ph": "X", "name": "ReadAsync 46", "args": {} },
{ "pid": 36728, "tid": 55834574848, "ts": 1749474192625330, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {} },
{ "pid": 36728, "tid": 55834574848, "ts": 1749474192625332, "dur": 27, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 36728, "tid": 55834574848, "ts": 1749474192625362, "dur": 47, "ph": "X", "name": "ReadAsync 20", "args": {} },
{ "pid": 36728, "tid": 55834574848, "ts": 1749474192625413, "dur": 25, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 36728, "tid": 55834574848, "ts": 1749474192625441, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {} },
{ "pid": 36728, "tid": 55834574848, "ts": 1749474192625443, "dur": 144, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 36728, "tid": 55834574848, "ts": 1749474192625593, "dur": 32, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 36728, "tid": 55834574848, "ts": 1749474192625628, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {} },
{ "pid": 36728, "tid": 55834574848, "ts": 1749474192625630, "dur": 44, "ph": "X", "name": "ReadAsync 20", "args": {} },
{ "pid": 36728, "tid": 55834574848, "ts": 1749474192625677, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {} },
{ "pid": 36728, "tid": 55834574848, "ts": 1749474192625679, "dur": 30, "ph": "X", "name": "ReadAsync 16", "args": {} },
{ "pid": 36728, "tid": 55834574848, "ts": 1749474192625712, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {} },
{ "pid": 36728, "tid": 55834574848, "ts": 1749474192625715, "dur": 64, "ph": "X", "name": "ReadAsync 48", "args": {} },
{ "pid": 36728, "tid": 55834574848, "ts": 1749474192625782, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {} },
{ "pid": 36728, "tid": 55834574848, "ts": 1749474192625784, "dur": 54, "ph": "X", "name": "ReadAsync 16", "args": {} },
{ "pid": 36728, "tid": 55834574848, "ts": 1749474192625842, "dur": 25, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 36728, "tid": 55834574848, "ts": 1749474192625870, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {} },
{ "pid": 36728, "tid": 55834574848, "ts": 1749474192625871, "dur": 19, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 36728, "tid": 55834574848, "ts": 1749474192625893, "dur": 87, "ph": "X", "name": "ReadAsync 16", "args": {} },
{ "pid": 36728, "tid": 55834574848, "ts": 1749474192625985, "dur": 37, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 36728, "tid": 55834574848, "ts": 1749474192626036, "dur": 1, "ph": "X", "name": "ProcessMessages 52", "args": {} },
{ "pid": 36728, "tid": 55834574848, "ts": 1749474192626039, "dur": 22, "ph": "X", "name": "ReadAsync 52", "args": {} },
{ "pid": 36728, "tid": 55834574848, "ts": 1749474192626066, "dur": 19, "ph": "X", "name": "ReadAsync 16", "args": {} },
{ "pid": 36728, "tid": 55834574848, "ts": 1749474192626088, "dur": 18, "ph": "X", "name": "ReadAsync 32", "args": {} },
{ "pid": 36728, "tid": 55834574848, "ts": 1749474192626109, "dur": 14, "ph": "X", "name": "ReadAsync 16", "args": {} },
{ "pid": 36728, "tid": 55834574848, "ts": 1749474192626126, "dur": 14, "ph": "X", "name": "ReadAsync 16", "args": {} },
{ "pid": 36728, "tid": 55834574848, "ts": 1749474192626142, "dur": 16, "ph": "X", "name": "ReadAsync 16", "args": {} },
{ "pid": 36728, "tid": 55834574848, "ts": 1749474192626161, "dur": 15, "ph": "X", "name": "ReadAsync 32", "args": {} },
{ "pid": 36728, "tid": 55834574848, "ts": 1749474192626180, "dur": 58, "ph": "X", "name": "ReadAsync 16", "args": {} },
{ "pid": 36728, "tid": 55834574848, "ts": 1749474192626243, "dur": 63, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 36728, "tid": 55834574848, "ts": 1749474192626309, "dur": 2, "ph": "X", "name": "ProcessMessages 24", "args": {} },
{ "pid": 36728, "tid": 55834574848, "ts": 1749474192626313, "dur": 41, "ph": "X", "name": "ReadAsync 24", "args": {} },
{ "pid": 36728, "tid": 55834574848, "ts": 1749474192626359, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {} },
{ "pid": 36728, "tid": 55834574848, "ts": 1749474192626361, "dur": 89, "ph": "X", "name": "ReadAsync 16", "args": {} },
{ "pid": 36728, "tid": 55834574848, "ts": 1749474192626454, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {} },
{ "pid": 36728, "tid": 55834574848, "ts": 1749474192626456, "dur": 29, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 36728, "tid": 55834574848, "ts": 1749474192626488, "dur": 1, "ph": "X", "name": "ProcessMessages 36", "args": {} },
{ "pid": 36728, "tid": 55834574848, "ts": 1749474192626491, "dur": 171, "ph": "X", "name": "ReadAsync 36", "args": {} },
{ "pid": 36728, "tid": 55834574848, "ts": 1749474192626665, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {} },
{ "pid": 36728, "tid": 55834574848, "ts": 1749474192626668, "dur": 59, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 36728, "tid": 55834574848, "ts": 1749474192626730, "dur": 1, "ph": "X", "name": "ProcessMessages 36", "args": {} },
{ "pid": 36728, "tid": 55834574848, "ts": 1749474192626733, "dur": 287, "ph": "X", "name": "ReadAsync 36", "args": {} },
{ "pid": 36728, "tid": 55834574848, "ts": 1749474192627023, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {} },
{ "pid": 36728, "tid": 55834574848, "ts": 1749474192627025, "dur": 53, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 36728, "tid": 55834574848, "ts": 1749474192627082, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {} },
{ "pid": 36728, "tid": 55834574848, "ts": 1749474192627084, "dur": 34, "ph": "X", "name": "ReadAsync 20", "args": {} },
{ "pid": 36728, "tid": 55834574848, "ts": 1749474192627121, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {} },
{ "pid": 36728, "tid": 55834574848, "ts": 1749474192627123, "dur": 169, "ph": "X", "name": "ReadAsync 16", "args": {} },
{ "pid": 36728, "tid": 55834574848, "ts": 1749474192627296, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {} },
{ "pid": 36728, "tid": 55834574848, "ts": 1749474192627299, "dur": 23, "ph": "X", "name": "ReadAsync 48", "args": {} },
{ "pid": 36728, "tid": 55834574848, "ts": 1749474192627345, "dur": 26, "ph": "X", "name": "ReadAsync 16", "args": {} },
{ "pid": 36728, "tid": 55834574848, "ts": 1749474192627374, "dur": 55, "ph": "X", "name": "ReadAsync 16", "args": {} },
{ "pid": 36728, "tid": 55834574848, "ts": 1749474192627432, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {} },
{ "pid": 36728, "tid": 55834574848, "ts": 1749474192627434, "dur": 214, "ph": "X", "name": "ReadAsync 16", "args": {} },
{ "pid": 36728, "tid": 55834574848, "ts": 1749474192627654, "dur": 104, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 36728, "tid": 55834574848, "ts": 1749474192627761, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {} },
{ "pid": 36728, "tid": 55834574848, "ts": 1749474192627762, "dur": 44, "ph": "X", "name": "ReadAsync 20", "args": {} },
{ "pid": 36728, "tid": 55834574848, "ts": 1749474192627808, "dur": 1, "ph": "X", "name": "ProcessMessages 96", "args": {} },
{ "pid": 36728, "tid": 55834574848, "ts": 1749474192627810, "dur": 164, "ph": "X", "name": "ReadAsync 96", "args": {} },
{ "pid": 36728, "tid": 55834574848, "ts": 1749474192627978, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {} },
{ "pid": 36728, "tid": 55834574848, "ts": 1749474192627980, "dur": 28, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 36728, "tid": 55834574848, "ts": 1749474192628011, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {} },
{ "pid": 36728, "tid": 55834574848, "ts": 1749474192628013, "dur": 406, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 36728, "tid": 55834574848, "ts": 1749474192628424, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {} },
{ "pid": 36728, "tid": 55834574848, "ts": 1749474192628426, "dur": 68, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 36728, "tid": 55834574848, "ts": 1749474192628498, "dur": 4, "ph": "X", "name": "ProcessMessages 132", "args": {} },
{ "pid": 36728, "tid": 55834574848, "ts": 1749474192628504, "dur": 53, "ph": "X", "name": "ReadAsync 132", "args": {} },
{ "pid": 36728, "tid": 55834574848, "ts": 1749474192628560, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {} },
{ "pid": 36728, "tid": 55834574848, "ts": 1749474192628562, "dur": 43, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 36728, "tid": 55834574848, "ts": 1749474192628610, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {} },
{ "pid": 36728, "tid": 55834574848, "ts": 1749474192628612, "dur": 116, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 36728, "tid": 55834574848, "ts": 1749474192628732, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {} },
{ "pid": 36728, "tid": 55834574848, "ts": 1749474192628734, "dur": 349, "ph": "X", "name": "ReadAsync 16", "args": {} },
{ "pid": 36728, "tid": 55834574848, "ts": 1749474192629086, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {} },
{ "pid": 36728, "tid": 55834574848, "ts": 1749474192629088, "dur": 31, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 36728, "tid": 55834574848, "ts": 1749474192629123, "dur": 1, "ph": "X", "name": "ProcessMessages 36", "args": {} },
{ "pid": 36728, "tid": 55834574848, "ts": 1749474192629160, "dur": 86, "ph": "X", "name": "ReadAsync 36", "args": {} },
{ "pid": 36728, "tid": 55834574848, "ts": 1749474192629251, "dur": 28, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 36728, "tid": 55834574848, "ts": 1749474192629283, "dur": 100, "ph": "X", "name": "ReadAsync 20", "args": {} },
{ "pid": 36728, "tid": 55834574848, "ts": 1749474192629388, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {} },
{ "pid": 36728, "tid": 55834574848, "ts": 1749474192629390, "dur": 292, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 36728, "tid": 55834574848, "ts": 1749474192629687, "dur": 1, "ph": "X", "name": "ProcessMessages 52", "args": {} },
{ "pid": 36728, "tid": 55834574848, "ts": 1749474192629689, "dur": 51, "ph": "X", "name": "ReadAsync 52", "args": {} },
{ "pid": 36728, "tid": 55834574848, "ts": 1749474192629744, "dur": 3, "ph": "X", "name": "ProcessMessages 288", "args": {} },
{ "pid": 36728, "tid": 55834574848, "ts": 1749474192629748, "dur": 337, "ph": "X", "name": "ReadAsync 288", "args": {} },
{ "pid": 36728, "tid": 55834574848, "ts": 1749474192630089, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {} },
{ "pid": 36728, "tid": 55834574848, "ts": 1749474192630091, "dur": 43, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 36728, "tid": 55834574848, "ts": 1749474192630137, "dur": 1, "ph": "X", "name": "ProcessMessages 84", "args": {} },
{ "pid": 36728, "tid": 55834574848, "ts": 1749474192630138, "dur": 397, "ph": "X", "name": "ReadAsync 84", "args": {} },
{ "pid": 36728, "tid": 55834574848, "ts": 1749474192630549, "dur": 34, "ph": "X", "name": "ProcessMessages 12", "args": {} },
{ "pid": 36728, "tid": 55834574848, "ts": 1749474192630586, "dur": 38, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 36728, "tid": 55834574848, "ts": 1749474192630626, "dur": 4, "ph": "X", "name": "ProcessMessages 176", "args": {} },
{ "pid": 36728, "tid": 55834574848, "ts": 1749474192630631, "dur": 55, "ph": "X", "name": "ReadAsync 176", "args": {} },
{ "pid": 36728, "tid": 55834574848, "ts": 1749474192630690, "dur": 3, "ph": "X", "name": "ProcessMessages 100", "args": {} },
{ "pid": 36728, "tid": 55834574848, "ts": 1749474192630695, "dur": 35, "ph": "X", "name": "ReadAsync 100", "args": {} },
{ "pid": 36728, "tid": 55834574848, "ts": 1749474192630733, "dur": 2, "ph": "X", "name": "ProcessMessages 84", "args": {} },
{ "pid": 36728, "tid": 55834574848, "ts": 1749474192630737, "dur": 198, "ph": "X", "name": "ReadAsync 84", "args": {} },
{ "pid": 36728, "tid": 55834574848, "ts": 1749474192630940, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {} },
{ "pid": 36728, "tid": 55834574848, "ts": 1749474192630941, "dur": 118, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 36728, "tid": 55834574848, "ts": 1749474192631064, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {} },
{ "pid": 36728, "tid": 55834574848, "ts": 1749474192631066, "dur": 962, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 36728, "tid": 55834574848, "ts": 1749474192632036, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {} },
{ "pid": 36728, "tid": 55834574848, "ts": 1749474192632039, "dur": 188, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 36728, "tid": 55834574848, "ts": 1749474192632232, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {} },
{ "pid": 36728, "tid": 55834574848, "ts": 1749474192632235, "dur": 53, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 36728, "tid": 55834574848, "ts": 1749474192632293, "dur": 23, "ph": "X", "name": "ProcessMessages 46", "args": {} },
{ "pid": 36728, "tid": 55834574848, "ts": 1749474192632317, "dur": 460, "ph": "X", "name": "ReadAsync 46", "args": {} },
{ "pid": 36728, "tid": 55834574848, "ts": 1749474192632782, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {} },
{ "pid": 36728, "tid": 55834574848, "ts": 1749474192632784, "dur": 39, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 36728, "tid": 55834574848, "ts": 1749474192632829, "dur": 8, "ph": "X", "name": "ProcessMessages 34", "args": {} },
{ "pid": 36728, "tid": 55834574848, "ts": 1749474192632848, "dur": 3068, "ph": "X", "name": "ReadAsync 34", "args": {} },
{ "pid": 36728, "tid": 55834574848, "ts": 1749474192635929, "dur": 3, "ph": "X", "name": "ProcessMessages 12", "args": {} },
{ "pid": 36728, "tid": 55834574848, "ts": 1749474192635934, "dur": 59, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 36728, "tid": 55834574848, "ts": 1749474192636000, "dur": 14, "ph": "X", "name": "ProcessMessages 34", "args": {} },
{ "pid": 36728, "tid": 55834574848, "ts": 1749474192636016, "dur": 864, "ph": "X", "name": "ReadAsync 34", "args": {} },
{ "pid": 36728, "tid": 55834574848, "ts": 1749474192636888, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {} },
{ "pid": 36728, "tid": 55834574848, "ts": 1749474192636891, "dur": 66, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 36728, "tid": 55834574848, "ts": 1749474192636960, "dur": 13, "ph": "X", "name": "ProcessMessages 34", "args": {} },
{ "pid": 36728, "tid": 55834574848, "ts": 1749474192636975, "dur": 569, "ph": "X", "name": "ReadAsync 34", "args": {} },
{ "pid": 36728, "tid": 55834574848, "ts": 1749474192637550, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {} },
{ "pid": 36728, "tid": 55834574848, "ts": 1749474192637552, "dur": 87, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 36728, "tid": 55834574848, "ts": 1749474192637643, "dur": 14, "ph": "X", "name": "ProcessMessages 34", "args": {} },
{ "pid": 36728, "tid": 55834574848, "ts": 1749474192637659, "dur": 151, "ph": "X", "name": "ReadAsync 34", "args": {} },
{ "pid": 36728, "tid": 55834574848, "ts": 1749474192637814, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {} },
{ "pid": 36728, "tid": 55834574848, "ts": 1749474192637816, "dur": 28, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 36728, "tid": 55834574848, "ts": 1749474192637846, "dur": 4, "ph": "X", "name": "ProcessMessages 34", "args": {} },
{ "pid": 36728, "tid": 55834574848, "ts": 1749474192637851, "dur": 226, "ph": "X", "name": "ReadAsync 34", "args": {} },
{ "pid": 36728, "tid": 55834574848, "ts": 1749474192638082, "dur": 41, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 36728, "tid": 55834574848, "ts": 1749474192638132, "dur": 7, "ph": "X", "name": "ProcessMessages 34", "args": {} },
{ "pid": 36728, "tid": 55834574848, "ts": 1749474192638141, "dur": 236, "ph": "X", "name": "ReadAsync 34", "args": {} },
{ "pid": 36728, "tid": 55834574848, "ts": 1749474192638382, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {} },
{ "pid": 36728, "tid": 55834574848, "ts": 1749474192638384, "dur": 110, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 36728, "tid": 55834574848, "ts": 1749474192638498, "dur": 6, "ph": "X", "name": "ProcessMessages 34", "args": {} },
{ "pid": 36728, "tid": 55834574848, "ts": 1749474192638505, "dur": 735, "ph": "X", "name": "ReadAsync 34", "args": {} },
{ "pid": 36728, "tid": 55834574848, "ts": 1749474192639245, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {} },
{ "pid": 36728, "tid": 55834574848, "ts": 1749474192639246, "dur": 289, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 36728, "tid": 55834574848, "ts": 1749474192639540, "dur": 7, "ph": "X", "name": "ProcessMessages 34", "args": {} },
{ "pid": 36728, "tid": 55834574848, "ts": 1749474192639549, "dur": 415, "ph": "X", "name": "ReadAsync 34", "args": {} },
{ "pid": 36728, "tid": 55834574848, "ts": 1749474192639968, "dur": 60, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 36728, "tid": 55834574848, "ts": 1749474192640031, "dur": 11, "ph": "X", "name": "ProcessMessages 34", "args": {} },
{ "pid": 36728, "tid": 55834574848, "ts": 1749474192640043, "dur": 8415, "ph": "X", "name": "ReadAsync 34", "args": {} },
{ "pid": 36728, "tid": 55834574848, "ts": 1749474192648466, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {} },
{ "pid": 36728, "tid": 55834574848, "ts": 1749474192648469, "dur": 146, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 36728, "tid": 55834574848, "ts": 1749474192648618, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {} },
{ "pid": 36728, "tid": 55834574848, "ts": 1749474192648621, "dur": 32374, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 36728, "tid": 55834574848, "ts": 1749474192681003, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {} },
{ "pid": 36728, "tid": 55834574848, "ts": 1749474192681006, "dur": 48, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 36728, "tid": 55834574848, "ts": 1749474192681057, "dur": 6001, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 36728, "tid": 55834574848, "ts": 1749474192687066, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {} },
{ "pid": 36728, "tid": 55834574848, "ts": 1749474192687070, "dur": 61, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 36728, "tid": 55834574848, "ts": 1749474192687135, "dur": 3, "ph": "X", "name": "ProcessMessages 8", "args": {} },
{ "pid": 36728, "tid": 55834574848, "ts": 1749474192687148, "dur": 1143, "ph": "X", "name": "ReadAsync 8", "args": {} },
{ "pid": 36728, "tid": 55834574848, "ts": 1749474192688296, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {} },
{ "pid": 36728, "tid": 55834574848, "ts": 1749474192688298, "dur": 29, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 36728, "tid": 55834574848, "ts": 1749474192688329, "dur": 11, "ph": "X", "name": "ProcessMessages 34", "args": {} },
{ "pid": 36728, "tid": 55834574848, "ts": 1749474192688341, "dur": 5346, "ph": "X", "name": "ReadAsync 34", "args": {} },
{ "pid": 36728, "tid": 55834574848, "ts": 1749474192693692, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {} },
{ "pid": 36728, "tid": 55834574848, "ts": 1749474192693694, "dur": 142, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 36728, "tid": 55834574848, "ts": 1749474192693840, "dur": 40098, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 36728, "tid": 55834574848, "ts": 1749474192733945, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {} },
{ "pid": 36728, "tid": 55834574848, "ts": 1749474192733948, "dur": 37, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 36728, "tid": 55834574848, "ts": 1749474192733988, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {} },
{ "pid": 36728, "tid": 55834574848, "ts": 1749474192733989, "dur": 128538, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 36728, "tid": 55834574848, "ts": 1749474192862536, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {} },
{ "pid": 36728, "tid": 55834574848, "ts": 1749474192862539, "dur": 21, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 36728, "tid": 55834574848, "ts": 1749474192862561, "dur": 2, "ph": "X", "name": "ProcessMessages 8", "args": {} },
{ "pid": 36728, "tid": 55834574848, "ts": 1749474192862563, "dur": 617, "ph": "X", "name": "ReadAsync 8", "args": {} },
{ "pid": 36728, "tid": 55834574848, "ts": 1749474192863185, "dur": 35, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 36728, "tid": 55834574848, "ts": 1749474192863224, "dur": 2483, "ph": "X", "name": "ProcessMessages 16930", "args": {} },
{ "pid": 36728, "tid": 55834574848, "ts": 1749474192865710, "dur": 907802, "ph": "X", "name": "ReadAsync 16930", "args": {} },
{ "pid": 36728, "tid": 55834574848, "ts": 1749474193773519, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {} },
{ "pid": 36728, "tid": 55834574848, "ts": 1749474193773523, "dur": 20, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 36728, "tid": 55834574848, "ts": 1749474193773544, "dur": 2, "ph": "X", "name": "ProcessMessages 8", "args": {} },
{ "pid": 36728, "tid": 55834574848, "ts": 1749474193773547, "dur": 83630, "ph": "X", "name": "ReadAsync 8", "args": {} },
{ "pid": 36728, "tid": 55834574848, "ts": 1749474193857188, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {} },
{ "pid": 36728, "tid": 55834574848, "ts": 1749474193857192, "dur": 24, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 36728, "tid": 55834574848, "ts": 1749474193857217, "dur": 11, "ph": "X", "name": "ProcessMessages 516", "args": {} },
{ "pid": 36728, "tid": 55834574848, "ts": 1749474193857230, "dur": 9974, "ph": "X", "name": "ReadAsync 516", "args": {} },
{ "pid": 36728, "tid": 55834574848, "ts": 1749474193867212, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {} },
{ "pid": 36728, "tid": 55834574848, "ts": 1749474193867215, "dur": 20, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 36728, "tid": 55834574848, "ts": 1749474193867237, "dur": 2, "ph": "X", "name": "ProcessMessages 8", "args": {} },
{ "pid": 36728, "tid": 55834574848, "ts": 1749474193867239, "dur": 783, "ph": "X", "name": "ReadAsync 8", "args": {} },
{ "pid": 36728, "tid": 55834574848, "ts": 1749474193868036, "dur": 3, "ph": "X", "name": "ProcessMessages 12", "args": {} },
{ "pid": 36728, "tid": 55834574848, "ts": 1749474193868041, "dur": 29, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 36728, "tid": 55834574848, "ts": 1749474193868072, "dur": 14, "ph": "X", "name": "ProcessMessages 34", "args": {} },
{ "pid": 36728, "tid": 55834574848, "ts": 1749474193868087, "dur": 3673986, "ph": "X", "name": "ReadAsync 34", "args": {} },
{ "pid": 36728, "tid": 55834574848, "ts": 1749474197542078, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {} },
{ "pid": 36728, "tid": 55834574848, "ts": 1749474197542081, "dur": 86, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 36728, "tid": 55834574848, "ts": 1749474197542170, "dur": 8, "ph": "X", "name": "ProcessMessages 68", "args": {} },
{ "pid": 36728, "tid": 55834574848, "ts": 1749474197542179, "dur": 3245, "ph": "X", "name": "ReadAsync 68", "args": {} },
{ "pid": 36728, "tid": 55834574848, "ts": 1749474197545430, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {} },
{ "pid": 36728, "tid": 55834574848, "ts": 1749474197545433, "dur": 102, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 36728, "tid": 55834574848, "ts": 1749474197545539, "dur": 2, "ph": "X", "name": "ProcessMessages 13", "args": {} },
{ "pid": 36728, "tid": 55834574848, "ts": 1749474197545541, "dur": 6624, "ph": "X", "name": "ReadAsync 13", "args": {} },
{ "pid": 36728, "tid": 2801, "ts": 1749474197774053, "dur": 539, "ph": "X", "name": "ReadEntireBinlogFromIpcAsync", "args": {} },
{ "pid": 36728, "tid": 51539607552, "ph": "M", "name": "thread_name", "args": { "name": "ReadEntireBinlogFromIpcAsync" } },
{ "pid": 36728, "tid": 51539607552, "ts": 1749474191360316, "dur": 18107, "ph": "X", "name": "WaitForConnectionAsync", "args": {} },
{ "pid": 36728, "tid": 51539607552, "ts": 1749474191378425, "dur": 138, "ph": "X", "name": "UpdateFromStreamAsync", "args": {} },
{ "pid": 36728, "tid": 51539607552, "ts": 1749474191378436, "dur": 57, "ph": "X", "name": "ReadAsync 0", "args": {} },
{ "pid": 36728, "tid": 51539607552, "ts": 1749474191378498, "dur": 3, "ph": "X", "name": "ProcessMessages 4", "args": {} },
{ "pid": 36728, "tid": 51539607552, "ts": 1749474191378503, "dur": 40, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 36728, "tid": 51539607552, "ts": 1749474191378548, "dur": 2, "ph": "X", "name": "ProcessMessages 25", "args": {} },
{ "pid": 36728, "tid": 51539607552, "ts": 1749474191378550, "dur": 10, "ph": "X", "name": "ReadAsync 25", "args": {} },
{ "pid": 36728, "tid": 2801, "ts": 1749474197774594, "dur": 9, "ph": "X", "name": "ReadEntireBinlogFromIpcAsync", "args": {} },
{ "pid": 36728, "tid": 47244640256, "ph": "M", "name": "thread_name", "args": { "name": "BuildAsync" } },
{ "pid": 36728, "tid": 47244640256, "ts": 1749474191357389, "dur": 21196, "ph": "X", "name": "RunBackend", "args": {} },
{ "pid": 36728, "tid": 47244640256, "ts": 1749474191357488, "dur": 2756, "ph": "X", "name": "BackendProgram.Start", "args": {} },
{ "pid": 36728, "tid": 47244640256, "ts": 1749474191378589, "dur": 1189576, "ph": "X", "name": "await ExecuteBuildProgram", "args": {} },
{ "pid": 36728, "tid": 47244640256, "ts": 1749474192568182, "dur": 4984164, "ph": "X", "name": "RunBackend", "args": {} },
{ "pid": 36728, "tid": 47244640256, "ts": 1749474192568317, "dur": 2597, "ph": "X", "name": "BackendProgram.Start", "args": {} },
{ "pid": 36728, "tid": 47244640256, "ts": 1749474197552357, "dur": 158295, "ph": "X", "name": "await ExecuteBuildProgram", "args": {} },
{ "pid": 36728, "tid": 47244640256, "ts": 1749474197710665, "dur": 61815, "ph": "X", "name": "RunBackend", "args": {} },
{ "pid": 36728, "tid": 47244640256, "ts": 1749474197710752, "dur": 3371, "ph": "X", "name": "BackendProgram.Start", "args": {} },
{ "pid": 36728, "tid": 47244640256, "ts": 1749474197772483, "dur": 110, "ph": "X", "name": "await WaitForAndApplyScriptUpdaters", "args": {} },
{ "pid": 36728, "tid": 2801, "ts": 1749474197774605, "dur": 11, "ph": "X", "name": "BuildAsync", "args": {} },
{ "pid": 36728, "tid": 1, "ph": "M", "name": "thread_name", "args": { "name": "" } },
{ "pid": 36728, "tid": 1, "ts": 1749474191250030, "dur": 1339, "ph": "X", "name": "<Add>b__0", "args": {} },
{ "pid": 36728, "tid": 1, "ts": 1749474191251375, "dur": 105013, "ph": "X", "name": "<SetupBuildRequest>b__0", "args": {} },
{ "pid": 36728, "tid": 1, "ts": 1749474191356391, "dur": 975, "ph": "X", "name": "WriteJson", "args": {} },
{ "pid": 36728, "tid": 2801, "ts": 1749474197774617, "dur": 4, "ph": "X", "name": "", "args": {} },
{ "pid": 35942, "tid": -1, "ph": "M", "name": "process_name", "args": { "name": "netcorerun.dll" } },
{ "pid": 35942, "tid": -1, "ph": "M", "name": "process_sort_index", "args": { "sort_index": "-1" } },
{ "pid": 35942, "tid": 1, "ph": "M", "name": "thread_name", "args": { "name": "" } },
{ "pid": 35942, "tid": 1, "ts": 1749474197606941, "dur": 92539, "ph": "X", "name": "BuildProgram", "args": {} },
{ "pid": 35942, "tid": 1, "ts": 1749474197607752, "dur": 31623, "ph": "X", "name": "BuildProgramContextConstructor", "args": {} },
{ "pid": 35942, "tid": 1, "ts": 1749474197644607, "dur": 35632, "ph": "X", "name": "RunBuildProgram", "args": {} },
{ "pid": 35942, "tid": 1, "ts": 1749474197644893, "dur": 26787, "ph": "X", "name": "PlayerBuildProgramBase.SetupPlayerBuild", "args": {} },
{ "pid": 35942, "tid": 1, "ts": 1749474197645348, "dur": 18266, "ph": "X", "name": "SetupDataFiles", "args": {} },
{ "pid": 35942, "tid": 1, "ts": 1749474197663962, "dur": 420, "ph": "X", "name": "SetupCopyPlugins", "args": {} },
{ "pid": 35942, "tid": 1, "ts": 1749474197665662, "dur": 4677, "ph": "X", "name": "SetupUnityLinker", "args": {} },
{ "pid": 35942, "tid": 1, "ts": 1749474197685861, "dur": 2364, "ph": "X", "name": "OutputData.Write", "args": {} },
{ "pid": 35942, "tid": 1, "ts": 1749474197688226, "dur": 11248, "ph": "X", "name": "Backend.Write", "args": {} },
{ "pid": 35942, "tid": 1, "ts": 1749474197688942, "dur": 9527, "ph": "X", "name": "JsonToString", "args": {} },
{ "pid": 35942, "tid": 1, "ts": 1749474197704201, "dur": 1006, "ph": "X", "name": "", "args": {} },
{ "pid": 35942, "tid": 1, "ts": 1749474197703789, "dur": 1590, "ph": "X", "name": "Write chrome-trace events", "args": {} },
{ "cat":"", "pid":12345, "tid":0, "ts":0, "ph":"M", "name":"process_name", "args": { "name":"bee_backend" } }
,{ "pid":12345, "tid":0, "ts":1749474197731001, "dur":25915, "ph":"X", "name": "DriverInitData",  "args": { "detail":"" }}
,{ "pid":12345, "tid":0, "ts":1749474197756923, "dur":111, "ph":"X", "name": "RemoveStaleOutputs",  "args": { "detail":"" }}
,{ "pid":12345, "tid":0, "ts":1749474197757078, "dur":465, "ph":"X", "name": "BuildQueueInit",  "args": { "detail":"" }}
,{ "pid":12345, "tid":0, "ts":1749474197757939, "dur":117, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"CopyFiles C:/Users/<USER>/Desktop/Computer/Cascade Games/Void Voyager/Build/Void Voyager_Data/Managed/UnityEngine.CoreModule.dll" }}
,{ "pid":12345, "tid":0, "ts":1749474197758360, "dur":144, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"CopyFiles C:/Users/<USER>/Desktop/Computer/Cascade Games/Void Voyager/Build/Void Voyager_Data/Managed/UnityEngine.SpriteMaskModule.dll" }}
,{ "pid":12345, "tid":0, "ts":1749474197757560, "dur":2291, "ph":"X", "name": "EnqueueRequestedNodes",  "args": { "detail":"" }}
,{ "pid":12345, "tid":0, "ts":1749474197759853, "dur":8559, "ph":"X", "name": "WaitForBuildFinished", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":0, "ts":1749474197768413, "dur":304, "ph":"X", "name": "JoinBuildThread",  "args": { "detail":"" }}
,{ "pid":12345, "tid":0, "ts":1749474197768873, "dur":1800, "ph":"X", "name": "Tundra",  "args": { "detail":"Write AllBuiltNodes" }}
,{ "pid":12345, "tid":1, "ts":1749474197757860, "dur":1999, "ph":"X", "name": "FirstLock",  "args": { "detail":"" }}
,{ "pid":12345, "tid":1, "ts":1749474197759865, "dur":4344, "ph":"X", "name": "CheckDagSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":1, "ts":1749474197764282, "dur":67, "ph":"X", "name": "EmitNodeUpToDate",  "args": { "detail":"WriteText Library/Bee/artifacts/csharpactions/boot.config_tyr4.info" }}
,{ "pid":12345, "tid":1, "ts":1749474197764351, "dur":594, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"AddBootConfigGUID Library/Bee/artifacts/WinPlayerBuildProgram/boot.config" }}
,{ "pid":12345, "tid":1, "ts":1749474197764946, "dur":75, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":1, "ts":1749474197765026, "dur":60, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":1, "ts":1749474197765086, "dur":51, "ph":"X", "name": "EmitNodeUpToDate",  "args": { "detail":"CopyFiles C:/Users/<USER>/Desktop/Computer/Cascade Games/Void Voyager/Build/Void Voyager_Data/boot.config" }}
,{ "pid":12345, "tid":1, "ts":1749474197765268, "dur":3136, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1749474197757887, "dur":1986, "ph":"X", "name": "FirstLock",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1749474197759878, "dur":561, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1749474197760440, "dur":3819, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1749474197764268, "dur":92, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"CopyFiles C:/Users/<USER>/Desktop/Computer/Cascade Games/Void Voyager/Build/MonoBleedingEdge/etc/mono/browscap.ini" }}
,{ "pid":12345, "tid":2, "ts":1749474197764361, "dur":75, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1749474197764462, "dur":133, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\PlaybackEngines\\WindowsStandaloneSupport\\Variations\\win64_player_nondevelopment_mono\\MonoBleedingEdge\\EmbedRuntime\\MonoPosixHelper.dll" }}
,{ "pid":12345, "tid":2, "ts":1749474197764448, "dur":148, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"CopyFiles C:/Users/<USER>/Desktop/Computer/Cascade Games/Void Voyager/Build/MonoBleedingEdge/EmbedRuntime/MonoPosixHelper.dll" }}
,{ "pid":12345, "tid":2, "ts":1749474197764597, "dur":242, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1749474197764897, "dur":95, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1749474197765054, "dur":54, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1749474197765117, "dur":55, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1749474197765311, "dur":3147, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1749474197758132, "dur":1872, "ph":"X", "name": "FirstLock",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1749474197760005, "dur":4382, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1749474197764408, "dur":293, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"CopyFiles C:/Users/<USER>/Desktop/Computer/Cascade Games/Void Voyager/Build/MonoBleedingEdge/etc/mono/4.0/DefaultWsdlHelpGenerator.aspx" }}
,{ "pid":12345, "tid":3, "ts":1749474197764702, "dur":204, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1749474197764945, "dur":78, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1749474197765074, "dur":88, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1749474197765170, "dur":159, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1749474197765337, "dur":3173, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1749474197758298, "dur":1797, "ph":"X", "name": "FirstLock",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1749474197760095, "dur":4140, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1749474197764259, "dur":95, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\PlaybackEngines\\WindowsStandaloneSupport\\Variations\\win64_player_nondevelopment_mono\\UnityCrashHandler64.exe" }}
,{ "pid":12345, "tid":4, "ts":1749474197764241, "dur":114, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"CopyFiles C:/Users/<USER>/Desktop/Computer/Cascade Games/Void Voyager/Build/UnityCrashHandler64.exe" }}
,{ "pid":12345, "tid":4, "ts":1749474197764356, "dur":76, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1749474197764441, "dur":108, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"CopyFiles C:/Users/<USER>/Desktop/Computer/Cascade Games/Void Voyager/Build/MonoBleedingEdge/etc/mono/2.0/DefaultWsdlHelpGenerator.aspx" }}
,{ "pid":12345, "tid":4, "ts":1749474197764550, "dur":239, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1749474197764789, "dur":58, "ph":"X", "name": "EmitNodeUpToDate",  "args": { "detail":"CopyFiles C:/Users/<USER>/Desktop/Computer/Cascade Games/Void Voyager/Build/MonoBleedingEdge/etc/mono/2.0/DefaultWsdlHelpGenerator.aspx" }}
,{ "pid":12345, "tid":4, "ts":1749474197764899, "dur":101, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1749474197765007, "dur":53, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1749474197765070, "dur":53, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1749474197765237, "dur":79, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1749474197765318, "dur":3094, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1749474197758301, "dur":1768, "ph":"X", "name": "FirstLock",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1749474197760070, "dur":4278, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1749474197764357, "dur":122, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"CopyFiles C:/Users/<USER>/Desktop/Computer/Cascade Games/Void Voyager/Build/MonoBleedingEdge/etc/mono/4.5/Browsers/Compat.browser" }}
,{ "pid":12345, "tid":5, "ts":1749474197764480, "dur":261, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1749474197764769, "dur":107, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1749474197764900, "dur":114, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1749474197765023, "dur":52, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1749474197765082, "dur":114, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1749474197765204, "dur":54, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1749474197765262, "dur":3148, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1749474197758677, "dur":1254, "ph":"X", "name": "FirstLock",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1749474197759932, "dur":691, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1749474197760623, "dur":3624, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1749474197764252, "dur":55, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"CopyFiles C:/Users/<USER>/Desktop/Computer/Cascade Games/Void Voyager/Build/MonoBleedingEdge/etc/mono/config" }}
,{ "pid":12345, "tid":6, "ts":1749474197764348, "dur":53, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"CopyFiles C:/Users/<USER>/Desktop/Computer/Cascade Games/Void Voyager/Build/MonoBleedingEdge/etc/mono/4.5/DefaultWsdlHelpGenerator.aspx" }}
,{ "pid":12345, "tid":6, "ts":1749474197764402, "dur":61, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1749474197764475, "dur":53, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"Temp\\StagingArea\\WindowsPlayer.exe" }}
,{ "pid":12345, "tid":6, "ts":1749474197764466, "dur":63, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"CopyFiles C:/Users/<USER>/Desktop/Computer/Cascade Games/Void Voyager/Build/Void Voyager.exe" }}
,{ "pid":12345, "tid":6, "ts":1749474197764530, "dur":53, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1749474197764592, "dur":231, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1749474197764824, "dur":110, "ph":"X", "name": "EmitNodeUpToDate",  "args": { "detail":"CopyFiles C:/Users/<USER>/Desktop/Computer/Cascade Games/Void Voyager/Build/Void Voyager_Data/Managed/System.IO.Compression.dll" }}
,{ "pid":12345, "tid":6, "ts":1749474197764941, "dur":112, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1749474197765082, "dur":56, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1749474197765171, "dur":61, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1749474197765241, "dur":50, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1749474197765296, "dur":3174, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1749474197757875, "dur":1991, "ph":"X", "name": "FirstLock",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1749474197759872, "dur":603, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1749474197760475, "dur":3809, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1749474197764325, "dur":110, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\PlaybackEngines\\WindowsStandaloneSupport\\Variations\\win64_player_nondevelopment_mono\\MonoBleedingEdge\\etc\\mono\\4.5\\web.config" }}
,{ "pid":12345, "tid":7, "ts":1749474197764309, "dur":128, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"CopyFiles C:/Users/<USER>/Desktop/Computer/Cascade Games/Void Voyager/Build/MonoBleedingEdge/etc/mono/4.5/web.config" }}
,{ "pid":12345, "tid":7, "ts":1749474197764438, "dur":98, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1749474197764559, "dur":86, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1749474197764656, "dur":210, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1749474197764871, "dur":58, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1749474197765030, "dur":107, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1749474197765206, "dur":108, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\PlaybackEngines\\WindowsStandaloneSupport\\Variations\\win64_player_nondevelopment_mono\\UnityPlayer.dll" }}
,{ "pid":12345, "tid":7, "ts":1749474197765206, "dur":109, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"CopyFiles C:/Users/<USER>/Desktop/Computer/Cascade Games/Void Voyager/Build/UnityPlayer.dll" }}
,{ "pid":12345, "tid":7, "ts":1749474197765365, "dur":3109, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1749474197757909, "dur":1991, "ph":"X", "name": "FirstLock",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1749474197760015, "dur":934, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1749474197760949, "dur":3284, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1749474197764641, "dur":129, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"ProjectSettings\\BurstAotSettings_StandaloneWindows.json" }}
,{ "pid":12345, "tid":8, "ts":1749474197764842, "dur":67, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Users\\<USER>\\Hydra Games\\Void Voyager\\Library\\PackageCache\\com.unity.burst@1.8.15\\.Runtime\\bcl.exe" }}
,{ "pid":12345, "tid":8, "ts":1749474197764240, "dur":670, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"GenerateNativePluginsForAssemblies Library/Bee/artifacts/WinPlayerBuildProgram/AsyncPluginsFromLinker" }}
,{ "pid":12345, "tid":8, "ts":1749474197764911, "dur":178, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1749474197765534, "dur":216, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"Library\\Bee\\artifacts\\WinPlayerBuildProgram\\AsyncPluginsFromLinker\\x86_64\\lib_burst_generated.dll" }}
,{ "pid":12345, "tid":8, "ts":1749474197765534, "dur":217, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"CopyFiles C:/Users/<USER>/Desktop/Computer/Cascade Games/Void Voyager/Build/Void Voyager_Data/Plugins/x86_64/lib_burst_generated.dll" }}
,{ "pid":12345, "tid":8, "ts":1749474197766714, "dur":1633, "ph":"X", "name": "CopyFiles",  "args": { "detail":"C:/Users/<USER>/Desktop/Computer/Cascade Games/Void Voyager/Build/Void Voyager_Data/Plugins/x86_64/lib_burst_generated.dll" }}
,{ "pid":12345, "tid":9, "ts":1749474197758097, "dur":1820, "ph":"X", "name": "FirstLock",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1749474197759917, "dur":860, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1749474197760778, "dur":3707, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1749474197764500, "dur":83, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"CopyFiles C:/Users/<USER>/Desktop/Computer/Cascade Games/Void Voyager/Build/Void Voyager_Data/Resources/unity default resources" }}
,{ "pid":12345, "tid":9, "ts":1749474197764684, "dur":184, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1749474197764886, "dur":90, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1749474197764997, "dur":133, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1749474197765142, "dur":130, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1749474197765294, "dur":3171, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1749474197758121, "dur":1965, "ph":"X", "name": "FirstLock",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1749474197760086, "dur":4327, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1749474197764433, "dur":80, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\PlaybackEngines\\WindowsStandaloneSupport\\Variations\\win64_player_nondevelopment_mono\\MonoBleedingEdge\\etc\\mono\\2.0\\web.config" }}
,{ "pid":12345, "tid":10, "ts":1749474197764423, "dur":91, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"CopyFiles C:/Users/<USER>/Desktop/Computer/Cascade Games/Void Voyager/Build/MonoBleedingEdge/etc/mono/2.0/web.config" }}
,{ "pid":12345, "tid":10, "ts":1749474197764626, "dur":199, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1749474197764844, "dur":54, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Users\\<USER>\\Hydra Games\\Void Voyager\\Library\\Bee\\PlayerScriptAssemblies\\Unity.2D.PixelPerfect.dll" }}
,{ "pid":12345, "tid":10, "ts":1749474197764843, "dur":55, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"CopyFiles C:/Users/<USER>/Desktop/Computer/Cascade Games/Void Voyager/Build/Void Voyager_Data/Managed/Unity.2D.PixelPerfect.dll" }}
,{ "pid":12345, "tid":10, "ts":1749474197764948, "dur":132, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1749474197765150, "dur":79, "ph":"X", "name": "EmitNodeUpToDate",  "args": { "detail":"CopyFiles C:/Users/<USER>/Desktop/Computer/Cascade Games/Void Voyager/Build/Void Voyager_Data/Managed/UnityEngine.HotReloadModule.dll" }}
,{ "pid":12345, "tid":10, "ts":1749474197765265, "dur":3143, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":11, "ts":1749474197758284, "dur":1708, "ph":"X", "name": "FirstLock",  "args": { "detail":"" }}
,{ "pid":12345, "tid":11, "ts":1749474197759992, "dur":4431, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":11, "ts":1749474197764432, "dur":53, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\PlaybackEngines\\WindowsStandaloneSupport\\Variations\\win64_player_nondevelopment_mono\\MonoBleedingEdge\\etc\\mono\\2.0\\machine.config" }}
,{ "pid":12345, "tid":11, "ts":1749474197764428, "dur":58, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"CopyFiles C:/Users/<USER>/Desktop/Computer/Cascade Games/Void Voyager/Build/MonoBleedingEdge/etc/mono/2.0/machine.config" }}
,{ "pid":12345, "tid":11, "ts":1749474197764533, "dur":190, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":11, "ts":1749474197764886, "dur":162, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":11, "ts":1749474197765254, "dur":3152, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":12, "ts":1749474197758475, "dur":1526, "ph":"X", "name": "FirstLock",  "args": { "detail":"" }}
,{ "pid":12345, "tid":12, "ts":1749474197760001, "dur":4440, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":12, "ts":1749474197764448, "dur":186, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"CopyFiles C:/Users/<USER>/Desktop/Computer/Cascade Games/Void Voyager/Build/MonoBleedingEdge/etc/mono/2.0/Browsers/Compat.browser" }}
,{ "pid":12345, "tid":12, "ts":1749474197764688, "dur":51, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":12, "ts":1749474197764745, "dur":142, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":12, "ts":1749474197764950, "dur":189, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":12, "ts":1749474197765139, "dur":73, "ph":"X", "name": "EmitNodeUpToDate",  "args": { "detail":"CopyFiles C:/Users/<USER>/Desktop/Computer/Cascade Games/Void Voyager/Build/Void Voyager_Data/Managed/UnityEngine.UnityWebRequestAssetBundleModule.dll" }}
,{ "pid":12345, "tid":12, "ts":1749474197765216, "dur":62, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":12, "ts":1749474197765297, "dur":3142, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":13, "ts":1749474197758560, "dur":1428, "ph":"X", "name": "FirstLock",  "args": { "detail":"" }}
,{ "pid":12345, "tid":13, "ts":1749474197759989, "dur":4471, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":13, "ts":1749474197764481, "dur":81, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\PlaybackEngines\\WindowsStandaloneSupport\\Variations\\win64_player_nondevelopment_mono\\MonoBleedingEdge\\EmbedRuntime\\mono-2.0-bdwgc.dll" }}
,{ "pid":12345, "tid":13, "ts":1749474197764469, "dur":95, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"CopyFiles C:/Users/<USER>/Desktop/Computer/Cascade Games/Void Voyager/Build/MonoBleedingEdge/EmbedRuntime/mono-2.0-bdwgc.dll" }}
,{ "pid":12345, "tid":13, "ts":1749474197764564, "dur":171, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":13, "ts":1749474197764793, "dur":92, "ph":"X", "name": "EmitNodeUpToDate",  "args": { "detail":"CopyFiles C:/Users/<USER>/Desktop/Computer/Cascade Games/Void Voyager/Build/Void Voyager_Data/Managed/Unity.2D.Common.Runtime.dll" }}
,{ "pid":12345, "tid":13, "ts":1749474197764889, "dur":173, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":13, "ts":1749474197765072, "dur":88, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":13, "ts":1749474197765166, "dur":81, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":13, "ts":1749474197765249, "dur":3168, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":14, "ts":1749474197758451, "dur":1528, "ph":"X", "name": "FirstLock",  "args": { "detail":"" }}
,{ "pid":12345, "tid":14, "ts":1749474197759979, "dur":137, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":14, "ts":1749474197760117, "dur":4207, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":14, "ts":1749474197764343, "dur":94, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\PlaybackEngines\\WindowsStandaloneSupport\\Variations\\win64_player_nondevelopment_mono\\MonoBleedingEdge\\etc\\mono\\4.5\\machine.config" }}
,{ "pid":12345, "tid":14, "ts":1749474197764334, "dur":105, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"CopyFiles C:/Users/<USER>/Desktop/Computer/Cascade Games/Void Voyager/Build/MonoBleedingEdge/etc/mono/4.5/machine.config" }}
,{ "pid":12345, "tid":14, "ts":1749474197764440, "dur":53, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":14, "ts":1749474197764736, "dur":54, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":14, "ts":1749474197764790, "dur":73, "ph":"X", "name": "EmitNodeUpToDate",  "args": { "detail":"CopyFiles C:/Users/<USER>/Desktop/Computer/Cascade Games/Void Voyager/Build/Void Voyager_Data/Managed/Unity.RenderPipeline.Universal.ShaderLibrary.dll" }}
,{ "pid":12345, "tid":14, "ts":1749474197764932, "dur":81, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":14, "ts":1749474197765119, "dur":81, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":14, "ts":1749474197765311, "dur":3170, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":15, "ts":1749474197758493, "dur":1516, "ph":"X", "name": "FirstLock",  "args": { "detail":"" }}
,{ "pid":12345, "tid":15, "ts":1749474197760041, "dur":4331, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":15, "ts":1749474197764384, "dur":58, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\PlaybackEngines\\WindowsStandaloneSupport\\Variations\\win64_player_nondevelopment_mono\\MonoBleedingEdge\\etc\\mono\\4.0\\machine.config" }}
,{ "pid":12345, "tid":15, "ts":1749474197764378, "dur":65, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"CopyFiles C:/Users/<USER>/Desktop/Computer/Cascade Games/Void Voyager/Build/MonoBleedingEdge/etc/mono/4.0/machine.config" }}
,{ "pid":12345, "tid":15, "ts":1749474197764444, "dur":66, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":15, "ts":1749474197764744, "dur":145, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":15, "ts":1749474197764902, "dur":182, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":15, "ts":1749474197765089, "dur":127, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":15, "ts":1749474197765227, "dur":84, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":15, "ts":1749474197765315, "dur":3110, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":16, "ts":1749474197758519, "dur":1461, "ph":"X", "name": "FirstLock",  "args": { "detail":"" }}
,{ "pid":12345, "tid":16, "ts":1749474197759981, "dur":4636, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":16, "ts":1749474197764805, "dur":193, "ph":"X", "name": "EmitNodeUpToDate",  "args": { "detail":"CopyFiles C:/Users/<USER>/Desktop/Computer/Cascade Games/Void Voyager/Build/Void Voyager_Data/Managed/Unity.Burst.dll" }}
,{ "pid":12345, "tid":16, "ts":1749474197765139, "dur":60, "ph":"X", "name": "EmitNodeUpToDate",  "args": { "detail":"CopyFiles C:/Users/<USER>/Desktop/Computer/Cascade Games/Void Voyager/Build/Void Voyager_Data/Managed/UnityEngine.JSONSerializeModule.dll" }}
,{ "pid":12345, "tid":16, "ts":1749474197765202, "dur":78, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":16, "ts":1749474197765292, "dur":3148, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":17, "ts":1749474197758536, "dur":1447, "ph":"X", "name": "FirstLock",  "args": { "detail":"" }}
,{ "pid":12345, "tid":17, "ts":1749474197759984, "dur":4574, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":17, "ts":1749474197764577, "dur":164, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":17, "ts":1749474197764748, "dur":212, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":17, "ts":1749474197765036, "dur":90, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":17, "ts":1749474197765133, "dur":78, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":17, "ts":1749474197765263, "dur":3159, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":18, "ts":1749474197758589, "dur":1355, "ph":"X", "name": "FirstLock",  "args": { "detail":"" }}
,{ "pid":12345, "tid":18, "ts":1749474197759945, "dur":753, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":18, "ts":1749474197760699, "dur":3530, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":18, "ts":1749474197764341, "dur":53, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":18, "ts":1749474197764397, "dur":94, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"CopyFiles C:/Users/<USER>/Desktop/Computer/Cascade Games/Void Voyager/Build/MonoBleedingEdge/etc/mono/4.0/Browsers/Compat.browser" }}
,{ "pid":12345, "tid":18, "ts":1749474197764492, "dur":51, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":18, "ts":1749474197764806, "dur":118, "ph":"X", "name": "EmitNodeUpToDate",  "args": { "detail":"CopyFiles C:/Users/<USER>/Desktop/Computer/Cascade Games/Void Voyager/Build/Void Voyager_Data/Managed/Unity.VisualScripting.Core.dll" }}
,{ "pid":12345, "tid":18, "ts":1749474197764988, "dur":111, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":18, "ts":1749474197765113, "dur":184, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":18, "ts":1749474197765298, "dur":3117, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":19, "ts":1749474197758614, "dur":1328, "ph":"X", "name": "FirstLock",  "args": { "detail":"" }}
,{ "pid":12345, "tid":19, "ts":1749474197759942, "dur":691, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":19, "ts":1749474197760634, "dur":3675, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":19, "ts":1749474197764421, "dur":87, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"CopyFiles C:/Users/<USER>/Desktop/Computer/Cascade Games/Void Voyager/Build/MonoBleedingEdge/etc/mono/2.0/settings.map" }}
,{ "pid":12345, "tid":19, "ts":1749474197764508, "dur":54, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":19, "ts":1749474197764587, "dur":188, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":19, "ts":1749474197764782, "dur":92, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":19, "ts":1749474197765266, "dur":3213, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":20, "ts":1749474197758643, "dur":1291, "ph":"X", "name": "FirstLock",  "args": { "detail":"" }}
,{ "pid":12345, "tid":20, "ts":1749474197759935, "dur":819, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":20, "ts":1749474197760754, "dur":3486, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":20, "ts":1749474197764256, "dur":70, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\PlaybackEngines\\WindowsStandaloneSupport\\Variations\\win64_player_nondevelopment_mono\\MonoBleedingEdge\\etc\\mono\\mconfig\\config.xml" }}
,{ "pid":12345, "tid":20, "ts":1749474197764246, "dur":81, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"CopyFiles C:/Users/<USER>/Desktop/Computer/Cascade Games/Void Voyager/Build/MonoBleedingEdge/etc/mono/mconfig/config.xml" }}
,{ "pid":12345, "tid":20, "ts":1749474197764365, "dur":81, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\PlaybackEngines\\WindowsStandaloneSupport\\Variations\\win64_player_nondevelopment_mono\\MonoBleedingEdge\\etc\\mono\\4.0\\web.config" }}
,{ "pid":12345, "tid":20, "ts":1749474197764363, "dur":84, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"CopyFiles C:/Users/<USER>/Desktop/Computer/Cascade Games/Void Voyager/Build/MonoBleedingEdge/etc/mono/4.0/web.config" }}
,{ "pid":12345, "tid":20, "ts":1749474197764448, "dur":383, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":20, "ts":1749474197764945, "dur":77, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":20, "ts":1749474197765141, "dur":100, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":20, "ts":1749474197765243, "dur":3164, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":0, "ts":1749474197772688, "dur":565, "ph":"X", "name": "ProfilerWriteOutput" }
,{ "cat":"", "pid":12345, "tid":0, "ts":0, "ph":"M", "name":"process_name", "args": { "name":"bee_backend" } }
,{ "pid":12345, "tid":0, "ts":1749474192585810, "dur":25360, "ph":"X", "name": "DriverInitData",  "args": { "detail":"" }}
,{ "pid":12345, "tid":0, "ts":1749474192611176, "dur":2186, "ph":"X", "name": "RemoveStaleOutputs",  "args": { "detail":"" }}
,{ "pid":12345, "tid":0, "ts":1749474192613387, "dur":510, "ph":"X", "name": "BuildQueueInit",  "args": { "detail":"" }}
,{ "pid":12345, "tid":0, "ts":1749474192614158, "dur":51, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"CopyFiles C:/Users/<USER>/Desktop/Computer/Cascade Games/Void Voyager/Build/Void Voyager_Data/Managed/UnityEngine.AIModule.dll" }}
,{ "pid":12345, "tid":0, "ts":1749474192614482, "dur":66, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"CopyFiles C:/Users/<USER>/Desktop/Computer/Cascade Games/Void Voyager/Build/Void Voyager_Data/Managed/UnityEngine.InputLegacyModule.dll" }}
,{ "pid":12345, "tid":0, "ts":1749474192615290, "dur":51, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"CopyFiles C:/Users/<USER>/Desktop/Computer/Cascade Games/Void Voyager/Build/Void Voyager_Data/Managed/Unity.RenderPipelines.Universal.Runtime.dll" }}
,{ "pid":12345, "tid":0, "ts":1749474192615954, "dur":50, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"CopyFiles C:/Users/<USER>/Desktop/Computer/Cascade Games/Void Voyager/Build/MonoBleedingEdge/etc/mono/4.5/DefaultWsdlHelpGenerator.aspx" }}
,{ "pid":12345, "tid":0, "ts":1749474192613915, "dur":2189, "ph":"X", "name": "EnqueueRequestedNodes",  "args": { "detail":"" }}
,{ "pid":12345, "tid":0, "ts":1749474192616106, "dur":4929357, "ph":"X", "name": "WaitForBuildFinished", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":0, "ts":1749474197545467, "dur":722, "ph":"X", "name": "JoinBuildThread",  "args": { "detail":"" }}
,{ "pid":12345, "tid":0, "ts":1749474197546211, "dur":75, "ph":"X", "name": "JoinBuildThread",  "args": { "detail":"" }}
,{ "pid":12345, "tid":0, "ts":1749474197546327, "dur":67, "ph":"X", "name": "JoinBuildThread",  "args": { "detail":"" }}
,{ "pid":12345, "tid":0, "ts":1749474197546578, "dur":2714, "ph":"X", "name": "Tundra",  "args": { "detail":"Write AllBuiltNodes" }}
,{ "pid":12345, "tid":1, "ts":1749474192615262, "dur":1049, "ph":"X", "name": "FirstLock",  "args": { "detail":"" }}
,{ "pid":12345, "tid":1, "ts":1749474192616311, "dur":3186, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":1, "ts":1749474192619499, "dur":213, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"CopyFiles C:/Users/<USER>/Desktop/Computer/Cascade Games/Void Voyager/Build/MonoBleedingEdge/etc/mono/4.5/settings.map" }}
,{ "pid":12345, "tid":1, "ts":1749474192619713, "dur":937, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":1, "ts":1749474192620659, "dur":3380, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\MonoBleedingEdge\\lib\\mono\\unityjit-win32\\mscorlib.dll" }}
,{ "pid":12345, "tid":1, "ts":1749474192620654, "dur":3386, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"CopyFiles C:/Users/<USER>/Desktop/Computer/Cascade Games/Void Voyager/Build/Void Voyager_Data/Managed/mscorlib.dll" }}
,{ "pid":12345, "tid":1, "ts":1749474192624041, "dur":1373, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":1, "ts":1749474192625435, "dur":1296, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\MonoBleedingEdge\\lib\\mono\\unityjit-win32\\System.Runtime.Serialization.dll" }}
,{ "pid":12345, "tid":1, "ts":1749474192625434, "dur":1298, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"CopyFiles C:/Users/<USER>/Desktop/Computer/Cascade Games/Void Voyager/Build/Void Voyager_Data/Managed/System.Runtime.Serialization.dll" }}
,{ "pid":12345, "tid":1, "ts":1749474192626732, "dur":167, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":1, "ts":1749474192626908, "dur":314, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":1, "ts":1749474192627231, "dur":130, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":1, "ts":1749474192627375, "dur":277, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":1, "ts":1749474192627663, "dur":551, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":1, "ts":1749474192628224, "dur":226, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":1, "ts":1749474192628457, "dur":515, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":1, "ts":1749474192628980, "dur":889, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":1, "ts":1749474192629877, "dur":799, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":1, "ts":1749474192630682, "dur":78, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":1, "ts":1749474192630768, "dur":65, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":1, "ts":1749474192630893, "dur":444, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":1, "ts":1749474192631344, "dur":498, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":1, "ts":1749474192631855, "dur":70, "ph":"X", "name": "EmitNodeStart",  "args": { "detail":"" }}
,{ "pid":12345, "tid":1, "ts":1749474192631929, "dur":5184, "ph":"X", "name": "CopyFiles",  "args": { "detail":"C:/Users/<USER>/Desktop/Computer/Cascade Games/Void Voyager/Build/Void Voyager_Data/level0" }}
,{ "pid":12345, "tid":1, "ts":1749474192637167, "dur":4908263, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1749474192614684, "dur":1453, "ph":"X", "name": "FirstLock",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1749474192616143, "dur":671, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1749474192616815, "dur":2710, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1749474192619558, "dur":13591, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\PlaybackEngines\\WindowsStandaloneSupport\\Variations\\win64_player_nondevelopment_mono\\MonoBleedingEdge\\etc\\mono\\2.0\\web.config" }}
,{ "pid":12345, "tid":2, "ts":1749474192619528, "dur":13623, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"CopyFiles C:/Users/<USER>/Desktop/Computer/Cascade Games/Void Voyager/Build/MonoBleedingEdge/etc/mono/2.0/web.config" }}
,{ "pid":12345, "tid":2, "ts":1749474192633152, "dur":80, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1749474192633243, "dur":4912215, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1749474192614890, "dur":1278, "ph":"X", "name": "FirstLock",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1749474192616169, "dur":877, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1749474192617046, "dur":2459, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1749474192619506, "dur":109, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"CopyFiles C:/Users/<USER>/Desktop/Computer/Cascade Games/Void Voyager/Build/MonoBleedingEdge/etc/mono/4.5/Browsers/Compat.browser" }}
,{ "pid":12345, "tid":3, "ts":1749474192619616, "dur":1062, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1749474192620694, "dur":2123, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\MonoBleedingEdge\\lib\\mono\\unityjit-win32\\System.dll" }}
,{ "pid":12345, "tid":3, "ts":1749474192620685, "dur":2134, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"CopyFiles C:/Users/<USER>/Desktop/Computer/Cascade Games/Void Voyager/Build/Void Voyager_Data/Managed/System.dll" }}
,{ "pid":12345, "tid":3, "ts":1749474192622820, "dur":3322, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1749474192626190, "dur":1276, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\MonoBleedingEdge\\lib\\mono\\unityjit-win32\\System.Configuration.dll" }}
,{ "pid":12345, "tid":3, "ts":1749474192626189, "dur":1278, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"CopyFiles C:/Users/<USER>/Desktop/Computer/Cascade Games/Void Voyager/Build/Void Voyager_Data/Managed/System.Configuration.dll" }}
,{ "pid":12345, "tid":3, "ts":1749474192627468, "dur":195, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1749474192627673, "dur":750, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1749474192628430, "dur":539, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1749474192628978, "dur":494, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1749474192629516, "dur":98, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1749474192629622, "dur":689, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1749474192630337, "dur":265, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1749474192630607, "dur":117, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1749474192630733, "dur":105, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1749474192630846, "dur":96, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1749474192630950, "dur":835, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1749474192631814, "dur":7243, "ph":"X", "name": "CopyFiles",  "args": { "detail":"C:/Users/<USER>/Desktop/Computer/Cascade Games/Void Voyager/Build/Void Voyager_Data/sharedassets0.assets" }}
,{ "pid":12345, "tid":3, "ts":1749474192639108, "dur":4906347, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1749474192615345, "dur":933, "ph":"X", "name": "FirstLock",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1749474192616279, "dur":3223, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1749474192619505, "dur":266, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"CopyFiles C:/Users/<USER>/Desktop/Computer/Cascade Games/Void Voyager/Build/MonoBleedingEdge/etc/mono/4.5/DefaultWsdlHelpGenerator.aspx" }}
,{ "pid":12345, "tid":4, "ts":1749474192619771, "dur":842, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1749474192620619, "dur":179, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"CopyFiles C:/Users/<USER>/Desktop/Computer/Cascade Games/Void Voyager/Build/Void Voyager_Data/Resources/unity default resources" }}
,{ "pid":12345, "tid":4, "ts":1749474192620799, "dur":3345, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1749474192624171, "dur":341, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\MonoBleedingEdge\\lib\\mono\\unityjit-win32\\System.ServiceModel.Internals.dll" }}
,{ "pid":12345, "tid":4, "ts":1749474192624160, "dur":353, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"CopyFiles C:/Users/<USER>/Desktop/Computer/Cascade Games/Void Voyager/Build/Void Voyager_Data/Managed/System.ServiceModel.Internals.dll" }}
,{ "pid":12345, "tid":4, "ts":1749474192624514, "dur":902, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1749474192625439, "dur":235, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\MonoBleedingEdge\\lib\\mono\\unityjit-win32\\System.Numerics.dll" }}
,{ "pid":12345, "tid":4, "ts":1749474192625438, "dur":236, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"CopyFiles C:/Users/<USER>/Desktop/Computer/Cascade Games/Void Voyager/Build/Void Voyager_Data/Managed/System.Numerics.dll" }}
,{ "pid":12345, "tid":4, "ts":1749474192625675, "dur":243, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1749474192625932, "dur":517, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\MonoBleedingEdge\\lib\\mono\\unityjit-win32\\System.Drawing.dll" }}
,{ "pid":12345, "tid":4, "ts":1749474192625931, "dur":519, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"CopyFiles C:/Users/<USER>/Desktop/Computer/Cascade Games/Void Voyager/Build/Void Voyager_Data/Managed/System.Drawing.dll" }}
,{ "pid":12345, "tid":4, "ts":1749474192626450, "dur":140, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1749474192626599, "dur":370, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1749474192626977, "dur":231, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1749474192627221, "dur":85, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1749474192627312, "dur":91, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1749474192627410, "dur":248, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1749474192627669, "dur":575, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1749474192628253, "dur":107, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1749474192628368, "dur":223, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1749474192628601, "dur":358, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1749474192628970, "dur":502, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1749474192629524, "dur":786, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1749474192630343, "dur":244, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1749474192630597, "dur":181, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1749474192630787, "dur":150, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1749474192630945, "dur":805, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1749474192631828, "dur":54, "ph":"X", "name": "EmitNodeStart",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1749474192631886, "dur":8446, "ph":"X", "name": "CopyFiles",  "args": { "detail":"C:/Users/<USER>/Desktop/Computer/Cascade Games/Void Voyager/Build/Void Voyager_Data/sharedassets0.assets.resS" }}
,{ "pid":12345, "tid":4, "ts":1749474192640376, "dur":4905061, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1749474192615757, "dur":531, "ph":"X", "name": "FirstLock",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1749474192616289, "dur":3221, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1749474192619541, "dur":11584, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\PlaybackEngines\\WindowsStandaloneSupport\\Variations\\win64_player_nondevelopment_mono\\MonoBleedingEdge\\etc\\mono\\4.0\\machine.config" }}
,{ "pid":12345, "tid":5, "ts":1749474192619512, "dur":11614, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"CopyFiles C:/Users/<USER>/Desktop/Computer/Cascade Games/Void Voyager/Build/MonoBleedingEdge/etc/mono/4.0/machine.config" }}
,{ "pid":12345, "tid":5, "ts":1749474192631127, "dur":83, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1749474192631218, "dur":631, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1749474192631902, "dur":7412, "ph":"X", "name": "CopyFiles",  "args": { "detail":"C:/Users/<USER>/Desktop/Computer/Cascade Games/Void Voyager/Build/Void Voyager_Data/globalgamemanagers.assets" }}
,{ "pid":12345, "tid":5, "ts":1749474192639364, "dur":4906176, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1749474192614385, "dur":1738, "ph":"X", "name": "FirstLock",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1749474192616132, "dur":1015, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1749474192617148, "dur":2395, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1749474192619544, "dur":157, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"CopyFiles C:/Users/<USER>/Desktop/Computer/Cascade Games/Void Voyager/Build/MonoBleedingEdge/etc/mono/2.0/settings.map" }}
,{ "pid":12345, "tid":6, "ts":1749474192619701, "dur":763, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1749474192620483, "dur":74280, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\PlaybackEngines\\WindowsStandaloneSupport\\Variations\\win64_player_nondevelopment_mono\\MonoBleedingEdge\\EmbedRuntime\\MonoPosixHelper.dll" }}
,{ "pid":12345, "tid":6, "ts":1749474192620478, "dur":74287, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"CopyFiles C:/Users/<USER>/Desktop/Computer/Cascade Games/Void Voyager/Build/MonoBleedingEdge/EmbedRuntime/MonoPosixHelper.dll" }}
,{ "pid":12345, "tid":6, "ts":1749474192694766, "dur":85, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1749474192694860, "dur":4850631, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1749474192614570, "dur":1560, "ph":"X", "name": "FirstLock",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1749474192616134, "dur":900, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1749474192617034, "dur":2460, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1749474192619495, "dur":101, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"CopyFiles C:/Users/<USER>/Desktop/Computer/Cascade Games/Void Voyager/Build/MonoBleedingEdge/etc/mono/config" }}
,{ "pid":12345, "tid":7, "ts":1749474192619597, "dur":435, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1749474192620042, "dur":79, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"CopyFiles C:/Users/<USER>/Desktop/Computer/Cascade Games/Void Voyager/Build/MonoBleedingEdge/etc/mono/2.0/DefaultWsdlHelpGenerator.aspx" }}
,{ "pid":12345, "tid":7, "ts":1749474192620122, "dur":411, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1749474192620551, "dur":114400, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\PlaybackEngines\\WindowsStandaloneSupport\\Variations\\win64_player_nondevelopment_mono\\MonoBleedingEdge\\EmbedRuntime\\mono-2.0-bdwgc.dll" }}
,{ "pid":12345, "tid":7, "ts":1749474192620541, "dur":114412, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"CopyFiles C:/Users/<USER>/Desktop/Computer/Cascade Games/Void Voyager/Build/MonoBleedingEdge/EmbedRuntime/mono-2.0-bdwgc.dll" }}
,{ "pid":12345, "tid":7, "ts":1749474192734954, "dur":93, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1749474192735057, "dur":4810396, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1749474192614838, "dur":1313, "ph":"X", "name": "FirstLock",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1749474192616263, "dur":869, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1749474192617132, "dur":2386, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1749474192619554, "dur":190, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"CopyFiles C:/Users/<USER>/Desktop/Computer/Cascade Games/Void Voyager/Build/MonoBleedingEdge/etc/mono/4.0/Browsers/Compat.browser" }}
,{ "pid":12345, "tid":8, "ts":1749474192619745, "dur":1073, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1749474192620819, "dur":97, "ph":"X", "name": "EmitNodeUpToDate",  "args": { "detail":"CopyFiles C:/Users/<USER>/Desktop/Computer/Cascade Games/Void Voyager/Build/MonoBleedingEdge/etc/mono/4.0/Browsers/Compat.browser" }}
,{ "pid":12345, "tid":8, "ts":1749474192620925, "dur":145, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\MonoBleedingEdge\\lib\\mono\\unityjit-win32\\System.Transactions.dll" }}
,{ "pid":12345, "tid":8, "ts":1749474192620917, "dur":153, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"CopyFiles C:/Users/<USER>/Desktop/Computer/Cascade Games/Void Voyager/Build/Void Voyager_Data/Managed/System.Transactions.dll" }}
,{ "pid":12345, "tid":8, "ts":1749474192621071, "dur":4341, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1749474192625435, "dur":421, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\MonoBleedingEdge\\lib\\mono\\unityjit-win32\\System.Security.dll" }}
,{ "pid":12345, "tid":8, "ts":1749474192625433, "dur":424, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"CopyFiles C:/Users/<USER>/Desktop/Computer/Cascade Games/Void Voyager/Build/Void Voyager_Data/Managed/System.Security.dll" }}
,{ "pid":12345, "tid":8, "ts":1749474192625858, "dur":100, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1749474192625964, "dur":2138, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\MonoBleedingEdge\\lib\\mono\\unityjit-win32\\System.Data.dll" }}
,{ "pid":12345, "tid":8, "ts":1749474192625963, "dur":2140, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"CopyFiles C:/Users/<USER>/Desktop/Computer/Cascade Games/Void Voyager/Build/Void Voyager_Data/Managed/System.Data.dll" }}
,{ "pid":12345, "tid":8, "ts":1749474192628104, "dur":792, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1749474192628905, "dur":293, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1749474192629212, "dur":290, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1749474192629511, "dur":86, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1749474192629604, "dur":884, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1749474192630499, "dur":1343, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1749474192631857, "dur":76, "ph":"X", "name": "EmitNodeStart",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1749474192631938, "dur":6838, "ph":"X", "name": "CopyFiles",  "args": { "detail":"C:/Users/<USER>/Desktop/Computer/Cascade Games/Void Voyager/Build/Void Voyager_Data/resources.assets" }}
,{ "pid":12345, "tid":8, "ts":1749474192638831, "dur":4906676, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1749474192614987, "dur":1286, "ph":"X", "name": "FirstLock",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1749474192616274, "dur":3240, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1749474192619516, "dur":161, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"CopyFiles C:/Users/<USER>/Desktop/Computer/Cascade Games/Void Voyager/Build/MonoBleedingEdge/etc/mono/4.0/DefaultWsdlHelpGenerator.aspx" }}
,{ "pid":12345, "tid":9, "ts":1749474192619677, "dur":1137, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1749474192620850, "dur":229, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\MonoBleedingEdge\\lib\\mono\\unityjit-win32\\System.Xml.Linq.dll" }}
,{ "pid":12345, "tid":9, "ts":1749474192620842, "dur":238, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"CopyFiles C:/Users/<USER>/Desktop/Computer/Cascade Games/Void Voyager/Build/Void Voyager_Data/Managed/System.Xml.Linq.dll" }}
,{ "pid":12345, "tid":9, "ts":1749474192621080, "dur":4716, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1749474192625802, "dur":408, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\MonoBleedingEdge\\lib\\mono\\unityjit-win32\\System.IO.Compression.FileSystem.dll" }}
,{ "pid":12345, "tid":9, "ts":1749474192625801, "dur":410, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"CopyFiles C:/Users/<USER>/Desktop/Computer/Cascade Games/Void Voyager/Build/Void Voyager_Data/Managed/System.IO.Compression.FileSystem.dll" }}
,{ "pid":12345, "tid":9, "ts":1749474192626211, "dur":338, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1749474192626558, "dur":443, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1749474192627002, "dur":75, "ph":"X", "name": "EmitNodeUpToDate",  "args": { "detail":"CopyFiles C:/Users/<USER>/Desktop/Computer/Cascade Games/Void Voyager/Build/Void Voyager_Data/Managed/Unity.VisualScripting.Antlr3.Runtime.dll" }}
,{ "pid":12345, "tid":9, "ts":1749474192627091, "dur":426, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1749474192627525, "dur":384, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1749474192627916, "dur":959, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1749474192628885, "dur":102, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1749474192628997, "dur":473, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1749474192629498, "dur":276, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1749474192629790, "dur":824, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1749474192630624, "dur":162, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1749474192630794, "dur":104, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1749474192630906, "dur":432, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1749474192631347, "dur":50418, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\PlaybackEngines\\WindowsStandaloneSupport\\Variations\\win64_player_nondevelopment_mono\\UnityPlayer.dll" }}
,{ "pid":12345, "tid":9, "ts":1749474192631346, "dur":50421, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"CopyFiles C:/Users/<USER>/Desktop/Computer/Cascade Games/Void Voyager/Build/UnityPlayer.dll" }}
,{ "pid":12345, "tid":9, "ts":1749474192681768, "dur":337, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1749474192682117, "dur":4863393, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1749474192615099, "dur":1170, "ph":"X", "name": "FirstLock",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1749474192616270, "dur":3225, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1749474192619496, "dur":118, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"CopyFiles C:/Users/<USER>/Desktop/Computer/Cascade Games/Void Voyager/Build/MonoBleedingEdge/etc/mono/browscap.ini" }}
,{ "pid":12345, "tid":10, "ts":1749474192619615, "dur":580, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1749474192620204, "dur":84, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"CopyFiles C:/Users/<USER>/Desktop/Computer/Cascade Games/Void Voyager/Build/MonoBleedingEdge/etc/mono/2.0/Browsers/Compat.browser" }}
,{ "pid":12345, "tid":10, "ts":1749474192620288, "dur":275, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1749474192620573, "dur":67606, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"Temp\\StagingArea\\WindowsPlayer.exe" }}
,{ "pid":12345, "tid":10, "ts":1749474192620567, "dur":67614, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"CopyFiles C:/Users/<USER>/Desktop/Computer/Cascade Games/Void Voyager/Build/Void Voyager.exe" }}
,{ "pid":12345, "tid":10, "ts":1749474192688252, "dur":1241, "ph":"X", "name": "CopyFiles",  "args": { "detail":"C:/Users/<USER>/Desktop/Computer/Cascade Games/Void Voyager/Build/Void Voyager.exe" }}
,{ "pid":12345, "tid":10, "ts":1749474192689514, "dur":4855948, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":11, "ts":1749474192615207, "dur":1058, "ph":"X", "name": "FirstLock",  "args": { "detail":"" }}
,{ "pid":12345, "tid":11, "ts":1749474192616265, "dur":3228, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":11, "ts":1749474192619508, "dur":29791, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\PlaybackEngines\\WindowsStandaloneSupport\\Variations\\win64_player_nondevelopment_mono\\MonoBleedingEdge\\etc\\mono\\mconfig\\config.xml" }}
,{ "pid":12345, "tid":11, "ts":1749474192619496, "dur":29804, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"CopyFiles C:/Users/<USER>/Desktop/Computer/Cascade Games/Void Voyager/Build/MonoBleedingEdge/etc/mono/mconfig/config.xml" }}
,{ "pid":12345, "tid":11, "ts":1749474192649302, "dur":111, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":11, "ts":1749474192649424, "dur":4896044, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":12, "ts":1749474192615236, "dur":1065, "ph":"X", "name": "FirstLock",  "args": { "detail":"" }}
,{ "pid":12345, "tid":12, "ts":1749474192616301, "dur":3190, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":12, "ts":1749474192619524, "dur":5809, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\PlaybackEngines\\WindowsStandaloneSupport\\Variations\\win64_player_nondevelopment_mono\\UnityCrashHandler64.exe" }}
,{ "pid":12345, "tid":12, "ts":1749474192619495, "dur":5840, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"CopyFiles C:/Users/<USER>/Desktop/Computer/Cascade Games/Void Voyager/Build/UnityCrashHandler64.exe" }}
,{ "pid":12345, "tid":12, "ts":1749474192625336, "dur":407, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":12, "ts":1749474192625768, "dur":168, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\MonoBleedingEdge\\lib\\mono\\unityjit-win32\\System.IO.Compression.dll" }}
,{ "pid":12345, "tid":12, "ts":1749474192625767, "dur":170, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"CopyFiles C:/Users/<USER>/Desktop/Computer/Cascade Games/Void Voyager/Build/Void Voyager_Data/Managed/System.IO.Compression.dll" }}
,{ "pid":12345, "tid":12, "ts":1749474192625937, "dur":113, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":12, "ts":1749474192626058, "dur":103, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\MonoBleedingEdge\\lib\\mono\\unityjit-win32\\System.Data.DataSetExtensions.dll" }}
,{ "pid":12345, "tid":12, "ts":1749474192626057, "dur":104, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"CopyFiles C:/Users/<USER>/Desktop/Computer/Cascade Games/Void Voyager/Build/Void Voyager_Data/Managed/System.Data.DataSetExtensions.dll" }}
,{ "pid":12345, "tid":12, "ts":1749474192626162, "dur":60, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":12, "ts":1749474192626229, "dur":50, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":12, "ts":1749474192626338, "dur":68, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":12, "ts":1749474192626410, "dur":232, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":12, "ts":1749474192626650, "dur":158, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":12, "ts":1749474192626817, "dur":69, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":12, "ts":1749474192626939, "dur":297, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":12, "ts":1749474192627241, "dur":121, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":12, "ts":1749474192627366, "dur":518, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":12, "ts":1749474192627896, "dur":712, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":12, "ts":1749474192628627, "dur":333, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":12, "ts":1749474192628971, "dur":534, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":12, "ts":1749474192629514, "dur":101, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":12, "ts":1749474192629625, "dur":684, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":12, "ts":1749474192630322, "dur":313, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":12, "ts":1749474192630644, "dur":117, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":12, "ts":1749474192630771, "dur":120, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":12, "ts":1749474192630896, "dur":930, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":12, "ts":1749474192631847, "dur":7758, "ph":"X", "name": "CopyFiles",  "args": { "detail":"C:/Users/<USER>/Desktop/Computer/Cascade Games/Void Voyager/Build/Void Voyager_Data/resources.assets.resS" }}
,{ "pid":12345, "tid":12, "ts":1749474192639634, "dur":4905812, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":13, "ts":1749474192614324, "dur":1788, "ph":"X", "name": "FirstLock",  "args": { "detail":"" }}
,{ "pid":12345, "tid":13, "ts":1749474192616118, "dur":3364, "ph":"X", "name": "CheckDagSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":13, "ts":1749474192619496, "dur":99, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"WriteText Library/Bee/artifacts/csharpactions/boot.config_tyr4.info" }}
,{ "pid":12345, "tid":13, "ts":1749474192619596, "dur":178, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":13, "ts":1749474192621341, "dur":645, "ph":"X", "name": "WriteText",  "args": { "detail":"Library/Bee/artifacts/csharpactions/boot.config_tyr4.info" }}
,{ "pid":12345, "tid":13, "ts":1749474192621990, "dur":122, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"Library\\PlayerDataCache\\Win64\\Data\\boot.config" }}
,{ "pid":12345, "tid":13, "ts":1749474192622120, "dur":104, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"Library\\PlayerDataCache\\Win64\\Data\\RuntimeInitializeOnLoads.json" }}
,{ "pid":12345, "tid":13, "ts":1749474192622224, "dur":94, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"Library\\PlayerDataCache\\Win64\\Data\\ScriptingAssemblies.json" }}
,{ "pid":12345, "tid":13, "ts":1749474192622465, "dur":565, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Users\\<USER>\\Hydra Games\\Void Voyager\\Library\\Bee\\PlayerScriptAssemblies\\Unity.RenderPipelines.Universal.Runtime.dll" }}
,{ "pid":12345, "tid":13, "ts":1749474192623032, "dur":270, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Users\\<USER>\\Hydra Games\\Void Voyager\\Library\\Bee\\PlayerScriptAssemblies\\Unity.Burst.dll" }}
,{ "pid":12345, "tid":13, "ts":1749474192623303, "dur":443, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Users\\<USER>\\Hydra Games\\Void Voyager\\Library\\Bee\\PlayerScriptAssemblies\\Unity.VisualScripting.Core.dll" }}
,{ "pid":12345, "tid":13, "ts":1749474192623750, "dur":301, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Users\\<USER>\\Hydra Games\\Void Voyager\\Library\\Bee\\PlayerScriptAssemblies\\UnityEngine.UI.dll" }}
,{ "pid":12345, "tid":13, "ts":1749474192624054, "dur":92, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Users\\<USER>\\Hydra Games\\Void Voyager\\Library\\Bee\\PlayerScriptAssemblies\\Unity.2D.Common.Runtime.dll" }}
,{ "pid":12345, "tid":13, "ts":1749474192624147, "dur":70, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Users\\<USER>\\Hydra Games\\Void Voyager\\Library\\Bee\\PlayerScriptAssemblies\\Unity.RenderPipeline.Universal.ShaderLibrary.dll" }}
,{ "pid":12345, "tid":13, "ts":1749474192624218, "dur":187, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Users\\<USER>\\Hydra Games\\Void Voyager\\Library\\Bee\\PlayerScriptAssemblies\\Unity.Timeline.dll" }}
,{ "pid":12345, "tid":13, "ts":1749474192624406, "dur":603, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Users\\<USER>\\Hydra Games\\Void Voyager\\Library\\Bee\\PlayerScriptAssemblies\\Unity.Mathematics.dll" }}
,{ "pid":12345, "tid":13, "ts":1749474192625010, "dur":86, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Users\\<USER>\\Hydra Games\\Void Voyager\\Library\\Bee\\PlayerScriptAssemblies\\Unity.RenderPipelines.Universal.Shaders.dll" }}
,{ "pid":12345, "tid":13, "ts":1749474192625096, "dur":76, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Users\\<USER>\\Hydra Games\\Void Voyager\\Library\\Bee\\PlayerScriptAssemblies\\Unity.VisualScripting.State.dll" }}
,{ "pid":12345, "tid":13, "ts":1749474192625173, "dur":67, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Users\\<USER>\\Hydra Games\\Void Voyager\\Library\\PackageCache\\com.unity.collections@1.2.4\\Unity.Collections.LowLevel.ILSupport\\Unity.Collections.LowLevel.ILSupport.dll" }}
,{ "pid":12345, "tid":13, "ts":1749474192625241, "dur":118, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Users\\<USER>\\Hydra Games\\Void Voyager\\Library\\PackageCache\\com.unity.visualscripting@1.9.4\\Runtime\\VisualScripting.Flow\\Dependencies\\NCalc\\Unity.VisualScripting.Antlr3.Runtime.dll" }}
,{ "pid":12345, "tid":13, "ts":1749474192625360, "dur":67, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Users\\<USER>\\Hydra Games\\Void Voyager\\Library\\PackageCache\\com.unity.burst@1.8.15\\Unity.Burst.Unsafe.dll" }}
,{ "pid":12345, "tid":13, "ts":1749474192625428, "dur":120, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\MonoBleedingEdge\\lib\\mono\\unityjit-win32\\Facades\\System.Runtime.dll" }}
,{ "pid":12345, "tid":13, "ts":1749474192625549, "dur":137, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\MonoBleedingEdge\\lib\\mono\\unityjit-win32\\Facades\\netstandard.dll" }}
,{ "pid":12345, "tid":13, "ts":1749474192625687, "dur":212, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\MonoBleedingEdge\\lib\\mono\\unityjit-win32\\Mono.Security.dll" }}
,{ "pid":12345, "tid":13, "ts":1749474192625900, "dur":304, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\MonoBleedingEdge\\lib\\mono\\unityjit-win32\\System.ComponentModel.Composition.dll" }}
,{ "pid":12345, "tid":13, "ts":1749474192626205, "dur":159, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\MonoBleedingEdge\\lib\\mono\\unityjit-win32\\System.Configuration.dll" }}
,{ "pid":12345, "tid":13, "ts":1749474192626365, "dur":720, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\MonoBleedingEdge\\lib\\mono\\unityjit-win32\\System.Core.dll" }}
,{ "pid":12345, "tid":13, "ts":1749474192627088, "dur":1741, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\MonoBleedingEdge\\lib\\mono\\unityjit-win32\\System.Data.dll" }}
,{ "pid":12345, "tid":13, "ts":1749474192628861, "dur":189, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"Library\\Bee\\Player7b6476bc-inputdata.json" }}
,{ "pid":12345, "tid":13, "ts":1749474192629098, "dur":77511, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Tools\\BuildPipeline\\Bee.BuildTools.dll" }}
,{ "pid":12345, "tid":13, "ts":1749474192706611, "dur":796, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Tools\\BuildPipeline\\Bee.Core.dll" }}
,{ "pid":12345, "tid":13, "ts":1749474192707408, "dur":234404, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Tools\\BuildPipeline\\Bee.CSharpSupport.dll" }}
,{ "pid":12345, "tid":13, "ts":1749474192941814, "dur":666, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Tools\\BuildPipeline\\Bee.DotNet.dll" }}
,{ "pid":12345, "tid":13, "ts":1749474192942481, "dur":140, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Tools\\BuildPipeline\\Bee.NativeProgramSupport.dll" }}
,{ "pid":12345, "tid":13, "ts":1749474192942626, "dur":534, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Tools\\BuildPipeline\\Bee.Stevedore.Program.exe" }}
,{ "pid":12345, "tid":13, "ts":1749474192943160, "dur":258, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Tools\\BuildPipeline\\Bee.TinyProfiler2.dll" }}
,{ "pid":12345, "tid":13, "ts":1749474192943419, "dur":82111, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Tools\\BuildPipeline\\Bee.Toolchain.GNU.dll" }}
,{ "pid":12345, "tid":13, "ts":1749474193025531, "dur":103442, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Tools\\BuildPipeline\\Bee.Toolchain.LLVM.dll" }}
,{ "pid":12345, "tid":13, "ts":1749474193128976, "dur":356908, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Tools\\BuildPipeline\\Bee.Toolchain.VisualStudio.dll" }}
,{ "pid":12345, "tid":13, "ts":1749474193485886, "dur":276428, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Tools\\BuildPipeline\\Bee.Toolchain.Xcode.dll" }}
,{ "pid":12345, "tid":13, "ts":1749474193762315, "dur":680, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Tools\\BuildPipeline\\Bee.Tools.dll" }}
,{ "pid":12345, "tid":13, "ts":1749474193762997, "dur":520, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Tools\\BuildPipeline\\Bee.TundraBackend.dll" }}
,{ "pid":12345, "tid":13, "ts":1749474193763518, "dur":71, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Tools\\BuildPipeline\\Bee.VisualStudioSolution.dll" }}
,{ "pid":12345, "tid":13, "ts":1749474193763671, "dur":663, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Tools\\BuildPipeline\\BeeLocalCacheTool.exe" }}
,{ "pid":12345, "tid":13, "ts":1749474193764335, "dur":1554, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Tools\\BuildPipeline\\Newtonsoft.Json.dll" }}
,{ "pid":12345, "tid":13, "ts":1749474193765890, "dur":435, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Tools\\BuildPipeline\\NiceIO.dll" }}
,{ "pid":12345, "tid":13, "ts":1749474193766326, "dur":260, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Tools\\BuildPipeline\\PlayerBuildProgramLibrary.Data.dll" }}
,{ "pid":12345, "tid":13, "ts":1749474193766587, "dur":158, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Tools\\BuildPipeline\\PlayerBuildProgramLibrary.dll" }}
,{ "pid":12345, "tid":13, "ts":1749474193766746, "dur":52, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Tools\\BuildPipeline\\ScriptCompilationBuildProgram.Data.dll" }}
,{ "pid":12345, "tid":13, "ts":1749474193766799, "dur":298, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Tools\\BuildPipeline\\ScriptCompilationBuildProgram.exe" }}
,{ "pid":12345, "tid":13, "ts":1749474193767098, "dur":1369, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Tools\\BuildPipeline\\SharpYaml.dll" }}
,{ "pid":12345, "tid":13, "ts":1749474193768468, "dur":346, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Tools\\BuildPipeline\\Unity.Api.Attributes.dll" }}
,{ "pid":12345, "tid":13, "ts":1749474193768815, "dur":3087, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Tools\\BuildPipeline\\Unity.Cecil.dll" }}
,{ "pid":12345, "tid":13, "ts":1749474193771903, "dur":496, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Tools\\BuildPipeline\\Unity.Cecil.Mdb.dll" }}
,{ "pid":12345, "tid":13, "ts":1749474193772400, "dur":611, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Tools\\BuildPipeline\\Unity.Cecil.Pdb.dll" }}
,{ "pid":12345, "tid":13, "ts":1749474193773011, "dur":261, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Tools\\BuildPipeline\\Unity.Cecil.Rocks.dll" }}
,{ "pid":12345, "tid":13, "ts":1749474193773273, "dur":272, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Tools\\BuildPipeline\\Unity.IL2CPP.Api.dll" }}
,{ "pid":12345, "tid":13, "ts":1749474193773546, "dur":280, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Tools\\BuildPipeline\\Unity.IL2CPP.Bee.BuildLogic.dll" }}
,{ "pid":12345, "tid":13, "ts":1749474193773827, "dur":84, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Tools\\BuildPipeline\\Unity.IL2CPP.Bee.IL2CPPExeCompileCppBuildProgram.Data.dll" }}
,{ "pid":12345, "tid":13, "ts":1749474193773912, "dur":212, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Tools\\BuildPipeline\\Unity.Linker.Api.dll" }}
,{ "pid":12345, "tid":13, "ts":1749474193774125, "dur":322, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Tools\\BuildPipeline\\Unity.Options.dll" }}
,{ "pid":12345, "tid":13, "ts":1749474192621989, "dur":1152458, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"AddBootConfigGUID Library/Bee/artifacts/WinPlayerBuildProgram/boot.config" }}
,{ "pid":12345, "tid":13, "ts":1749474193774628, "dur":83766, "ph":"X", "name": "AddBootConfigGUID",  "args": { "detail":"Library/Bee/artifacts/WinPlayerBuildProgram/boot.config" }}
,{ "pid":12345, "tid":13, "ts":1749474193861085, "dur":7311, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"Library\\Bee\\artifacts\\WinPlayerBuildProgram\\boot.config" }}
,{ "pid":12345, "tid":13, "ts":1749474193861084, "dur":7313, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"CopyFiles C:/Users/<USER>/Desktop/Computer/Cascade Games/Void Voyager/Build/Void Voyager_Data/boot.config" }}
,{ "pid":12345, "tid":13, "ts":1749474193868417, "dur":810, "ph":"X", "name": "CopyFiles",  "args": { "detail":"C:/Users/<USER>/Desktop/Computer/Cascade Games/Void Voyager/Build/Void Voyager_Data/boot.config" }}
,{ "pid":12345, "tid":13, "ts":1749474193869231, "dur":3676302, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":14, "ts":1749474192615321, "dur":923, "ph":"X", "name": "FirstLock",  "args": { "detail":"" }}
,{ "pid":12345, "tid":14, "ts":1749474192616244, "dur":163, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":14, "ts":1749474192616407, "dur":3099, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":14, "ts":1749474192619517, "dur":11554, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\PlaybackEngines\\WindowsStandaloneSupport\\Variations\\win64_player_nondevelopment_mono\\MonoBleedingEdge\\etc\\mono\\4.0\\web.config" }}
,{ "pid":12345, "tid":14, "ts":1749474192619507, "dur":11565, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"CopyFiles C:/Users/<USER>/Desktop/Computer/Cascade Games/Void Voyager/Build/MonoBleedingEdge/etc/mono/4.0/web.config" }}
,{ "pid":12345, "tid":14, "ts":1749474192631073, "dur":70, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":14, "ts":1749474192631152, "dur":649, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":14, "ts":1749474192631919, "dur":2096, "ph":"X", "name": "CopyFiles",  "args": { "detail":"C:/Users/<USER>/Desktop/Computer/Cascade Games/Void Voyager/Build/Void Voyager_Data/Resources/unity_builtin_extra" }}
,{ "pid":12345, "tid":14, "ts":1749474192634064, "dur":4911377, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":15, "ts":1749474192615446, "dur":815, "ph":"X", "name": "FirstLock",  "args": { "detail":"" }}
,{ "pid":12345, "tid":15, "ts":1749474192616263, "dur":3226, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":15, "ts":1749474192619590, "dur":119, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\PlaybackEngines\\WindowsStandaloneSupport\\Variations\\mono\\Managed\\UnityEngine.NVIDIAModule.dll" }}
,{ "pid":12345, "tid":15, "ts":1749474192619809, "dur":166, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Users\\<USER>\\Hydra Games\\Void Voyager\\Library\\Bee\\PlayerScriptAssemblies\\Assembly-CSharp.dll" }}
,{ "pid":12345, "tid":15, "ts":1749474192619976, "dur":75, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Users\\<USER>\\Hydra Games\\Void Voyager\\Library\\Bee\\PlayerScriptAssemblies\\Unity.2D.IK.Runtime.dll" }}
,{ "pid":12345, "tid":15, "ts":1749474192620052, "dur":416, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Users\\<USER>\\Hydra Games\\Void Voyager\\Library\\Bee\\PlayerScriptAssemblies\\Unity.RenderPipelines.Core.Runtime.dll" }}
,{ "pid":12345, "tid":15, "ts":1749474192620469, "dur":74, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Users\\<USER>\\Hydra Games\\Void Voyager\\Library\\Bee\\PlayerScriptAssemblies\\Unity.RenderPipelines.Universal.Config.Runtime.dll" }}
,{ "pid":12345, "tid":15, "ts":1749474192620544, "dur":353, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Users\\<USER>\\Hydra Games\\Void Voyager\\Library\\Bee\\PlayerScriptAssemblies\\Unity.VisualScripting.Flow.dll" }}
,{ "pid":12345, "tid":15, "ts":1749474192620899, "dur":67, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Users\\<USER>\\Hydra Games\\Void Voyager\\Library\\Bee\\PlayerScriptAssemblies\\Unity.RenderPipelines.Core.ShaderLibrary.dll" }}
,{ "pid":12345, "tid":15, "ts":1749474192620967, "dur":289, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Users\\<USER>\\Hydra Games\\Void Voyager\\Library\\Bee\\PlayerScriptAssemblies\\Unity.Collections.dll" }}
,{ "pid":12345, "tid":15, "ts":1749474192621257, "dur":239, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Users\\<USER>\\Hydra Games\\Void Voyager\\Library\\Bee\\PlayerScriptAssemblies\\Unity.2D.SpriteShape.Runtime.dll" }}
,{ "pid":12345, "tid":15, "ts":1749474192621497, "dur":106, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Users\\<USER>\\Hydra Games\\Void Voyager\\Library\\Bee\\PlayerScriptAssemblies\\Unity.2D.PixelPerfect.dll" }}
,{ "pid":12345, "tid":15, "ts":1749474192621604, "dur":460, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Users\\<USER>\\Hydra Games\\Void Voyager\\Library\\Bee\\PlayerScriptAssemblies\\Unity.TextMeshPro.dll" }}
,{ "pid":12345, "tid":15, "ts":1749474192622065, "dur":80, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Users\\<USER>\\Hydra Games\\Void Voyager\\Library\\Bee\\PlayerScriptAssemblies\\Unity.RenderPipelines.ShaderGraph.ShaderGraphLibrary.dll" }}
,{ "pid":12345, "tid":15, "ts":1749474192622146, "dur":631, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Users\\<USER>\\Hydra Games\\Void Voyager\\Library\\Bee\\PlayerScriptAssemblies\\Unity.RenderPipelines.Universal.Runtime.dll" }}
,{ "pid":12345, "tid":15, "ts":1749474192622778, "dur":307, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Users\\<USER>\\Hydra Games\\Void Voyager\\Library\\Bee\\PlayerScriptAssemblies\\Unity.Burst.dll" }}
,{ "pid":12345, "tid":15, "ts":1749474192623085, "dur":383, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Users\\<USER>\\Hydra Games\\Void Voyager\\Library\\Bee\\PlayerScriptAssemblies\\Unity.VisualScripting.Core.dll" }}
,{ "pid":12345, "tid":15, "ts":1749474192623469, "dur":75, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Users\\<USER>\\Hydra Games\\Void Voyager\\Library\\Bee\\PlayerScriptAssemblies\\Unity.2D.Animation.Runtime.dll" }}
,{ "pid":12345, "tid":15, "ts":1749474192623544, "dur":51, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Users\\<USER>\\Hydra Games\\Void Voyager\\Library\\Bee\\PlayerScriptAssemblies\\Unity.2D.Tilemap.Extras.dll" }}
,{ "pid":12345, "tid":15, "ts":1749474192623595, "dur":220, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Users\\<USER>\\Hydra Games\\Void Voyager\\Library\\Bee\\PlayerScriptAssemblies\\UnityEngine.UI.dll" }}
,{ "pid":12345, "tid":15, "ts":1749474192623816, "dur":87, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Users\\<USER>\\Hydra Games\\Void Voyager\\Library\\Bee\\PlayerScriptAssemblies\\Unity.InternalAPIEngineBridge.001.dll" }}
,{ "pid":12345, "tid":15, "ts":1749474192623904, "dur":691, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Users\\<USER>\\Hydra Games\\Void Voyager\\Library\\Bee\\PlayerScriptAssemblies\\Unity.2D.Common.Runtime.dll" }}
,{ "pid":12345, "tid":15, "ts":1749474192624624, "dur":3068, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Users\\<USER>\\Hydra Games\\Void Voyager\\Library\\Bee\\PlayerScriptAssemblies\\Unity.Mathematics.dll" }}
,{ "pid":12345, "tid":15, "ts":1749474192627712, "dur":1940, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\MonoBleedingEdge\\lib\\mono\\unityjit-win32\\System.Data.dll" }}
,{ "pid":12345, "tid":15, "ts":1749474192629735, "dur":248, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"ProjectSettings\\BurstAotSettings_StandaloneWindows.json" }}
,{ "pid":12345, "tid":15, "ts":1749474192630047, "dur":233319, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Users\\<USER>\\Hydra Games\\Void Voyager\\Library\\PackageCache\\com.unity.burst@1.8.15\\.Runtime\\bcl.exe" }}
,{ "pid":12345, "tid":15, "ts":1749474192619503, "dur":243865, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"GenerateNativePluginsForAssemblies Library/Bee/artifacts/WinPlayerBuildProgram/AsyncPluginsFromLinker" }}
,{ "pid":12345, "tid":15, "ts":1749474192864350, "dur":4678943, "ph":"X", "name": "GenerateNativePluginsForAssemblies",  "args": { "detail":"Library/Bee/artifacts/WinPlayerBuildProgram/AsyncPluginsFromLinker" }}
,{ "pid":12345, "tid":16, "ts":1749474192615484, "dur":757, "ph":"X", "name": "FirstLock",  "args": { "detail":"" }}
,{ "pid":12345, "tid":16, "ts":1749474192616242, "dur":656, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":16, "ts":1749474192616899, "dur":2608, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":16, "ts":1749474192619508, "dur":93, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"CopyFiles C:/Users/<USER>/Desktop/Computer/Cascade Games/Void Voyager/Build/MonoBleedingEdge/etc/mono/4.0/settings.map" }}
,{ "pid":12345, "tid":16, "ts":1749474192619601, "dur":1168, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":16, "ts":1749474192620797, "dur":2158, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\MonoBleedingEdge\\lib\\mono\\unityjit-win32\\System.Xml.dll" }}
,{ "pid":12345, "tid":16, "ts":1749474192620778, "dur":2178, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"CopyFiles C:/Users/<USER>/Desktop/Computer/Cascade Games/Void Voyager/Build/Void Voyager_Data/Managed/System.Xml.dll" }}
,{ "pid":12345, "tid":16, "ts":1749474192622957, "dur":2587, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":16, "ts":1749474192625563, "dur":151, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\MonoBleedingEdge\\lib\\mono\\unityjit-win32\\System.Net.Http.dll" }}
,{ "pid":12345, "tid":16, "ts":1749474192625562, "dur":153, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"CopyFiles C:/Users/<USER>/Desktop/Computer/Cascade Games/Void Voyager/Build/Void Voyager_Data/Managed/System.Net.Http.dll" }}
,{ "pid":12345, "tid":16, "ts":1749474192625716, "dur":81, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":16, "ts":1749474192625806, "dur":189, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\MonoBleedingEdge\\lib\\mono\\unityjit-win32\\System.EnterpriseServices.dll" }}
,{ "pid":12345, "tid":16, "ts":1749474192625805, "dur":191, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"CopyFiles C:/Users/<USER>/Desktop/Computer/Cascade Games/Void Voyager/Build/Void Voyager_Data/Managed/System.EnterpriseServices.dll" }}
,{ "pid":12345, "tid":16, "ts":1749474192625997, "dur":64, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":16, "ts":1749474192626066, "dur":1067, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\MonoBleedingEdge\\lib\\mono\\unityjit-win32\\System.Core.dll" }}
,{ "pid":12345, "tid":16, "ts":1749474192626065, "dur":1069, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"CopyFiles C:/Users/<USER>/Desktop/Computer/Cascade Games/Void Voyager/Build/Void Voyager_Data/Managed/System.Core.dll" }}
,{ "pid":12345, "tid":16, "ts":1749474192627134, "dur":88, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":16, "ts":1749474192627228, "dur":91, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":16, "ts":1749474192627324, "dur":68, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":16, "ts":1749474192627403, "dur":60, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":16, "ts":1749474192628221, "dur":5128, "ph":"X", "name": "CopyFiles",  "args": { "detail":"C:/Users/<USER>/Desktop/Computer/Cascade Games/Void Voyager/Build/Void Voyager_Data/Managed/Assembly-CSharp.dll" }}
,{ "pid":12345, "tid":16, "ts":1749474192633393, "dur":4912041, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":17, "ts":1749474192615512, "dur":699, "ph":"X", "name": "FirstLock",  "args": { "detail":"" }}
,{ "pid":12345, "tid":17, "ts":1749474192616211, "dur":829, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":17, "ts":1749474192617041, "dur":2447, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":17, "ts":1749474192620954, "dur":398, "ph":"X", "name": "WriteText",  "args": { "detail":"Library/Bee/artifacts/WinPlayerBuildProgram/Data/app.info" }}
,{ "pid":12345, "tid":17, "ts":1749474192624141, "dur":2366, "ph":"X", "name": "CopyFiles",  "args": { "detail":"C:/Users/<USER>/Desktop/Computer/Cascade Games/Void Voyager/Build/Void Voyager_Data/app.info" }}
,{ "pid":12345, "tid":17, "ts":1749474192626512, "dur":323, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":17, "ts":1749474192626843, "dur":63, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":17, "ts":1749474192626911, "dur":202, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":17, "ts":1749474192627121, "dur":162, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":17, "ts":1749474192627289, "dur":54, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":17, "ts":1749474192627395, "dur":488, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":17, "ts":1749474192627894, "dur":394, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":17, "ts":1749474192628300, "dur":239, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":17, "ts":1749474192628545, "dur":403, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":17, "ts":1749474192628955, "dur":563, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":17, "ts":1749474192629527, "dur":944, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":17, "ts":1749474192630481, "dur":191, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":17, "ts":1749474192630679, "dur":58, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":17, "ts":1749474192630794, "dur":92, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":17, "ts":1749474192630892, "dur":58, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":17, "ts":1749474192630955, "dur":833, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":17, "ts":1749474192631797, "dur":64, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":17, "ts":1749474192631888, "dur":9120, "ph":"X", "name": "CopyFiles",  "args": { "detail":"C:/Users/<USER>/Desktop/Computer/Cascade Games/Void Voyager/Build/Void Voyager_Data/globalgamemanagers.assets.resS" }}
,{ "pid":12345, "tid":17, "ts":1749474192641040, "dur":4904440, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":18, "ts":1749474192615625, "dur":620, "ph":"X", "name": "FirstLock",  "args": { "detail":"" }}
,{ "pid":12345, "tid":18, "ts":1749474192616246, "dur":3253, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":18, "ts":1749474192619512, "dur":11565, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\PlaybackEngines\\WindowsStandaloneSupport\\Variations\\win64_player_nondevelopment_mono\\MonoBleedingEdge\\etc\\mono\\4.5\\machine.config" }}
,{ "pid":12345, "tid":18, "ts":1749474192619500, "dur":11578, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"CopyFiles C:/Users/<USER>/Desktop/Computer/Cascade Games/Void Voyager/Build/MonoBleedingEdge/etc/mono/4.5/machine.config" }}
,{ "pid":12345, "tid":18, "ts":1749474192631079, "dur":110, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":18, "ts":1749474192631198, "dur":645, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":18, "ts":1749474192631921, "dur":6160, "ph":"X", "name": "CopyFiles",  "args": { "detail":"C:/Users/<USER>/Desktop/Computer/Cascade Games/Void Voyager/Build/Void Voyager_Data/globalgamemanagers" }}
,{ "pid":12345, "tid":18, "ts":1749474192638115, "dur":4907317, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":19, "ts":1749474192615694, "dur":512, "ph":"X", "name": "FirstLock",  "args": { "detail":"" }}
,{ "pid":12345, "tid":19, "ts":1749474192616224, "dur":689, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":19, "ts":1749474192616914, "dur":2673, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":19, "ts":1749474192619613, "dur":12439, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\PlaybackEngines\\WindowsStandaloneSupport\\Variations\\win64_player_nondevelopment_mono\\MonoBleedingEdge\\etc\\mono\\2.0\\machine.config" }}
,{ "pid":12345, "tid":19, "ts":1749474192619588, "dur":12465, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"CopyFiles C:/Users/<USER>/Desktop/Computer/Cascade Games/Void Voyager/Build/MonoBleedingEdge/etc/mono/2.0/machine.config" }}
,{ "pid":12345, "tid":19, "ts":1749474192632054, "dur":82, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":19, "ts":1749474192632143, "dur":4913322, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":20, "ts":1749474192615720, "dur":570, "ph":"X", "name": "FirstLock",  "args": { "detail":"" }}
,{ "pid":12345, "tid":20, "ts":1749474192616290, "dur":3206, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":20, "ts":1749474192619519, "dur":11554, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\PlaybackEngines\\WindowsStandaloneSupport\\Variations\\win64_player_nondevelopment_mono\\MonoBleedingEdge\\etc\\mono\\4.5\\web.config" }}
,{ "pid":12345, "tid":20, "ts":1749474192619497, "dur":11578, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"CopyFiles C:/Users/<USER>/Desktop/Computer/Cascade Games/Void Voyager/Build/MonoBleedingEdge/etc/mono/4.5/web.config" }}
,{ "pid":12345, "tid":20, "ts":1749474192631075, "dur":64, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":20, "ts":1749474192631148, "dur":647, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":20, "ts":1749474192631805, "dur":86, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":20, "ts":1749474192631911, "dur":4913555, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":0, "ts":1749474197551499, "dur":1320, "ph":"X", "name": "ProfilerWriteOutput" }
,{ "pid": 35942, "tid": -1, "ph": "M", "name": "process_name", "args": { "name": "netcorerun.dll" } },
{ "pid": 35942, "tid": -1, "ph": "M", "name": "process_sort_index", "args": { "sort_index": "-1" } },
{ "pid": 35942, "tid": 1, "ph": "M", "name": "thread_name", "args": { "name": "" } },
{ "pid": 35942, "tid": 1, "ts": 1749474192445924, "dur": 108644, "ph": "X", "name": "BuildProgram", "args": {} },
{ "pid": 35942, "tid": 1, "ts": 1749474192446630, "dur": 30645, "ph": "X", "name": "BuildProgramContextConstructor", "args": {} },
{ "pid": 35942, "tid": 1, "ts": 1749474192493759, "dur": 40102, "ph": "X", "name": "RunBuildProgram", "args": {} },
{ "pid": 35942, "tid": 1, "ts": 1749474192494024, "dur": 27591, "ph": "X", "name": "PlayerBuildProgramBase.SetupPlayerBuild", "args": {} },
{ "pid": 35942, "tid": 1, "ts": 1749474192494496, "dur": 19295, "ph": "X", "name": "SetupDataFiles", "args": {} },
{ "pid": 35942, "tid": 1, "ts": 1749474192514067, "dur": 424, "ph": "X", "name": "SetupCopyPlugins", "args": {} },
{ "pid": 35942, "tid": 1, "ts": 1749474192516253, "dur": 3723, "ph": "X", "name": "SetupUnityLinker", "args": {} },
{ "pid": 35942, "tid": 1, "ts": 1749474192539040, "dur": 2411, "ph": "X", "name": "OutputData.Write", "args": {} },
{ "pid": 35942, "tid": 1, "ts": 1749474192541454, "dur": 13108, "ph": "X", "name": "Backend.Write", "args": {} },
{ "pid": 35942, "tid": 1, "ts": 1749474192542222, "dur": 10964, "ph": "X", "name": "JsonToString", "args": {} },
{ "pid": 35942, "tid": 1, "ts": 1749474192560410, "dur": 1352, "ph": "X", "name": "", "args": {} },
{ "pid": 35942, "tid": 1, "ts": 1749474192559805, "dur": 2207, "ph": "X", "name": "Write chrome-trace events", "args": {} },
{ "pid": 36728, "tid": 2801, "ts": 1749474197774670, "dur": 19, "ph": "X", "name": "Wait for external events", "args": {"First to finish": "buildprogram1.traceevents"} },
{ "pid": 36728, "tid": 2801, "ts": 1749474197774998, "dur": 22, "ph": "X", "name": "Wait for external events", "args": {"First to finish": "backend3.traceevents"} },
{ "pid": 36728, "tid": 2801, "ts": 1749474197786576, "dur": 32, "ph": "X", "name": "Wait for external events", "args": {"First to finish": "backend2.traceevents"} },
{ "pid": 36728, "tid": 2801, "ts": 1749474197787016, "dur": 24, "ph": "X", "name": "Wait for external events", "args": {"First to finish": "buildprogram0.traceevents"} },
{ "pid": 36728, "tid": 2801, "ts": 1749474197787191, "dur": 13, "ph": "X", "name": "Wait for external events", "args": {"First to finish": "backend1.traceevents"} },
{ "pid": 36728, "tid": 2801, "ts": 1749474197774802, "dur": 195, "ph": "X", "name": "buildprogram1.traceevents", "args": {} },
{ "pid": 36728, "tid": 2801, "ts": 1749474197775049, "dur": 11526, "ph": "X", "name": "backend3.traceevents", "args": {} },
{ "pid": 36728, "tid": 2801, "ts": 1749474197786651, "dur": 364, "ph": "X", "name": "backend2.traceevents", "args": {} },
{ "pid": 36728, "tid": 2801, "ts": 1749474197787074, "dur": 116, "ph": "X", "name": "buildprogram0.traceevents", "args": {} },
{ "pid": 36728, "tid": 2801, "ts": 1749474197773715, "dur": 13537, "ph": "X", "name": "Write chrome-trace events", "args": {} },
