using UnityEngine;
using UnityEngine.UI;
using UnityEngine.EventSystems;
using System.Collections;

/// <summary>
/// Represents a single slot in the hotbar that can contain an inventory item.
/// Handles drag and drop operations between hotbar and inventory.
/// </summary>
public class HotbarSlot : <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, IPointerEnter<PERSON><PERSON><PERSON>, IPointerExit<PERSON><PERSON><PERSON>, IBeginDragHandler, IDragHandler, IEndDragHandler
{
    [Header("References")]
    public Image itemIcon;
    public InventoryItem storedItem;
    public TMPro.TextMeshProUGUI stackCountText;
    public TMPro.TextMeshProUGUI ammoCountText;

    [Header("Selection Settings")]
    [SerializeField] private float selectionScale = 1.2f;
    [SerializeField] private float selectionAnimDuration = 0.1f;
    [SerializeField] private AnimationCurve selectionCurve = AnimationCurve.EaseInOut(0, 0, 1, 1);

    [Header("Inventory State Settings")]
    [SerializeField] private float inventoryOpenScale = 1.2f;
    [SerializeField] private float inventoryStateAnimDuration = 0.2f;

    [Header("Physics Settings")]
    [SerializeField] private float tiltFactor = 15f;
    [SerializeField] private float tiltSmoothing = 5f;
    [SerializeField] private float dragResistance = 8f;
    [SerializeField] private float maxTiltVelocity = 1000f;
    [SerializeField] private float rotationMomentumFactor = 0.5f;
    [SerializeField] private float minRotationSpeed = 360f;
    [SerializeField] private float maxRotationSpeed = 720f;

    // Selection state
    private bool isSelected = false;
    private Vector3 originalScale;
    private Coroutine selectionAnimation;

    // References to external systems
    private Hotbar parentHotbar;
    private InventoryUI inventoryUI;
    private InventoryManager inventoryManager;
    private RectTransform rectTransform;

    // Drag state
    private bool isDragging = false;
    private bool wasJustDragging = false;
    private GameObject draggedItemIcon;
    private Vector2 originalPosition;
    private Vector2 lastPosition;
    private Vector2 velocity;

    // Rotation state
    private bool initialRotation = false;
    private bool currentRotation = false;
    private Quaternion rotationStart;
    private Quaternion rotationTarget;
    private float rotationProgress = 1f;
    private float currentRotationSpeed;
    private float rotationDirection = 1f;

    // Tilt state
    private float currentTiltX = 0f;
    private float currentTiltZ = 0f;
    private float targetTiltX = 0f;
    private float targetTiltZ = 0f;

    // Inventory state tracking
    private bool wasInventoryOpenLastFrame = true;

    #region Initialization

    private void Awake()
    {
        // Find parent Hotbar component
        parentHotbar = GetComponentInParent<Hotbar>();
        if (parentHotbar == null)
        {
            Debug.LogError("Could not find Hotbar component in parents!");
        }

        rectTransform = GetComponent<RectTransform>();
        originalScale = rectTransform.localScale;

        InitializeItemIcon();
        InitializePhysics();
    }

    private void InitializeItemIcon()
    {
        // Create or get the ItemIcon image
        Transform iconTransform = transform.Find("ItemIcon");
        if (iconTransform == null)
        {
            GameObject iconObject = new GameObject("ItemIcon");
            iconObject.transform.SetParent(transform, false);
            itemIcon = iconObject.AddComponent<Image>();
            itemIcon.raycastTarget = false;
            itemIcon.enabled = false;

            // Make the icon fill the slot
            RectTransform iconRect = itemIcon.GetComponent<RectTransform>();
            iconRect.anchorMin = Vector2.zero;
            iconRect.anchorMax = Vector2.one;
            iconRect.sizeDelta = Vector2.zero;
            iconRect.anchoredPosition = Vector2.zero;
        }
        else
        {
            itemIcon = iconTransform.GetComponent<Image>();
        }
    }

    private void InitializePhysics()
    {
        lastPosition = Vector2.zero;
        velocity = Vector2.zero;
        currentTiltX = 0f;
        currentTiltZ = 0f;
        targetTiltX = 0f;
        targetTiltZ = 0f;
        currentRotationSpeed = minRotationSpeed;
        rotationDirection = 1f;
        rotationProgress = 1f;
    }

    private void Start()
    {
        FindInventoryReferences();
        originalPosition = rectTransform.anchoredPosition;
    }

    #endregion

    #region Update Logic

    private void Update()
    {
        CheckInventoryState();
        UpdatePhysicsIfNeeded();
        CheckForRotation();
    }

    private void CheckInventoryState()
    {
        if (!isDragging) return;

        bool isInventoryOpen = inventoryUI != null && inventoryUI.gameObject.activeInHierarchy;

        if (wasInventoryOpenLastFrame && !isInventoryOpen)
        {
            // Inventory was just closed during drag
            // OnEndDrag will handle returning the item
        }
        else if (!wasInventoryOpenLastFrame && isInventoryOpen)
        {
            // Inventory was just reopened
            FindInventoryReferences();

            if (currentRotation)
            {
                InventoryItemUIDrag.SetCurrentDraggedItemRotation(currentRotation);
            }
        }

        wasInventoryOpenLastFrame = isInventoryOpen;
    }

    private void UpdatePhysicsIfNeeded()
    {
        bool needsPhysicsUpdate = isDragging || wasJustDragging ||
            !Mathf.Approximately(currentTiltX, 0f) ||
            !Mathf.Approximately(currentTiltZ, 0f) ||
            rotationProgress < 1f;

        if (needsPhysicsUpdate)
        {
            UpdatePhysics();
        }
    }

    private void CheckForRotation()
    {
        if (isDragging && storedItem != null && Input.GetMouseButtonDown(1))
        {
            RotateItem();
        }
    }

    #endregion

    #region Item Management

    /// <summary>
    /// Sets or clears the item in this slot
    /// </summary>
    public void SetItem(InventoryItem item)
    {
        // Mark item as having been in hotbar (for potential future logic)
        if (item != null && item.itemData != null)
        {
            item.wasInHotbar = true;
        }

        storedItem = item;
        UpdateItemVisual();
    }

    private void UpdateItemVisual()
    {
        if (storedItem != null && storedItem.itemData != null)
        {
            itemIcon.sprite = storedItem.itemData.inventoryIcon;
            itemIcon.preserveAspect = true;
            itemIcon.enabled = true;
            itemIcon.color = Color.white;

            // Update text displays
            UpdateItemTexts();
        }
        else
        {
            itemIcon.sprite = null;
            itemIcon.enabled = false;
            storedItem = null;

            // Hide text displays
            if (stackCountText != null) stackCountText.gameObject.SetActive(false);
            if (ammoCountText != null) ammoCountText.gameObject.SetActive(false);
        }
    }

    /// <summary>
    /// Update text displays for stack count and ammo
    /// </summary>
    private void UpdateItemTexts()
    {
        if (storedItem == null || storedItem.itemData == null) return;

        // Update stack count for ammo
        if (stackCountText != null)
        {
            if (storedItem.itemData.itemType == ItemType.Ammo && storedItem.stackCount > 1)
            {
                stackCountText.text = storedItem.stackCount.ToString();
                stackCountText.gameObject.SetActive(true);
            }
            else
            {
                stackCountText.gameObject.SetActive(false);
            }
        }

        // Update ammo count for weapons
        if (ammoCountText != null)
        {
            if (storedItem.itemData.itemType == ItemType.Weapon)
            {
                ammoCountText.text = $"{storedItem.currentAmmo}/{storedItem.itemData.magazineSize}";
                ammoCountText.gameObject.SetActive(true);
            }
            else
            {
                ammoCountText.gameObject.SetActive(false);
            }
        }
    }

    /// <summary>
    /// Uses the item in this slot based on its type
    /// </summary>
    public void UseItem()
    {
        if (storedItem == null) return;

        // Handle different item types
        switch (storedItem.itemData.itemType)
        {
            case ItemType.Weapon:
                UseWeapon();
                break;
            case ItemType.Consumable:
                UseConsumable();
                break;
            default:
                Debug.Log($"Item {storedItem.GetName()} cannot be used");
                break;
        }
    }

    /// <summary>
    /// Use weapon (reload only - shooting handled by WeaponShootingSystem)
    /// </summary>
    private void UseWeapon()
    {
        WeaponSystem weaponSystem = FindObjectOfType<WeaponSystem>();
        if (weaponSystem == null)
        {
            Debug.LogError("WeaponSystem not found! Please add WeaponSystem component to scene.");
            return;
        }

        // Only handle reload when hotbar item is used directly
        // Shooting is now handled by WeaponShootingSystem
        weaponSystem.TryReloadWeapon(storedItem);

        // Update display after weapon use
        UpdateItemTexts();
    }

    /// <summary>
    /// Use consumable item
    /// </summary>
    private void UseConsumable()
    {
        // Implement consumable logic here
        Debug.Log($"Used consumable: {storedItem.GetName()}");

        // For now, just remove the item after use
        SetItem(null);
    }

    /// <summary>
    /// Drops the item from this hotbar slot into the world
    /// </summary>
    public void DropItem()
    {
        if (storedItem == null) return;

        // Find player transform for drop position calculation
        Transform playerTransform = FindPlayerTransform();
        if (playerTransform == null)
        {
            Debug.LogError("Cannot drop item: Player not found");
            return;
        }

        // Ensure we have inventory manager reference
        if (!EnsureInventoryReferences())
        {
            Debug.LogError("Cannot drop item: No inventory manager available");
            return;
        }

        // Get the item to drop
        InventoryItem itemToDrop = storedItem;

        // Clear the slot first
        SetItem(null);

        // Drop the item through the inventory manager with player position for animation
        // The InventoryManager will calculate the random drop position
        inventoryManager.DropItem(itemToDrop, Vector3.zero, playerTransform.position);

        Debug.Log($"Dropped {itemToDrop.GetName()} from hotbar");
    }

    /// <summary>
    /// Finds the player transform for drop position calculation
    /// </summary>
    private Transform FindPlayerTransform()
    {
        // Try to find player by tag first
        GameObject player = GameObject.FindGameObjectWithTag("Player");
        if (player != null)
        {
            return player.transform;
        }

        // Fallback: try to find PlayerController component
        PlayerController playerController = FindObjectOfType<PlayerController>();
        if (playerController != null)
        {
            return playerController.transform;
        }

        return null;
    }

    #endregion

    #region Inventory References

    /// <summary>
    /// Finds references to InventoryUI and InventoryManager
    /// </summary>
    private void FindInventoryReferences()
    {
        // Try through parent hotbar first (preferred path)
        if (parentHotbar != null && parentHotbar.inventoryUI != null &&
            parentHotbar.inventoryUI.gameObject.activeInHierarchy)
        {
            inventoryUI = parentHotbar.inventoryUI;
            inventoryManager = inventoryUI.inventoryManager;
            return;
        }

        // Fallback: find any active InventoryUI
        InventoryUI[] potentialInventoryUIs = FindObjectsOfType<InventoryUI>();

        // First try to find an active one
        foreach (InventoryUI ui in potentialInventoryUIs)
        {
            if (ui.gameObject.activeInHierarchy)
            {
                inventoryUI = ui;
                inventoryManager = ui.inventoryManager;
                return;
            }
        }

        // Last resort: use any available UI
        if (potentialInventoryUIs.Length > 0)
        {
            inventoryUI = potentialInventoryUIs[0];
            inventoryManager = inventoryUI.inventoryManager;
        }
    }

    /// <summary>
    /// Ensures we have valid inventory references before operations
    /// </summary>
    private bool EnsureInventoryReferences()
    {
        bool needsRefresh = inventoryUI == null ||
                           inventoryManager == null ||
                           (inventoryUI != null && !inventoryUI.gameObject.activeInHierarchy);

        if (needsRefresh)
        {
            FindInventoryReferences();
        }

        return inventoryUI != null && inventoryManager != null;
    }

    #endregion

    #region Rotation and Physics

    /// <summary>
    /// Rotates the currently dragged item
    /// </summary>
    private void RotateItem()
    {
        if (storedItem == null || !storedItem.itemData.canRotate || draggedItemIcon == null)
            return;

        currentRotation = !currentRotation;

        if (inventoryUI != null)
        {
            // Update size based on item dimensions
            RectTransform dragRect = draggedItemIcon.GetComponent<RectTransform>();
            Vector2 slotSize = inventoryUI.slotSize;

            // Store current position
            Vector3 currentPos = draggedItemIcon.transform.position;

            // Set size to match item's actual size
            dragRect.sizeDelta = new Vector2(
                (slotSize.x * storedItem.itemData.size.x),
                (slotSize.y * storedItem.itemData.size.y)
            );

            // Restore position
            draggedItemIcon.transform.position = currentPos;
        }

        // Set up rotation animation
        rotationStart = draggedItemIcon.transform.rotation;
        rotationTarget = currentRotation ? Quaternion.Euler(0, 0, 90) : Quaternion.identity;
        rotationProgress = 0f;

        // Calculate rotation parameters
        float velocityMagnitude = velocity.magnitude;
        currentRotationSpeed = Mathf.Lerp(minRotationSpeed, maxRotationSpeed,
            Mathf.Clamp01(velocityMagnitude / maxTiltVelocity));
        rotationDirection = (velocity.x > 0) ? 1f : -1f;

        // Update grid highlights
        InventoryItemUIDrag.SetCurrentDraggedItemRotation(currentRotation);
    }

    /// <summary>
    /// Updates physics for dragged item including tilt and rotation
    /// </summary>
    private void UpdatePhysics()
    {
        if (draggedItemIcon == null) return;

        UpdateVelocityAndTilt();
        ApplyRotationAndTilt();
    }

    private void UpdateVelocityAndTilt()
    {
        if (isDragging)
        {
            // Calculate velocity based on position change
            Vector2 currentPos = draggedItemIcon.transform.position;
            velocity = (currentPos - lastPosition) / Time.deltaTime;
            lastPosition = currentPos;

            // Calculate tilt based on velocity
            float velocityMagnitude = velocity.magnitude;
            float tiltRatio = Mathf.Clamp01(velocityMagnitude / maxTiltVelocity);

            targetTiltZ = -velocity.x * tiltRatio * tiltFactor / maxTiltVelocity;
            targetTiltX = velocity.y * tiltRatio * tiltFactor / maxTiltVelocity;

            // Update rotation parameters
            currentRotationSpeed = Mathf.Lerp(minRotationSpeed, maxRotationSpeed,
                Mathf.Clamp01(velocityMagnitude / maxTiltVelocity));
            rotationDirection = (velocity.x > 0) ? 1f : -1f;
        }
        else
        {
            // Apply drag and reset tilt when not dragging
            velocity = Vector2.Lerp(velocity, Vector2.zero, dragResistance * Time.deltaTime);

            if (wasJustDragging)
            {
                wasJustDragging = false;
                velocity *= 0.5f;
            }

            targetTiltX = 0f;
            targetTiltZ = 0f;
        }

        // Smooth tilt transitions
        currentTiltX = Mathf.Lerp(currentTiltX, targetTiltX, tiltSmoothing * Time.deltaTime);
        currentTiltZ = Mathf.Lerp(currentTiltZ, targetTiltZ, tiltSmoothing * Time.deltaTime);
    }

    private void ApplyRotationAndTilt()
    {
        // Create tilt rotation
        Quaternion tiltRotation = Quaternion.Euler(currentTiltX, 0f, currentTiltZ);

        if (rotationProgress < 1f)
        {
            // Calculate rotation step
            float rotationStep = (currentRotationSpeed * Time.deltaTime) / 90f;
            rotationProgress = Mathf.Min(rotationProgress + rotationStep, 1f);

            // Apply easing
            float t = rotationProgress;
            if (isDragging || wasJustDragging)
            {
                // Add "punch" to rotation based on velocity
                float velocityInfluence = Mathf.Clamp01(velocity.magnitude / maxTiltVelocity) * rotationMomentumFactor;
                t = Mathf.Sin(t * Mathf.PI * (1f + velocityInfluence)) * (1f - t) + t;
            }
            else
            {
                // Smooth ease-in-out
                t = t * t * (3f - 2f * t);
            }

            // Apply rotation with tilt
            Quaternion baseRotation = Quaternion.Lerp(rotationStart, rotationTarget, t);
            draggedItemIcon.transform.rotation = baseRotation * tiltRotation;
        }
        else if (isDragging || wasJustDragging)
        {
            // Just apply tilt when rotation is complete
            draggedItemIcon.transform.rotation = rotationTarget * tiltRotation;
        }
    }

    #endregion

    #region Drag and Drop

    public void OnBeginDrag(PointerEventData eventData)
    {
        // Only allow left-click dragging
        if (eventData.button != PointerEventData.InputButton.Left || storedItem == null)
            return;

        isDragging = true;
        wasJustDragging = false;

        EnsureInventoryReferences();
        CreateDragVisual(eventData);
    }

    private void CreateDragVisual(PointerEventData eventData)
    {
        // Create visual GameObject
        draggedItemIcon = new GameObject("DraggedItem");
        draggedItemIcon.transform.SetParent(transform.root, false);
        draggedItemIcon.transform.SetAsLastSibling();

        // Add components
        CanvasGroup canvasGroup = draggedItemIcon.AddComponent<CanvasGroup>();
        canvasGroup.alpha = 0.8f;

        Image dragImage = draggedItemIcon.AddComponent<Image>();
        dragImage.sprite = itemIcon.sprite;
        dragImage.raycastTarget = false;
        dragImage.preserveAspect = false;

        // Store rotation state
        initialRotation = storedItem.isRotated;
        currentRotation = initialRotation;

        // Calculate size
        ConfigureDragVisualSize();

        // Set initial position and physics state
        draggedItemIcon.transform.position = eventData.position;
        lastPosition = eventData.position;
        velocity = Vector2.zero;

        // Initialize rotation values
        rotationStart = currentRotation ? Quaternion.Euler(0, 0, 90) : Quaternion.identity;
        rotationTarget = rotationStart;
        rotationProgress = 1f;
        currentRotationSpeed = minRotationSpeed;

        // Apply rotation and set up grid highlights
        if (currentRotation)
        {
            draggedItemIcon.transform.rotation = Quaternion.Euler(0, 0, 90);
        }

        InventoryItemUIDrag.SetCurrentlyDraggedItem(storedItem);
        InventoryItemUIDrag.SetCurrentDraggedItemRotation(currentRotation);
    }

    private void ConfigureDragVisualSize()
    {
        RectTransform dragRect = draggedItemIcon.GetComponent<RectTransform>();

        // Get size based on rotation
        Vector2Int itemSize = currentRotation ?
            new Vector2Int(storedItem.itemData.size.y, storedItem.itemData.size.x) :
            storedItem.itemData.size;

        if (inventoryUI != null)
        {
            // Get inventory grid metrics
            Vector2 slotSize = inventoryUI.slotSize;
            Vector2 spacing = inventoryUI.GridSpacing;

            // Set size with spacing
            dragRect.sizeDelta = new Vector2(
                (slotSize.x * itemSize.x) + (spacing.x * (itemSize.x - 1)),
                (slotSize.y * itemSize.y) + (spacing.y * (itemSize.y - 1))
            );
        }
        else
        {
            // Fallback size
            dragRect.sizeDelta = new Vector2(80, 80);
        }

        // Center pivot for dragging
        dragRect.pivot = new Vector2(0.5f, 0.5f);
    }

    public void OnDrag(PointerEventData eventData)
    {
        // Only process left-click dragging
        if (eventData.button != PointerEventData.InputButton.Left || !isDragging || draggedItemIcon == null)
            return;

        // Update visual position
        draggedItemIcon.transform.position = eventData.position;

        // Update grid highlights if inventory is open
        if (inventoryUI != null && inventoryUI.gameObject.activeInHierarchy)
        {
            InventoryItemUIDrag.UpdateCurrentDragHighlights(eventData.position);
        }
    }

    public void OnEndDrag(PointerEventData eventData)
    {
        // Only process left-click dragging
        if (eventData.button != PointerEventData.InputButton.Left || !isDragging)
            return;

        isDragging = false;
        wasJustDragging = true;

        // Clear highlights
        InventoryItemUIDrag.ClearCurrentDraggedItem();

        InventoryItem draggedItem = storedItem;
        bool finalRotation = currentRotation;

        // Validate dragged item
        if (draggedItem == null)
        {
            CleanupDragVisual();
            return;
        }

        // Handle inventory closed during drag
        if (inventoryUI == null || !inventoryUI.gameObject.activeInHierarchy)
        {
            HandleInventoryClosedDuringDrag(draggedItem);
            return;
        }

        // Process drop target
        if (eventData.pointerEnter != null)
        {
            ProcessDropTarget(eventData, draggedItem, finalRotation);
        }
        else
        {
            // Not dropped on any valid target
            SetItem(draggedItem);
            AnimateIconBackToSlot(draggedItemIcon);
        }
    }

    private void HandleInventoryClosedDuringDrag(InventoryItem draggedItem)
    {
        // Return item to slot and animate icon back
        SetItem(draggedItem);

        if (draggedItemIcon != null)
        {
            StartCoroutine(AnimateIconBackToSlot(draggedItemIcon));
        }
    }

    private void ProcessDropTarget(PointerEventData eventData, InventoryItem draggedItem, bool finalRotation)
    {
        bool validDropTarget = false;
        bool shouldDestroyIcon = false;

        // Check if dropped on another hotbar slot
        HotbarSlot targetSlot = eventData.pointerEnter.GetComponent<HotbarSlot>();
        if (targetSlot != null && targetSlot != this)
        {
            HandleDropOnHotbarSlot(targetSlot);
            validDropTarget = true;
            shouldDestroyIcon = true;
        }
        else if (EnsureInventoryReferences())
        {
            // Try to place in inventory
            InventorySlot inventorySlot = eventData.pointerEnter.GetComponent<InventorySlot>();
            if (inventorySlot != null)
            {
                bool success = TryPlaceInInventory(inventorySlot, draggedItem, finalRotation, eventData.position);
                validDropTarget = success;
                shouldDestroyIcon = success;
            }
            else
            {
                // Not a valid inventory slot
                SetItem(draggedItem);
            }
        }
        else
        {
            // No inventory references, return to slot
            SetItem(draggedItem);
        }

        // Finalize drag visual
        if (!validDropTarget && draggedItemIcon != null)
        {
            StartCoroutine(AnimateIconBackToSlot(draggedItemIcon));
        }
        else if (shouldDestroyIcon && draggedItemIcon != null)
        {
            Destroy(draggedItemIcon);
            draggedItemIcon = null;
        }
    }

    private void HandleDropOnHotbarSlot(HotbarSlot targetSlot)
    {
        if (targetSlot.storedItem == null)
        {
            // Move to empty slot
            transferItemToAnotherHotbarSlot(targetSlot);
        }
        else
        {
            // Swap with occupied slot
            swapItemsWithAnotherHotbarSlot(targetSlot);
        }
    }

    private bool TryPlaceInInventory(InventorySlot inventorySlot, InventoryItem draggedItem, bool finalRotation, Vector2 dropPosition)
    {
        Vector2Int gridPos = inventorySlot.GetGridPosition();

        // Ensure item is in inventory items list
        if (!inventoryManager.items.Contains(draggedItem))
        {
            inventoryManager.items.Add(draggedItem);
        }

        // Apply rotation from dragging
        draggedItem.SetRotation(finalRotation);

        // Try to place in inventory
        bool success = inventoryManager.TryAddItem(draggedItem, gridPos, finalRotation);

        if (success)
        {
            // Handle success
            HandleSuccessfulInventoryPlacement(draggedItem, gridPos, dropPosition);
            return true;
        }
        else
        {
            // Handle failure
            draggedItem.SetRotation(initialRotation);
            SetItem(draggedItem);
            return false;
        }
    }

    private void HandleSuccessfulInventoryPlacement(InventoryItem item, Vector2Int gridPos, Vector2 dropPosition)
    {
        // For multi-cell items, ensure grid is properly filled
        if (item.GetRotatedSize().x > 1 || item.GetRotatedSize().y > 1)
        {
            EnsureGridCellsAreFilled(item, gridPos);
        }

        // Create UI for item at cursor position
        CreateInventoryItemAtMousePosition(item, gridPos, dropPosition);

        // Remove from hotbar
        SetItem(null);
    }

    private void EnsureGridCellsAreFilled(InventoryItem item, Vector2Int gridPos)
    {
        Vector2Int size = item.GetRotatedSize();

        for (int x = 0; x < size.x; x++)
        {
            for (int y = 0; y < size.y; y++)
            {
                int gridX = gridPos.x + x;
                int gridY = gridPos.y + y;

                // Skip cells outside grid bounds
                if (gridX < 0 || gridY < 0 ||
                    gridX >= inventoryManager.width ||
                    gridY >= inventoryManager.height)
                {
                    continue;
                }

                // Fix missing references
                if (inventoryManager.GetInventoryGrid()[gridX, gridY] != item)
                {
                    inventoryManager.GetInventoryGrid()[gridX, gridY] = item;
                }
            }
        }
    }

    private void CleanupDragVisual()
    {
        if (draggedItemIcon != null)
        {
            Destroy(draggedItemIcon);
            draggedItemIcon = null;
        }
    }

    /// <summary>
    /// Creates an inventory item UI at the cursor position
    /// </summary>
    private void CreateInventoryItemAtMousePosition(InventoryItem item, Vector2Int gridPos, Vector2 mousePosition)
    {
        if (item == null || inventoryUI == null) return;

        // Set position for the inventory system
        item.position = gridPos;

        // Refresh UI with a delay to let the inventory system update
        StartCoroutine(DelayedRefreshWithItemAtCursor(item, mousePosition));
    }

    private IEnumerator DelayedRefreshWithItemAtCursor(InventoryItem item, Vector2 cursorPosition)
    {
        // Wait a frame
        yield return null;

        if (item == null || !inventoryUI.inventoryManager.items.Contains(item))
        {
            yield break;
        }

        // Validate position and rotation
        Vector2Int size = item.GetRotatedSize();
        bool placementValid = inventoryUI.inventoryManager.UpdateItemPosition(
            item,
            item.position,
            size,
            item.isRotated
        );

        // Refresh inventory UI with item at cursor position
        inventoryUI.RefreshInventoryUI(item.GetInstanceID(), cursorPosition);
    }

    /// <summary>
    /// Animate icon smoothly returning to the slot
    /// </summary>
    private IEnumerator AnimateIconBackToSlot(GameObject icon)
    {
        if (icon == null) yield break;

        Vector3 startPos = icon.transform.position;
        Vector3 endPos = rectTransform.position;
        float duration = 0.25f;
        float elapsedTime = 0f;

        // Ensure visibility
        CanvasGroup canvasGroup = icon.GetComponent<CanvasGroup>();
        if (canvasGroup != null)
        {
            canvasGroup.alpha = 0.8f;
        }

        // Animate
        while (elapsedTime < duration)
        {
            float t = elapsedTime / duration;
            t = t * t * (3f - 2f * t); // Smooth step

            icon.transform.position = Vector3.Lerp(startPos, endPos, t);

            elapsedTime += Time.deltaTime;
            yield return null;
        }

        // Final cleanup
        icon.transform.position = endPos;
        Destroy(icon);
        draggedItemIcon = null;
    }

    /// <summary>
    /// Transfer item from this slot to another hotbar slot
    /// </summary>
    private void transferItemToAnotherHotbarSlot(HotbarSlot targetSlot)
    {
        if (storedItem == null) return;

        InventoryItem itemToTransfer = storedItem;
        SetItem(null);
        targetSlot.SetItem(itemToTransfer);
    }

    /// <summary>
    /// Swap items between this slot and another hotbar slot
    /// </summary>
    private void swapItemsWithAnotherHotbarSlot(HotbarSlot targetSlot)
    {
        if (storedItem == null || targetSlot.storedItem == null) return;

        InventoryItem ourItem = storedItem;
        InventoryItem theirItem = targetSlot.storedItem;

        SetItem(null);
        targetSlot.SetItem(null);

        SetItem(theirItem);
        targetSlot.SetItem(ourItem);
    }

    #endregion

    #region IPointerHandlers

    public void OnDrop(PointerEventData eventData)
    {
        if (eventData == null || eventData.pointerDrag == null) return;

        // Check if this slot is occupied
        if (storedItem != null) return;

        // Check if it's an inventory item being dragged
        InventoryItemUIDrag dragComponent = eventData.pointerDrag.GetComponent<InventoryItemUIDrag>();
        if (dragComponent != null && dragComponent.backendItem != null)
        {
            ProcessInventoryItemDrop(dragComponent);
        }
    }

    private void ProcessInventoryItemDrop(InventoryItemUIDrag dragComponent)
    {
        if (!EnsureInventoryReferences()) return;

        // Get the inventory item
        InventoryItem itemToAdd = dragComponent.backendItem;

        // Remove from inventory grid
        inventoryManager.RemoveItemFromGrid(itemToAdd);

        // Add to hotbar slot
        SetItem(itemToAdd);

        // Destroy inventory UI representation
        Destroy(dragComponent.gameObject);

        // Refresh inventory UI
        if (inventoryUI != null)
        {
            inventoryUI.RefreshInventoryUI();
        }
    }

    public void OnPointerEnter(PointerEventData eventData)
    {
        if (eventData.pointerDrag != null)
        {
            // Highlight slot
            GetComponent<Image>().color = new Color(0.8f, 0.8f, 1f, 1f);
        }
    }

    public void OnPointerExit(PointerEventData eventData)
    {
        // Reset color
        GetComponent<Image>().color = Color.white;
    }

    #endregion

    public void Select()
    {
        if (isSelected) return;

        isSelected = true;

        // Stop any existing animation
        if (selectionAnimation != null)
        {
            StopCoroutine(selectionAnimation);
        }

        // Start selection animation
        selectionAnimation = StartCoroutine(AnimateSelection(true));
    }

    public void Deselect()
    {
        if (!isSelected) return;

        isSelected = false;

        // Stop any existing animation
        if (selectionAnimation != null)
        {
            StopCoroutine(selectionAnimation);
        }

        // Start deselection animation
        selectionAnimation = StartCoroutine(AnimateSelection(false));
    }

    private IEnumerator AnimateSelection(bool selecting)
    {
        Vector3 startScale = rectTransform.localScale;
        Vector3 targetScale = selecting ? originalScale * selectionScale : originalScale;
        float elapsedTime = 0f;

        while (elapsedTime < selectionAnimDuration)
        {
            float t = elapsedTime / selectionAnimDuration;
            t = selectionCurve.Evaluate(t);

            rectTransform.localScale = Vector3.Lerp(startScale, targetScale, t);

            elapsedTime += Time.deltaTime;
            yield return null;
        }

        rectTransform.localScale = targetScale;
        selectionAnimation = null;
    }

    public bool IsSelected()
    {
        return isSelected;
    }

    private IEnumerator AnimateInventoryState(bool inventoryOpen)
    {
        Vector3 startScale = rectTransform.localScale;
        Vector3 targetScale;

        if (inventoryOpen)
        {
            // When inventory opens, scale up but preserve selection scale if selected
            targetScale = originalScale * inventoryOpenScale;
            if (isSelected)
            {
                targetScale *= (selectionScale / inventoryOpenScale); // Maintain relative selection scale
            }
        }
        else
        {
            // When inventory closes, return to original or selection scale
            targetScale = isSelected ? (originalScale * selectionScale) : originalScale;
        }

        float elapsedTime = 0f;

        while (elapsedTime < inventoryStateAnimDuration)
        {
            float t = elapsedTime / inventoryStateAnimDuration;
            t = selectionCurve.Evaluate(t);

            rectTransform.localScale = Vector3.Lerp(startScale, targetScale, t);

            elapsedTime += Time.deltaTime;
            yield return null;
        }

        rectTransform.localScale = targetScale;
    }
}