using System.Collections;
using System.Collections.Generic;
using UnityEngine;
using UnityEngine.UI;

// Extension method to swap vector components
public static class Vector2IntExtensions
{
    public static Vector2Int Swap(this Vector2Int v) => new Vector2Int(v.y, v.x);
}

public class InventoryItemUI : MonoBehaviour
{
    public ItemData itemData;
    public Image itemImage;
    public RectTransform rectTransform;
    public Vector2Int positionInGrid;
    public bool isRotated;

    [Header("UI Components")]
    public TMPro.TextMeshProUGUI stackCountText;
    public TMPro.TextMeshProUGUI ammoCountText;

    [Header("Font Settings")]
    [SerializeField] private TMPro.TMP_FontAsset customFont;

    // Public properties to check item state
    public bool IsDragging => isDragging;
    public bool IsAnimating => isAnimating;

    [SerializeField] private Canvas canvas;

    [Header("Animation Settings")]
    [SerializeField] private float moveSpeed = 2.5f; // Speed of lerp movement
    [SerializeField] private AnimationCurve movementCurve = AnimationCurve.EaseInOut(0, 0, 1, 1);

    [Header("Physics Settings")]
    [SerializeField] private float tiltFactor = 15f; // Maximum tilt angle
    [SerializeField] private float tiltSmoothing = 5f; // How smoothly to interpolate tilt
    [SerializeField] private float dragResistance = 8f; // How quickly velocity decreases
    [SerializeField] private float maxTiltVelocity = 1000f; // Velocity at which max tilt is reached
    [SerializeField] private float rotationMomentumFactor = 0.5f; // How much velocity affects rotation
    [SerializeField] private float minRotationSpeed = 360f; // Minimum rotation speed in degrees per second
    [SerializeField] private float maxRotationSpeed = 720f; // Maximum rotation speed in degrees per second

    private Vector2 targetPosition;
    private Quaternion targetRotation;
    private bool isAnimating = false;
    private float animationTime = 0f;
    private Vector2 startPosition;
    private bool isDragging = false;
    private Vector2 originalPivot;
    private Vector2 originalAnchorMin;
    private Vector2 originalAnchorMax;
    private Vector2 lastPosition;
    private Vector2 velocity;
    private float currentTiltX;
    private float currentTiltZ;
    private float targetTiltX;
    private float targetTiltZ;
    private bool wasJustDragging;
    private float currentRotationSpeed;
    private float rotationDirection;
    private Quaternion rotationStart;
    private Quaternion rotationTarget;
    private float rotationProgress;

    private void Awake()
    {
        rectTransform = GetComponent<RectTransform>();
        itemImage = GetComponent<Image>();

        // Store original transform properties
        originalPivot = rectTransform.pivot;
        originalAnchorMin = rectTransform.anchorMin;
        originalAnchorMax = rectTransform.anchorMax;

        // Initialize physics values
        lastPosition = rectTransform.anchoredPosition;
        velocity = Vector2.zero;
        currentTiltX = 0f;
        currentTiltZ = 0f;
        targetTiltX = 0f;
        targetTiltZ = 0f;
        wasJustDragging = false;

        // Set up initial transform properties
        rectTransform.pivot = new Vector2(0, 1);
        rectTransform.anchorMin = new Vector2(0, 1);
        rectTransform.anchorMax = new Vector2(0, 1);

        // Configure image
        if (itemImage != null)
        {
            itemImage.preserveAspect = false;
            itemImage.color = Color.white;
            itemImage.type = Image.Type.Simple;
        }

        // Initialize target position and rotation
        targetPosition = rectTransform.anchoredPosition;
        targetRotation = rectTransform.rotation;

        // Initialize rotation values
        currentRotationSpeed = minRotationSpeed;
        rotationDirection = 1f;
        rotationProgress = 1f;
    }

    // Set the item data and visual properties
    public void Set(ItemData data, bool rotated, Vector2 slotSize)
    {
        if (data == null)
        {
            Debug.LogError("Cannot set item: data is null");
            return;
        }

        itemData = data;

        // Ensure we have required components
        if (rectTransform == null)
            rectTransform = GetComponent<RectTransform>();

        if (itemImage == null)
            itemImage = GetComponent<Image>();

        // Ensure text components exist
        EnsureTextComponentsExist();

        // Set up the sprite
        if (itemImage != null && data.inventoryIcon != null)
        {
            itemImage.sprite = data.inventoryIcon;
            itemImage.preserveAspect = false;
            itemImage.type = Image.Type.Simple;
            itemImage.color = Color.white;
        }

        // Apply rotation and sizing
        SetRotation(rotated, slotSize);

        // Set name for easier debugging
        gameObject.name = $"Item_{data.itemName}";
    }

    /// <summary>
    /// Update UI text displays for stack count and ammo
    /// </summary>
    /// <param name="backendItem">The backend inventory item</param>
    public void UpdateItemTexts(InventoryItem backendItem)
    {
        if (backendItem == null || backendItem.itemData == null) return;

        // Ensure text components exist
        EnsureTextComponentsExist();

        // Update stack count for ammo - show for all ammo items (like Resident Evil 4)
        if (stackCountText != null)
        {
            if (backendItem.itemData.itemType == ItemType.Ammo)
            {
                stackCountText.text = backendItem.stackCount.ToString();
                // Activate the container (parent) instead of just the text
                stackCountText.transform.parent.gameObject.SetActive(true);
                UpdateTextPosition(stackCountText);
            }
            else
            {
                // Deactivate the container (parent) instead of just the text
                stackCountText.transform.parent.gameObject.SetActive(false);
            }
        }

        // Update ammo count for weapons - now also in bottom-right corner
        if (ammoCountText != null)
        {
            if (backendItem.itemData.itemType == ItemType.Weapon)
            {
                ammoCountText.text = $"{backendItem.currentAmmo}/{backendItem.itemData.magazineSize}";
                // Activate the container (parent) instead of just the text
                ammoCountText.transform.parent.gameObject.SetActive(true);
                UpdateTextPosition(ammoCountText);
            }
            else
            {
                // Deactivate the container (parent) instead of just the text
                ammoCountText.transform.parent.gameObject.SetActive(false);
            }
        }
    }

    /// <summary>
    /// Ensures that the text components exist, creating them if necessary
    /// </summary>
    private void EnsureTextComponentsExist()
    {
        // Create stack count text if it doesn't exist
        if (stackCountText == null)
        {
            stackCountText = CreateQuantityText("StackCountText");
        }

        // Create ammo count text if it doesn't exist
        if (ammoCountText == null)
        {
            ammoCountText = CreateQuantityText("AmmoCountText");
        }
    }

    /// <summary>
    /// Creates a quantity text component with black background for visibility
    /// </summary>
    private TMPro.TextMeshProUGUI CreateQuantityText(string name)
    {
        // Create container with black background
        GameObject containerObj = new GameObject(name + "_Container");
        containerObj.transform.SetParent(transform, false);

        // Add black background image
        Image backgroundImage = containerObj.AddComponent<Image>();
        backgroundImage.color = new Color(0f, 0f, 0f, 0.8f); // Semi-transparent black
        backgroundImage.type = Image.Type.Sliced;
        backgroundImage.raycastTarget = false; // Don't block raycasts to inventory slots

        // Create text object
        GameObject textObj = new GameObject(name);
        textObj.transform.SetParent(containerObj.transform, false);

        TMPro.TextMeshProUGUI textComponent = textObj.AddComponent<TMPro.TextMeshProUGUI>();
        textComponent.text = "";
        textComponent.fontSize = 24;
        textComponent.color = Color.white;
        textComponent.fontStyle = TMPro.FontStyles.Bold;
        textComponent.alignment = TMPro.TextAlignmentOptions.Center;
        textComponent.enableWordWrapping = false;
        textComponent.raycastTarget = false; // Don't block raycasts to inventory slots

        // Apply custom font if available
        if (customFont != null)
        {
            textComponent.font = customFont;
        }

        // Set up container rect transform - positioned in bottom-right corner
        RectTransform containerRect = containerObj.GetComponent<RectTransform>();
        containerRect.anchorMin = new Vector2(1f, 0f);
        containerRect.anchorMax = new Vector2(1f, 0f);
        containerRect.sizeDelta = new Vector2(50f, 30f);
        containerRect.anchoredPosition = new Vector2(-2f, 2f);

        // Set up text rect transform to fill container
        RectTransform textRect = textObj.GetComponent<RectTransform>();
        textRect.anchorMin = Vector2.zero;
        textRect.anchorMax = Vector2.one;
        textRect.sizeDelta = Vector2.zero;
        textRect.anchoredPosition = Vector2.zero;

        containerObj.SetActive(false);
        return textComponent;
    }

    /// <summary>
    /// Updates text position to always appear in the visual bottom-right corner, adjusting anchor based on rotation
    /// </summary>
    private void UpdateTextPosition(TMPro.TextMeshProUGUI textComponent)
    {
        if (textComponent == null) return;

        // Get the container (parent of the text component)
        Transform container = textComponent.transform.parent;
        if (container == null) return;

        // Reset container rotation to keep text upright
        container.rotation = Quaternion.identity;

        // Get container rect transform
        RectTransform containerRect = container.GetComponent<RectTransform>();
        if (containerRect == null) return;

        // Get the item's rotation (normalized to 0-360)
        float rotation = ((rectTransform.eulerAngles.z % 360) + 360) % 360;

        // Determine which corner to anchor to based on rotation to keep text in visual bottom-right
        Vector2 anchorMin, anchorMax;
        Vector2 offset;

        if (rotation >= 315 || rotation < 45) // 0 degrees (no rotation)
        {
            anchorMin = anchorMax = new Vector2(1f, 0f); // Bottom-right
            offset = new Vector2(-35f, 35f); // Even deeper inside for larger text
        }
        else if (rotation >= 45 && rotation < 135) // 90 degrees (rotated counter-clockwise)
        {
            anchorMin = anchorMax = new Vector2(0f, 0f); // Bottom-left (becomes visual bottom-right)
            offset = new Vector2(25f, 25f); // Even deeper inside for larger text
        }
        else if (rotation >= 135 && rotation < 225) // 180 degrees
        {
            anchorMin = anchorMax = new Vector2(0f, 1f); // Top-left (becomes visual bottom-right)
            offset = new Vector2(25f, -25f); // Even deeper inside for larger text
        }
        else // 270 degrees (rotation >= 225 && rotation < 315)
        {
            anchorMin = anchorMax = new Vector2(1f, 1f); // Top-right (becomes visual bottom-right)
            offset = new Vector2(-25f, -25f); // Even deeper inside for larger text
        }

        // Apply the anchor and position
        containerRect.anchorMin = anchorMin;
        containerRect.anchorMax = anchorMax;
        containerRect.anchoredPosition = offset;
    }

    /// <summary>
    /// Updates all text positions to ensure they stay in the correct position
    /// </summary>
    private void UpdateAllTextPositions()
    {
        UpdateTextPosition(stackCountText);
        UpdateTextPosition(ammoCountText);
    }

    public void SetDragMode(bool dragging)
    {
        isDragging = dragging;

        if (dragging)
        {
            // Center pivot for rotation around cursor during drag
            rectTransform.pivot = new Vector2(0.5f, 0.5f);
            // Reset physics values when starting drag
            lastPosition = rectTransform.anchoredPosition;
            velocity = Vector2.zero;
        }
        else
        {
            // Mark that we just stopped dragging
            wasJustDragging = true;
            // Restore original transform properties
            rectTransform.pivot = originalPivot;
            rectTransform.anchorMin = originalAnchorMin;
            rectTransform.anchorMax = originalAnchorMax;
        }
    }

    // Handle rotation of the item
    public void SetRotation(bool setRotated, Vector2 slotSize, bool isDragging = false)
    {
        if (rectTransform == null)
            rectTransform = GetComponent<RectTransform>();

        if (itemData == null)
        {
            Debug.LogError("Cannot set rotation: itemData is null on " + gameObject.name);
            return;
        }

        isRotated = setRotated;

        // Calculate non-rotated size
        Vector2 itemSize = new Vector2(
            itemData.size.x * slotSize.x,
            itemData.size.y * slotSize.y
        );

        // Apply size consistently
        rectTransform.sizeDelta = itemSize;

        // Calculate rotation speed based on velocity
        if (isDragging)
        {
            float velocityMagnitude = velocity.magnitude;
            currentRotationSpeed = Mathf.Lerp(minRotationSpeed, maxRotationSpeed,
                Mathf.Clamp01(velocityMagnitude / maxTiltVelocity));

            // Determine rotation direction based on velocity
            rotationDirection = (velocity.x > 0) ? 1f : -1f;
        }
        else
        {
            currentRotationSpeed = minRotationSpeed;
            rotationDirection = 1f;
        }

        // Store current rotation as start
        rotationStart = rectTransform.rotation;
        rotationProgress = 0f;

        if (isDragging)
        {
            // During drag, rotate around cursor
            rotationTarget = Quaternion.Euler(0, 0, isRotated ? 90 : 0);
        }
        else
        {
            // When not dragging, use original pivot-based rotation
            if (isRotated)
            {
                rectTransform.pivot = new Vector2(1f, 1f);
                rotationTarget = Quaternion.Euler(0, 0, 90);
            }
            else
            {
                rectTransform.pivot = new Vector2(0, 1);
                rotationTarget = Quaternion.identity;
            }
        }

        // Start animation
        StartAnimation();

        // Update text positions to ensure they stay in bottom-right corner
        UpdateAllTextPositions();
    }

    // Set target position for lerping
    public void SetTargetPosition(Vector2 newPosition)
    {
        startPosition = rectTransform.anchoredPosition;
        targetPosition = newPosition;
        StartAnimation();
    }

    private void StartAnimation()
    {
        isAnimating = true;
        animationTime = 0f;
    }

    private void UpdatePhysics()
    {
        if (isDragging)
        {
            // Calculate velocity based on position change
            Vector2 currentPos = rectTransform.anchoredPosition;
            velocity = (currentPos - lastPosition) / Time.deltaTime;
            lastPosition = currentPos;

            // Calculate tilt based on velocity
            float velocityMagnitude = velocity.magnitude;
            float tiltRatio = Mathf.Clamp01(velocityMagnitude / maxTiltVelocity);

            // Calculate tilt angles based on movement direction
            targetTiltZ = -velocity.x * tiltRatio * tiltFactor / maxTiltVelocity;
            targetTiltX = velocity.y * tiltRatio * tiltFactor / maxTiltVelocity;
        }
        else
        {
            // Apply drag to velocity when not dragging
            velocity = Vector2.Lerp(velocity, Vector2.zero, dragResistance * Time.deltaTime);

            if (wasJustDragging)
            {
                wasJustDragging = false;
                velocity *= 0.5f;
            }

            // Gradually return tilt to zero
            targetTiltX = 0f;
            targetTiltZ = 0f;
        }

        // Smoothly interpolate current tilt
        currentTiltX = Mathf.Lerp(currentTiltX, targetTiltX, tiltSmoothing * Time.deltaTime);
        currentTiltZ = Mathf.Lerp(currentTiltZ, targetTiltZ, tiltSmoothing * Time.deltaTime);

        // Create base tilt rotation
        Quaternion tiltRotation = Quaternion.Euler(currentTiltX, 0f, currentTiltZ);

        // Update rotation progress
        if (rotationProgress < 1f)
        {
            float rotationStep = (currentRotationSpeed * Time.deltaTime) / 90f;
            rotationProgress = Mathf.Min(rotationProgress + rotationStep, 1f);

            // Use a custom curve that incorporates velocity for the rotation
            float t = rotationProgress;
            if (isDragging || wasJustDragging)
            {
                // Add some "punch" to the rotation based on velocity
                float velocityInfluence = Mathf.Clamp01(velocity.magnitude / maxTiltVelocity) * rotationMomentumFactor;
                t = Mathf.Sin(t * Mathf.PI * (1f + velocityInfluence)) * (1f - t) + t;
            }
            else
            {
                // Use smooth ease-in-out curve for normal rotation
                t = t * t * (3f - 2f * t);
            }

            // Apply rotation with tilt
            Quaternion baseRotation = Quaternion.Lerp(rotationStart, rotationTarget, t);
            rectTransform.rotation = baseRotation * tiltRotation;
        }
        else if (isDragging || wasJustDragging)
        {
            // Just apply tilt when not rotating
            rectTransform.rotation = rotationTarget * tiltRotation;
        }
    }

    private void Update()
    {
        if (!isAnimating && !isDragging && !wasJustDragging &&
            Mathf.Approximately(currentTiltX, 0f) && Mathf.Approximately(currentTiltZ, 0f) &&
            rotationProgress >= 1f)
            return;

        // Update physics first
        UpdatePhysics();

        // Update text positions to keep them in correct position during rotation
        UpdateAllTextPositions();

        if (!isAnimating) return;

        // Update animation time
        animationTime += Time.deltaTime * moveSpeed;

        float t = movementCurve.Evaluate(Mathf.Clamp01(animationTime));

        // Only lerp position if not dragging
        if (!isDragging)
        {
            rectTransform.anchoredPosition = Vector2.Lerp(startPosition, targetPosition, t);
        }

        // Check if animations are complete
        bool positionComplete = t >= 1f;
        bool rotationComplete = rotationProgress >= 1f;

        if (positionComplete && rotationComplete)
        {
            isAnimating = false;
            if (!isDragging)
            {
                // Ensure final position and rotation are exact
                rectTransform.anchoredPosition = targetPosition;
                if (!isDragging && !wasJustDragging)
                {
                    rectTransform.rotation = rotationTarget;
                }
            }
        }
    }

    // Ensure pixel-perfect alignment after animation
    private void LateUpdate()
    {
        if (!isAnimating && canvas != null && canvas.scaleFactor > 0)
        {
            // Snap to pixel grid only when not animating
            rectTransform.anchoredPosition = new Vector2(
                Mathf.Round(rectTransform.anchoredPosition.x * canvas.scaleFactor) / canvas.scaleFactor,
                Mathf.Round(rectTransform.anchoredPosition.y * canvas.scaleFactor) / canvas.scaleFactor
            );
        }
    }

    /// <summary>
    /// Resets the item's visual state to its default appearance
    /// </summary>
    public void ResetItemVisuals()
    {
        if (itemImage != null)
        {
            itemImage.color = Color.white;
        }

        // Reset transform properties
        if (rectTransform != null)
        {
            // Only reset rotation if we're not in the middle of a rotation animation
            if (!isAnimating)
            {
                rectTransform.localRotation = isRotated ? Quaternion.Euler(0, 0, 90) : Quaternion.identity;
            }

            // Don't reset these during selection to prevent visual glitches
            if (!isDragging && !wasJustDragging)
            {
                currentTiltX = 0f;
                currentTiltZ = 0f;
                targetTiltX = 0f;
                targetTiltZ = 0f;
                velocity = Vector2.zero;

                // Ensure correct pivot based on rotation state
                rectTransform.pivot = isRotated ? new Vector2(1f, 1f) : new Vector2(0, 1);
                rectTransform.anchorMin = new Vector2(0, 1);
                rectTransform.anchorMax = new Vector2(0, 1);
            }
        }

        // Reset animation states only if we're not in the middle of something
        if (!isDragging && !wasJustDragging && !isAnimating)
        {
            isAnimating = false;
            animationTime = 0f;
            rotationProgress = 1f;
        }
    }
}
