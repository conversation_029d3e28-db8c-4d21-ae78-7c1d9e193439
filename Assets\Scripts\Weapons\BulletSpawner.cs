using UnityEngine;

/// <summary>
/// Handles spawning bullets from weapon fire points
/// </summary>
public class BulletSpawner : MonoBehaviour
{
    [<PERSON><PERSON>("Bullet Prefabs")]
    [Tooltip("Default bullet prefab")]
    public GameObject defaultBulletPrefab;
    
    [<PERSON><PERSON><PERSON>("Pistol bullet prefab")]
    public GameObject pistolBulletPrefab;
    
    [<PERSON><PERSON><PERSON>("Rifle bullet prefab")]
    public GameObject rifleBulletPrefab;
    
    [<PERSON><PERSON><PERSON>("Shotgun pellet prefab")]
    public GameObject shotgunPelletPrefab;
    
    [Header("Fire Points")]
    [Tooltip("Default fire point (if weapon-specific not found)")]
    public Transform defaultFirePoint;
    
    [Tooltip("Pistol fire point")]
    public Transform pistolFirePoint;
    
    [Toolt<PERSON>("Rifle fire point")]
    public Transform rifleFirePoint;
    
    [Tooltip("Shotgun fire point")]
    public Transform shotgunFirePoint;
    
    [Header("Bullet Properties")]
    [Tooltip("Default bullet speed")]
    public float defaultBulletSpeed = 50f;
    
    [<PERSON>lt<PERSON>("Default bullet damage")]
    public float defaultBulletDamage = 25f;
    
    [<PERSON>lt<PERSON>("Default bullet range")]
    public float defaultBulletRange = 100f;
    
    [<PERSON><PERSON>("Shotgun Settings")]
    [Tooltip("Number of pellets per shotgun shot")]
    public int shotgunPelletCount = 8;
    
    [Tooltip("Spread angle for shotgun pellets")]
    public float shotgunSpread = 15f;
    
    /// <summary>
    /// Spawn a bullet for the given weapon type
    /// </summary>
    public void SpawnBullet(WeaponType weaponType, Vector3 direction, InventoryItem weapon = null)
    {
        switch (weaponType)
        {
            case WeaponType.Pistol:
                SpawnSingleBullet(pistolBulletPrefab, pistolFirePoint, direction, weapon);
                break;
                
            case WeaponType.Rifle:
                SpawnSingleBullet(rifleBulletPrefab, rifleFirePoint, direction, weapon);
                break;
                
            case WeaponType.Shotgun:
                SpawnShotgunPellets(shotgunPelletPrefab, shotgunFirePoint, direction, weapon);
                break;
                
            default:
                SpawnSingleBullet(defaultBulletPrefab, defaultFirePoint, direction, weapon);
                break;
        }
    }
    
    /// <summary>
    /// Spawn a single bullet
    /// </summary>
    private void SpawnSingleBullet(GameObject bulletPrefab, Transform firePoint, Vector3 direction, InventoryItem weapon)
    {
        // Use defaults if specific prefab/point not assigned
        GameObject prefab = bulletPrefab != null ? bulletPrefab : defaultBulletPrefab;
        Transform spawnPoint = firePoint != null ? firePoint : defaultFirePoint;
        
        if (prefab == null || spawnPoint == null)
        {
            Debug.LogError("BulletSpawner: Missing bullet prefab or fire point!");
            return;
        }
        
        // Spawn bullet
        GameObject bulletObj = Instantiate(prefab, spawnPoint.position, Quaternion.identity);
        Bullet bullet = bulletObj.GetComponent<Bullet>();
        
        if (bullet != null)
        {
            // Get weapon-specific properties or use defaults
            float speed = GetBulletSpeed(weapon);
            float damage = GetBulletDamage(weapon);
            float range = GetBulletRange(weapon);
            
            // Initialize bullet
            bullet.Initialize(direction, speed, damage, range);
        }
        else
        {
            Debug.LogError("BulletSpawner: Bullet prefab missing Bullet component!");
        }
    }
    
    /// <summary>
    /// Spawn multiple pellets for shotgun
    /// </summary>
    private void SpawnShotgunPellets(GameObject pelletPrefab, Transform firePoint, Vector3 baseDirection, InventoryItem weapon)
    {
        // Use defaults if specific prefab/point not assigned
        GameObject prefab = pelletPrefab != null ? pelletPrefab : defaultBulletPrefab;
        Transform spawnPoint = firePoint != null ? firePoint : defaultFirePoint;
        
        if (prefab == null || spawnPoint == null)
        {
            Debug.LogError("BulletSpawner: Missing pellet prefab or fire point!");
            return;
        }
        
        // Get weapon properties
        float speed = GetBulletSpeed(weapon);
        float damage = GetBulletDamage(weapon) / shotgunPelletCount; // Divide damage among pellets
        float range = GetBulletRange(weapon);
        
        // Spawn multiple pellets with spread
        for (int i = 0; i < shotgunPelletCount; i++)
        {
            // Calculate spread direction
            Vector3 spreadDirection = CalculateSpreadDirection(baseDirection, shotgunSpread);
            
            // Spawn pellet
            GameObject pelletObj = Instantiate(prefab, spawnPoint.position, Quaternion.identity);
            Bullet pellet = pelletObj.GetComponent<Bullet>();
            
            if (pellet != null)
            {
                pellet.Initialize(spreadDirection, speed, damage, range);
            }
        }
    }
    
    /// <summary>
    /// Calculate spread direction for shotgun pellets
    /// </summary>
    private Vector3 CalculateSpreadDirection(Vector3 baseDirection, float spreadAngle)
    {
        // Random angles within spread cone
        float randomAngle = Random.Range(-spreadAngle, spreadAngle);
        float randomRotation = Random.Range(0f, 360f);
        
        // Apply spread
        Quaternion spreadRotation = Quaternion.AngleAxis(randomAngle, Vector3.up) * 
                                   Quaternion.AngleAxis(randomRotation, baseDirection);
        
        return spreadRotation * baseDirection;
    }
    
    /// <summary>
    /// Get bullet speed for weapon (or default)
    /// </summary>
    private float GetBulletSpeed(InventoryItem weapon)
    {
        if (weapon?.itemData?.weaponType == WeaponType.Rifle)
            return defaultBulletSpeed * 1.5f; // Rifles shoot faster
        else if (weapon?.itemData?.weaponType == WeaponType.Shotgun)
            return defaultBulletSpeed * 0.8f; // Shotguns shoot slower
        
        return defaultBulletSpeed;
    }
    
    /// <summary>
    /// Get bullet damage for weapon (or default)
    /// </summary>
    private float GetBulletDamage(InventoryItem weapon)
    {
        if (weapon?.itemData?.weaponType == WeaponType.Rifle)
            return defaultBulletDamage * 1.2f; // Rifles do more damage
        else if (weapon?.itemData?.weaponType == WeaponType.Shotgun)
            return defaultBulletDamage * 1.5f; // Shotguns do more total damage
        
        return defaultBulletDamage;
    }
    
    /// <summary>
    /// Get bullet range for weapon (or default)
    /// </summary>
    private float GetBulletRange(InventoryItem weapon)
    {
        if (weapon?.itemData?.weaponType == WeaponType.Rifle)
            return defaultBulletRange * 2f; // Rifles have longer range
        else if (weapon?.itemData?.weaponType == WeaponType.Shotgun)
            return defaultBulletRange * 0.5f; // Shotguns have shorter range
        
        return defaultBulletRange;
    }
    
    /// <summary>
    /// Get fire point for weapon type
    /// </summary>
    public Transform GetFirePoint(WeaponType weaponType)
    {
        switch (weaponType)
        {
            case WeaponType.Pistol:
                return pistolFirePoint != null ? pistolFirePoint : defaultFirePoint;
            case WeaponType.Rifle:
                return rifleFirePoint != null ? rifleFirePoint : defaultFirePoint;
            case WeaponType.Shotgun:
                return shotgunFirePoint != null ? shotgunFirePoint : defaultFirePoint;
            default:
                return defaultFirePoint;
        }
    }
}
