using System.Collections;
using System.Collections.Generic;
using UnityEngine;
using UnityEngine.UI;
using UnityEngine.EventSystems;

public class InventoryUI : MonoBehaviour, IPointerClickHandler
{
    [Header("References")]
    public InventoryManager inventoryManager;
    public GameObject slotPrefab;
    public GameObject itemUIPrefab;
    public Transform slotContainer;
    public Transform itemContainer;
    public Vector2 slotSize = new Vector2();

    [Header("Visual Effects")]
    [SerializeField] private Material retroMaterial;

    // Singleton instance
    public static InventoryUI Instance { get; private set; }

    // Properties for external access
    public Vector2 GridOffset => gridOffset;
    public RectTransform GridRectTransform => gridRectTransform;
    public Vector2 GridSpacing => gridLayout != null ? gridLayout.spacing : Vector2.zero;

    // Private members
    private GridLayoutGroup gridLayout;
    private RectTransform gridRectTransform;
    private Vector2 gridOffset;
    private Dictionary<int, InventoryItemUI> itemUIMapping = new Dictionary<int, InventoryItemUI>();

    // Track item that should spawn at cursor position
    private int itemToPositionAtCursor = -1;
    private Vector2 cursorSpawnPosition = Vector2.zero;

    [Header("Animation Settings")]
    [SerializeField] private float openDuration = 0.3f;
    [SerializeField] private float closeDuration = 0.2f;
    [SerializeField] private AnimationCurve openCurve = AnimationCurve.EaseInOut(0, 0, 1, 1);
    [SerializeField] private AnimationCurve closeCurve = AnimationCurve.EaseInOut(0, 0, 1, 1);
    [SerializeField] private Vector2 closedPosition = new Vector2(-800f, 0f); // Off-screen to the left
    [SerializeField] private Vector2 openPosition = new Vector2(-300f, 0f); // Left of center position

    private RectTransform rectTransform;
    private CanvasGroup canvasGroup;
    private Coroutine animationCoroutine;

    private void Awake()
    {
        // Set up singleton
        if (Instance == null)
            Instance = this;
        else
            Destroy(gameObject);

        // Get components
        rectTransform = GetComponent<RectTransform>();
        canvasGroup = GetComponent<CanvasGroup>();
        if (canvasGroup == null)
            canvasGroup = gameObject.AddComponent<CanvasGroup>();

        // Set up RectTransform anchoring
        rectTransform.anchorMin = new Vector2(0.5f, 0.5f); // Center of screen
        rectTransform.anchorMax = new Vector2(0.5f, 0.5f);
        rectTransform.pivot = new Vector2(0.5f, 0.5f);

        // Initialize position
        rectTransform.anchoredPosition = closedPosition;
        canvasGroup.alpha = 0f;

        // Ensure we have the necessary components for click detection
        EnsureRequiredComponents();
    }

    private void EnsureRequiredComponents()
    {
        // Ensure we have an Image component for the background
        Image backgroundImage = GetComponent<Image>();
        if (backgroundImage == null)
        {
            backgroundImage = gameObject.AddComponent<Image>();
            backgroundImage.color = new Color(0, 0, 0, 0.5f); // Semi-transparent black background
        }

        // Make sure the Image can receive raycasts
        backgroundImage.raycastTarget = true;

        // Apply the retro material if assigned
        if (retroMaterial != null)
        {
            backgroundImage.material = retroMaterial;
        }
    }

    private void Start()
    {
        // Subscribe to inventory events
        inventoryManager.OnInventoryChanged += RefreshUI;

        // Initialize the grid
        GenerateGrid();

        // Configure item container
        ConfigureItemContainer();

        RefreshUI();
    }

    private void ConfigureItemContainer()
    {
        // Make sure the item container doesn't have a visible background
        Image itemContainerImage = itemContainer.GetComponent<Image>();
        if (itemContainerImage != null)
        {
            itemContainerImage.enabled = false;
        }

        // Check for any child objects in the item container that might be the white square
        for (int i = 0; i < itemContainer.childCount; i++)
        {
            Transform child = itemContainer.GetChild(i);
            // Skip if this is a functional item
            if (child.GetComponent<InventoryItemUI>() != null)
                continue;

            // Disable any placeholder images
            Image childImage = child.GetComponent<Image>();
            if (childImage != null)
            {
                childImage.enabled = false;
            }
        }
    }

    private void OnEnable()
    {
        // Start opening animation
        if (animationCoroutine != null)
            StopCoroutine(animationCoroutine);
        animationCoroutine = StartCoroutine(AnimateInventory(true));

        // Refresh UI when inventory is opened
        StartCoroutine(DelayedRefresh());
    }

    private void OnDisable()
    {
        // Stop any ongoing animation
        if (animationCoroutine != null)
            StopCoroutine(animationCoroutine);

        // Reset to closed state
        rectTransform.anchoredPosition = closedPosition;
        canvasGroup.alpha = 0f;
    }

    private IEnumerator AnimateInventory(bool opening)
    {
        float elapsedTime = 0f;
        float duration = opening ? openDuration : closeDuration;
        AnimationCurve curve = opening ? openCurve : closeCurve;

        Vector2 startPos = rectTransform.anchoredPosition;
        Vector2 targetPos = opening ? openPosition : closedPosition;
        float startAlpha = canvasGroup.alpha;
        float targetAlpha = opening ? 1f : 0f;

        while (elapsedTime < duration)
        {
            float t = elapsedTime / duration;
            t = curve.Evaluate(t);

            // Animate position
            rectTransform.anchoredPosition = Vector2.Lerp(startPos, targetPos, t);

            // Animate alpha
            canvasGroup.alpha = Mathf.Lerp(startAlpha, targetAlpha, t);

            elapsedTime += Time.deltaTime;
            yield return null;
        }

        // Ensure we reach the final state
        rectTransform.anchoredPosition = targetPos;
        canvasGroup.alpha = targetAlpha;

        animationCoroutine = null;
    }

    private IEnumerator DelayedRefresh()
    {
        yield return null;  // Wait one frame
        RefreshUI();
    }

    // Create the grid slots
    private void GenerateGrid()
    {
        // Clear existing slots
        foreach (Transform child in slotContainer)
            Destroy(child.gameObject);

        // Configure grid layout
        gridLayout = slotContainer.GetComponent<GridLayoutGroup>();
        if (gridLayout == null)
            gridLayout = slotContainer.gameObject.AddComponent<GridLayoutGroup>();

        gridLayout.constraint = GridLayoutGroup.Constraint.FixedColumnCount;
        gridLayout.constraintCount = inventoryManager.width;

        // Create slots
        for (int y = 0; y < inventoryManager.height; y++)
        {
            for (int x = 0; x < inventoryManager.width; x++)
            {
                GameObject slotObj = Instantiate(slotPrefab, slotContainer);
                InventorySlot slot = slotObj.GetComponent<InventorySlot>();
                if (slot != null)
                {
                    slot.Initialize(x, y);
                }
            }
        }

        // Calculate grid origin
        gridRectTransform = slotContainer.GetComponent<RectTransform>();
        CalculateGridOrigin();
    }

    private void CalculateGridOrigin()
    {
        // Get grid corners
        Vector3[] corners = new Vector3[4];
        gridRectTransform.GetLocalCorners(corners);
        Vector2 topLeft = corners[1]; // Index 1 is top-left corner

        // Account for padding
        gridOffset = new Vector2(
            topLeft.x + gridLayout.padding.left,
            topLeft.y - gridLayout.padding.top
        );
    }

    // Public method to force a refresh
    public void RefreshInventoryUI()
    {
        RefreshUI();
    }

    // Public method to refresh with a specific item positioned at the cursor
    public void RefreshInventoryUI(int itemInstanceId, Vector2 cursorPosition)
    {
        // Store the info for use during refresh
        itemToPositionAtCursor = itemInstanceId;
        cursorSpawnPosition = cursorPosition;

        // Perform the refresh
        RefreshUI();

        // Reset the special case flags after refresh
        itemToPositionAtCursor = -1;
        cursorSpawnPosition = Vector2.zero;
    }

    // Update all item UI elements
    private void RefreshUI()
    {
        // Track current items
        HashSet<int> currentItemKeys = new HashSet<int>();

        // First, reset ALL slots to empty state
        ResetAllSlots();

        // Validate inventory grid state - safety check
        ValidateInventoryGrid();

        // Find the hotbar to check for items that are stored there
        Hotbar hotbar = FindObjectOfType<Hotbar>();
        HashSet<InventoryItem> hotbarItems = new HashSet<InventoryItem>();

        if (hotbar != null)
        {
            // Collect all items currently in the hotbar
            foreach (HotbarSlot slot in hotbar.hotbarSlots)
            {
                if (slot != null && slot.storedItem != null)
                {
                    hotbarItems.Add(slot.storedItem);
                }
            }
        }

        // Debug info
        Debug.Log($"RefreshUI: Found {hotbarItems.Count} items in hotbar, {inventoryManager.GetAllItems().Count} total items");

        // Add or update existing items that are not in the hotbar
        foreach (var item in inventoryManager.GetAllItems())
        {
            // Skip items that don't exist anymore
            if (item == null)
            {
                Debug.LogError("Null item in inventory items list!");
                continue;
            }

            // Skip items that are currently in the hotbar
            if (hotbarItems.Contains(item))
            {
                Debug.Log($"Skipping item {item.GetName()} - it's in the hotbar");
                continue;
            }

            // Skip items with invalid positions (these are likely dropped items)
            if (item.position.x < 0 || item.position.y < 0 ||
                item.position.x >= inventoryManager.width ||
                item.position.y >= inventoryManager.height)
            {
                Debug.Log($"Item {item.GetName()} has invalid position {item.position} - skipping UI update (likely dropped from hotbar)");
                continue;
            }

            AddOrUpdateItemUI(item);
            currentItemKeys.Add(item.GetInstanceID());

            // Mark slots as occupied for this item
            MarkSlotsAsOccupied(item);
        }

        // Remove any items that are no longer in inventory or are now in hotbar
        RemoveStaleItemUIs(currentItemKeys);
    }

    // Validate and potentially fix the inventory grid
    private void ValidateInventoryGrid()
    {
        // Get all the actual item references
        var items = inventoryManager.GetAllItems();
        var grid = inventoryManager.GetInventoryGrid();

        // Check each grid cell to ensure it references a valid item
        for (int x = 0; x < inventoryManager.width; x++)
        {
            for (int y = 0; y < inventoryManager.height; y++)
            {
                InventoryItem cellItem = grid[x, y];

                // Skip empty cells
                if (cellItem == null) continue;

                // If the cell references an item not in the items list, clear it
                if (!items.Contains(cellItem))
                {
                    Debug.LogWarning($"Found invalid item reference at ({x},{y}) - clearing");
                    grid[x, y] = null;
                }

                // Check if the item's position and size actually cover this cell
                bool validCell = false;
                Vector2Int itemPos = cellItem.position;
                Vector2Int itemSize = cellItem.GetRotatedSize();

                if (x >= itemPos.x && x < itemPos.x + itemSize.x &&
                    y >= itemPos.y && y < itemPos.y + itemSize.y)
                {
                    validCell = true;
                }

                if (!validCell)
                {
                    Debug.LogWarning($"Cell at ({x},{y}) references item at {itemPos} with size {itemSize} - clearing invalid reference");
                    grid[x, y] = null;
                }
            }
        }
    }

    // Reset all slots to unoccupied state
    private void ResetAllSlots()
    {
        foreach (Transform slotTransform in slotContainer)
        {
            InventorySlot slot = slotTransform.GetComponent<InventorySlot>();
            if (slot != null)
            {
                slot.SetOccupied(false);
            }
        }
    }

    // Mark grid slots as occupied for a specific item
    private void MarkSlotsAsOccupied(InventoryItem item)
    {
        if (item == null) return;

        Vector2Int itemPos = item.position;
        Vector2Int itemSize = item.GetRotatedSize();

        // Update slot opacity for each cell the item occupies
        for (int x = 0; x < itemSize.x; x++)
        {
            for (int y = 0; y < itemSize.y; y++)
            {
                Vector2Int slotPos = new Vector2Int(itemPos.x + x, itemPos.y + y);

                // Validate position is in grid bounds
                if (slotPos.x < 0 || slotPos.y < 0 ||
                    slotPos.x >= inventoryManager.width ||
                    slotPos.y >= inventoryManager.height)
                {
                    continue;
                }

                InventorySlot slot = GetSlotAtPosition(slotPos);
                if (slot != null)
                {
                    slot.SetOccupied(true);
                }
            }
        }
    }

    // Remove UI elements for items that are no longer in the inventory
    private void RemoveStaleItemUIs(HashSet<int> currentItemKeys)
    {
        List<int> keysToRemove = new List<int>();

        foreach (var key in itemUIMapping.Keys)
        {
            if (!currentItemKeys.Contains(key))
            {
                keysToRemove.Add(key);
            }
        }

        foreach (var key in keysToRemove)
        {
            if (itemUIMapping.TryGetValue(key, out InventoryItemUI itemUI))
            {
                Debug.Log($"Removing stale item UI with key {key}");
                Destroy(itemUI.gameObject);
                itemUIMapping.Remove(key);
            }
        }
    }

    private InventorySlot GetSlotAtPosition(Vector2Int position)
    {
        // Find the slot at the given grid position
        foreach (Transform slotTransform in slotContainer)
        {
            InventorySlot slot = slotTransform.GetComponent<InventorySlot>();
            if (slot != null && slot.GetGridPosition() == position)
            {
                return slot;
            }
        }
        return null;
    }

    // Add or update a single item UI
    public void AddOrUpdateItemUI(InventoryItem item)
    {
        if (item == null || item.itemData == null)
        {
            Debug.LogError("Cannot add/update UI: item or item.itemData is null");
            return;
        }

        int key = item.GetInstanceID();
        InventoryItemUI itemUI;

        // Check if this is the item that should be positioned at cursor
        bool spawnAtCursor = (key == itemToPositionAtCursor && cursorSpawnPosition != Vector2.zero);

        // Create new UI if needed
        if (!itemUIMapping.TryGetValue(key, out itemUI))
        {
            try
            {
                // Instantiate UI elements
                GameObject itemUIObj = Instantiate(itemUIPrefab, itemContainer);
                if (itemUIObj == null)
                {
                    Debug.LogError("Failed to instantiate itemUIPrefab");
                    return;
                }

                itemUI = itemUIObj.GetComponent<InventoryItemUI>();
                if (itemUI == null)
                {
                    Debug.LogError("ItemUI component not found on instantiated prefab");
                    Destroy(itemUIObj);
                    return;
                }

                // Configure image component
                Image image = itemUIObj.GetComponent<Image>();
                if (image != null)
                {
                    image.raycastTarget = true;
                    image.preserveAspect = false;
                    image.type = Image.Type.Simple;
                    image.color = Color.white;
                }

                // Set up drag component
                InventoryItemUIDrag dragComponent = itemUIObj.GetComponent<InventoryItemUIDrag>();
                if (dragComponent != null)
                    dragComponent.backendItem = item;
                else
                    Debug.LogWarning("InventoryItemUIDrag component not found on itemUIPrefab!");

                // If this item should spawn at cursor, set its position now
                if (spawnAtCursor && itemUIObj != null)
                {
                    RectTransform itemRect = itemUIObj.GetComponent<RectTransform>();
                    if (itemRect != null)
                    {
                        itemRect.position = cursorSpawnPosition;
                    }
                }

                itemUIMapping.Add(key, itemUI);
            }
            catch (System.Exception e)
            {
                Debug.LogError($"Error creating item UI: {e.Message}");
                return;
            }
        }

        // If we somehow still don't have a valid itemUI, return
        if (itemUI == null)
        {
            Debug.LogError("Failed to create or retrieve valid itemUI");
            return;
        }

        // Update UI properties
        itemUI.positionInGrid = item.position;

        // Configure transform
        RectTransform rect = itemUI.GetComponent<RectTransform>();
        if (rect == null)
        {
            Debug.LogError("RectTransform missing on itemUI");
            return;
        }

        rect.pivot = new Vector2(0, 1);
        rect.anchorMin = new Vector2(0, 1);
        rect.anchorMax = new Vector2(0, 1);

        // Calculate grid position
        Vector2 itemPos = new Vector2(
            gridOffset.x + (item.position.x * (slotSize.x + gridLayout.spacing.x)),
            gridOffset.y - (item.position.y * (slotSize.y + gridLayout.spacing.y))
        );

        // If this is a new UI that should spawn at cursor, use a special animation
        // from cursor to grid position
        if (spawnAtCursor && !itemUIMapping.ContainsKey(key))
        {
            // Item is already positioned at cursor, now animate to grid
            itemUI.SetTargetPosition(itemPos);
        }
        else
        {
            // Standard behavior - set target position for lerping
            itemUI.SetTargetPosition(itemPos);
        }

        try
        {
            itemUI.Set(item.itemData, item.isRotated, slotSize);
            // Update text displays for stack count and ammo
            itemUI.UpdateItemTexts(item);
        }
        catch (System.Exception e)
        {
            Debug.LogError($"Error setting item data: {e.Message}");
        }
    }

    // Remove a specific item
    public void RemoveItemUI(InventoryItem item)
    {
        int key = item.GetInstanceID();
        if (itemUIMapping.TryGetValue(key, out InventoryItemUI itemUI))
        {
            Destroy(itemUI.gameObject);
            itemUIMapping.Remove(key);
        }
    }

    public void OnPointerClick(PointerEventData eventData)
    {
        // Only handle left mouse button clicks
        if (eventData.button != PointerEventData.InputButton.Left) return;

        // Check if we clicked directly on the inventory background
        // This means the click wasn't handled by any other UI elements (items, slots, etc.)
        if (eventData.pointerPress == gameObject || eventData.pointerPress == null)
        {
            // Get and deselect the currently selected item
            var selectedItem = InventoryItemUIDrag.GetSelectedItem();
            if (selectedItem != null)
            {
                selectedItem.Deselect();
            }
        }
    }

    /// <summary>
    /// Force cleanup of any stuck drag visuals in the inventory system
    /// </summary>
    public void ForceCleanupDragVisuals()
    {
        // Clear all drag highlights
        InventoryItemUIDrag.ClearCurrentDraggedItem();

        // Find and cleanup any stuck hotbar drag visuals
        HotbarSlot[] hotbarSlots = FindObjectsOfType<HotbarSlot>();
        foreach (var slot in hotbarSlots)
        {
            slot.ForceCleanupDragVisuals();
        }

        // Refresh the inventory UI to ensure everything is in the correct state
        RefreshInventoryUI();

        Debug.Log("Forced cleanup of all inventory drag visuals");
    }

    #if UNITY_EDITOR
    // Debug visualization
    private void OnDrawGizmos()
    {
        if (!Application.isPlaying || gridRectTransform == null || slotSize.x <= 0 || slotSize.y <= 0)
            return;

        // Draw grid origin
        Vector3 worldPos = gridRectTransform.TransformPoint(gridOffset);
        Gizmos.color = Color.red;
        Gizmos.DrawSphere(worldPos, 5f);

        // Draw grid bounds
        Vector3[] corners = new Vector3[4];
        gridRectTransform.GetWorldCorners(corners);
        Gizmos.color = Color.cyan;
        Gizmos.DrawLine(corners[0], corners[1]);
        Gizmos.DrawLine(corners[1], corners[2]);
        Gizmos.DrawLine(corners[2], corners[3]);
        Gizmos.DrawLine(corners[3], corners[0]);

        // Draw first slot center
        Vector3 firstSlotCenter = worldPos + new Vector3(
            slotSize.x / 2f,
            -slotSize.y / 2f,
            0
        );
        Gizmos.color = Color.green;
        Gizmos.DrawCube(firstSlotCenter, Vector3.one * 5f);
    }
    #endif
}

