{"Instructions Readme": "1) Open Chrome or Edge, 2) go to chrome://tracing, 3) click Load, 4) navigate to this file.", "processInfo": {}, "traceEvents": [{"pid": 18384, "tid": -1, "ph": "M", "name": "process_name", "args": {"name": "BeeDriver"}}, {"pid": 18384, "tid": -1, "ph": "M", "name": "process_sort_index", "args": {"sort_index": "-2"}}, {"pid": 18384, "tid": 606, "ph": "M", "name": "thread_name", "args": {"name": "Thread Pool Worker"}}, {"pid": 18384, "tid": 606, "ts": 1749486033267478, "dur": 391, "ph": "X", "name": "ChromeTraceHeader", "args": {}}, {"pid": 18384, "tid": 606, "ts": 1749486033270492, "dur": 564, "ph": "X", "name": "Thread Pool Worker", "args": {}}, {"pid": 18384, "tid": 1, "ph": "M", "name": "thread_name", "args": {"name": ""}}, {"pid": 18384, "tid": 1, "ts": 1749486031372540, "dur": 3826, "ph": "X", "name": "<Add>b__0", "args": {}}, {"pid": 18384, "tid": 1, "ts": 1749486031376370, "dur": 24832, "ph": "X", "name": "<Add>b__0", "args": {}}, {"pid": 18384, "tid": 1, "ts": 1749486031401209, "dur": 32791, "ph": "X", "name": "WriteJson", "args": {}}, {"pid": 18384, "tid": 606, "ts": 1749486033271059, "dur": 8, "ph": "X", "name": "", "args": {}}, {"pid": 18384, "tid": 12884901888, "ph": "M", "name": "thread_name", "args": {"name": "ReadEntireBinlogFromIpcAsync"}}, {"pid": 18384, "tid": 12884901888, "ts": 1749486031370807, "dur": 9686, "ph": "X", "name": "WaitForConnectionAsync", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749486031380495, "dur": 1881568, "ph": "X", "name": "UpdateFromStreamAsync", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749486031381235, "dur": 1900, "ph": "X", "name": "ReadAsync 0", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749486031383140, "dur": 997, "ph": "X", "name": "ProcessMessages 20485", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749486031384140, "dur": 228, "ph": "X", "name": "ReadAsync 20485", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749486031384372, "dur": 11, "ph": "X", "name": "ProcessMessages 20488", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749486031384383, "dur": 41, "ph": "X", "name": "ReadAsync 20488", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749486031384426, "dur": 1, "ph": "X", "name": "ProcessMessages 1343", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749486031384427, "dur": 19, "ph": "X", "name": "ReadAsync 1343", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749486031384449, "dur": 45, "ph": "X", "name": "ReadAsync 60", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749486031384496, "dur": 24, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749486031384524, "dur": 1, "ph": "X", "name": "ProcessMessages 510", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749486031384525, "dur": 55, "ph": "X", "name": "ReadAsync 510", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749486031384582, "dur": 1, "ph": "X", "name": "ProcessMessages 970", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749486031384583, "dur": 18, "ph": "X", "name": "ReadAsync 970", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749486031384604, "dur": 19, "ph": "X", "name": "ReadAsync 300", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749486031384626, "dur": 18, "ph": "X", "name": "ReadAsync 517", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749486031384647, "dur": 17, "ph": "X", "name": "ReadAsync 286", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749486031384666, "dur": 19, "ph": "X", "name": "ReadAsync 324", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749486031384689, "dur": 19, "ph": "X", "name": "ReadAsync 596", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749486031384710, "dur": 20, "ph": "X", "name": "ReadAsync 485", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749486031384732, "dur": 18, "ph": "X", "name": "ReadAsync 483", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749486031384752, "dur": 49, "ph": "X", "name": "ReadAsync 75", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749486031384804, "dur": 1, "ph": "X", "name": "ProcessMessages 673", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749486031384806, "dur": 21, "ph": "X", "name": "ReadAsync 673", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749486031384830, "dur": 19, "ph": "X", "name": "ReadAsync 696", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749486031384851, "dur": 54, "ph": "X", "name": "ReadAsync 627", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749486031384907, "dur": 17, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749486031384926, "dur": 19, "ph": "X", "name": "ReadAsync 307", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749486031384948, "dur": 28, "ph": "X", "name": "ReadAsync 537", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749486031384979, "dur": 19, "ph": "X", "name": "ReadAsync 443", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749486031385000, "dur": 23, "ph": "X", "name": "ReadAsync 449", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749486031385025, "dur": 24, "ph": "X", "name": "ReadAsync 377", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749486031385052, "dur": 21, "ph": "X", "name": "ReadAsync 289", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749486031385077, "dur": 20, "ph": "X", "name": "ReadAsync 536", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749486031385100, "dur": 22, "ph": "X", "name": "ReadAsync 424", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749486031385125, "dur": 20, "ph": "X", "name": "ReadAsync 541", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749486031385147, "dur": 22, "ph": "X", "name": "ReadAsync 594", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749486031385172, "dur": 19, "ph": "X", "name": "ReadAsync 669", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749486031385194, "dur": 19, "ph": "X", "name": "ReadAsync 660", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749486031385215, "dur": 19, "ph": "X", "name": "ReadAsync 660", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749486031385237, "dur": 19, "ph": "X", "name": "ReadAsync 618", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749486031385258, "dur": 21, "ph": "X", "name": "ReadAsync 521", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749486031385282, "dur": 16, "ph": "X", "name": "ReadAsync 614", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749486031385300, "dur": 90, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749486031385393, "dur": 27, "ph": "X", "name": "ReadAsync 726", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749486031385422, "dur": 1, "ph": "X", "name": "ProcessMessages 1923", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749486031385424, "dur": 19, "ph": "X", "name": "ReadAsync 1923", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749486031385444, "dur": 2, "ph": "X", "name": "ProcessMessages 696", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749486031385447, "dur": 41, "ph": "X", "name": "ReadAsync 696", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749486031385491, "dur": 18, "ph": "X", "name": "ReadAsync 943", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749486031385511, "dur": 18, "ph": "X", "name": "ReadAsync 501", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749486031385532, "dur": 20, "ph": "X", "name": "ReadAsync 559", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749486031385554, "dur": 17, "ph": "X", "name": "ReadAsync 496", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749486031385575, "dur": 17, "ph": "X", "name": "ReadAsync 341", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749486031385594, "dur": 20, "ph": "X", "name": "ReadAsync 351", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749486031385617, "dur": 40, "ph": "X", "name": "ReadAsync 659", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749486031385659, "dur": 27, "ph": "X", "name": "ReadAsync 539", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749486031385689, "dur": 20, "ph": "X", "name": "ReadAsync 740", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749486031385712, "dur": 22, "ph": "X", "name": "ReadAsync 767", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749486031385736, "dur": 23, "ph": "X", "name": "ReadAsync 606", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749486031385762, "dur": 22, "ph": "X", "name": "ReadAsync 594", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749486031385786, "dur": 19, "ph": "X", "name": "ReadAsync 611", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749486031385808, "dur": 20, "ph": "X", "name": "ReadAsync 491", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749486031385830, "dur": 19, "ph": "X", "name": "ReadAsync 518", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749486031385852, "dur": 26, "ph": "X", "name": "ReadAsync 343", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749486031385881, "dur": 20, "ph": "X", "name": "ReadAsync 808", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749486031385903, "dur": 19, "ph": "X", "name": "ReadAsync 832", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749486031385925, "dur": 20, "ph": "X", "name": "ReadAsync 565", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749486031385947, "dur": 18, "ph": "X", "name": "ReadAsync 40", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749486031385968, "dur": 19, "ph": "X", "name": "ReadAsync 352", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749486031385990, "dur": 20, "ph": "X", "name": "ReadAsync 621", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749486031386012, "dur": 21, "ph": "X", "name": "ReadAsync 798", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749486031386036, "dur": 17, "ph": "X", "name": "ReadAsync 784", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749486031386056, "dur": 21, "ph": "X", "name": "ReadAsync 358", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749486031386079, "dur": 37, "ph": "X", "name": "ReadAsync 731", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749486031386119, "dur": 26, "ph": "X", "name": "ReadAsync 638", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749486031386148, "dur": 80, "ph": "X", "name": "ReadAsync 946", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749486031386230, "dur": 43, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749486031386276, "dur": 2, "ph": "X", "name": "ProcessMessages 2039", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749486031386279, "dur": 26, "ph": "X", "name": "ReadAsync 2039", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749486031386306, "dur": 1, "ph": "X", "name": "ProcessMessages 1020", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749486031386307, "dur": 28, "ph": "X", "name": "ReadAsync 1020", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749486031386337, "dur": 1, "ph": "X", "name": "ProcessMessages 842", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749486031386338, "dur": 18, "ph": "X", "name": "ReadAsync 842", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749486031386359, "dur": 18, "ph": "X", "name": "ReadAsync 321", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749486031386379, "dur": 21, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749486031386403, "dur": 19, "ph": "X", "name": "ReadAsync 719", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749486031386424, "dur": 22, "ph": "X", "name": "ReadAsync 341", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749486031386448, "dur": 20, "ph": "X", "name": "ReadAsync 589", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749486031386471, "dur": 138, "ph": "X", "name": "ReadAsync 654", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749486031386611, "dur": 20, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749486031386634, "dur": 20, "ph": "X", "name": "ReadAsync 670", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749486031386657, "dur": 19, "ph": "X", "name": "ReadAsync 608", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749486031386679, "dur": 17, "ph": "X", "name": "ReadAsync 573", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749486031386699, "dur": 26, "ph": "X", "name": "ReadAsync 298", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749486031386727, "dur": 20, "ph": "X", "name": "ReadAsync 674", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749486031386750, "dur": 21, "ph": "X", "name": "ReadAsync 604", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749486031386773, "dur": 18, "ph": "X", "name": "ReadAsync 585", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749486031386794, "dur": 20, "ph": "X", "name": "ReadAsync 298", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749486031386816, "dur": 19, "ph": "X", "name": "ReadAsync 654", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749486031386838, "dur": 30, "ph": "X", "name": "ReadAsync 316", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749486031386870, "dur": 20, "ph": "X", "name": "ReadAsync 515", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749486031386892, "dur": 20, "ph": "X", "name": "ReadAsync 508", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749486031386916, "dur": 21, "ph": "X", "name": "ReadAsync 340", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749486031386939, "dur": 20, "ph": "X", "name": "ReadAsync 724", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749486031386963, "dur": 22, "ph": "X", "name": "ReadAsync 601", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749486031386988, "dur": 18, "ph": "X", "name": "ReadAsync 730", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749486031387008, "dur": 21, "ph": "X", "name": "ReadAsync 345", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749486031387031, "dur": 20, "ph": "X", "name": "ReadAsync 645", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749486031387054, "dur": 18, "ph": "X", "name": "ReadAsync 527", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749486031387075, "dur": 19, "ph": "X", "name": "ReadAsync 435", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749486031387096, "dur": 20, "ph": "X", "name": "ReadAsync 165", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749486031387118, "dur": 24, "ph": "X", "name": "ReadAsync 773", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749486031387145, "dur": 19, "ph": "X", "name": "ReadAsync 902", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749486031387166, "dur": 19, "ph": "X", "name": "ReadAsync 340", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749486031387188, "dur": 18, "ph": "X", "name": "ReadAsync 432", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749486031387208, "dur": 20, "ph": "X", "name": "ReadAsync 354", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749486031387230, "dur": 19, "ph": "X", "name": "ReadAsync 666", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749486031387253, "dur": 19, "ph": "X", "name": "ReadAsync 545", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749486031387275, "dur": 19, "ph": "X", "name": "ReadAsync 618", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749486031387297, "dur": 19, "ph": "X", "name": "ReadAsync 469", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749486031387319, "dur": 23, "ph": "X", "name": "ReadAsync 605", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749486031387345, "dur": 19, "ph": "X", "name": "ReadAsync 743", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749486031387367, "dur": 21, "ph": "X", "name": "ReadAsync 388", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749486031387390, "dur": 20, "ph": "X", "name": "ReadAsync 626", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749486031387412, "dur": 21, "ph": "X", "name": "ReadAsync 592", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749486031387436, "dur": 19, "ph": "X", "name": "ReadAsync 493", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749486031387458, "dur": 15, "ph": "X", "name": "ReadAsync 274", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749486031387475, "dur": 17, "ph": "X", "name": "ReadAsync 345", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749486031387494, "dur": 27, "ph": "X", "name": "ReadAsync 455", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749486031387524, "dur": 22, "ph": "X", "name": "ReadAsync 709", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749486031387548, "dur": 21, "ph": "X", "name": "ReadAsync 608", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749486031387572, "dur": 38, "ph": "X", "name": "ReadAsync 648", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749486031387612, "dur": 24, "ph": "X", "name": "ReadAsync 604", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749486031387639, "dur": 20, "ph": "X", "name": "ReadAsync 489", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749486031387662, "dur": 19, "ph": "X", "name": "ReadAsync 376", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749486031387684, "dur": 21, "ph": "X", "name": "ReadAsync 591", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749486031387708, "dur": 20, "ph": "X", "name": "ReadAsync 757", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749486031387730, "dur": 1, "ph": "X", "name": "ProcessMessages 648", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749486031387732, "dur": 19, "ph": "X", "name": "ReadAsync 648", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749486031387755, "dur": 22, "ph": "X", "name": "ReadAsync 319", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749486031387780, "dur": 20, "ph": "X", "name": "ReadAsync 562", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749486031387803, "dur": 21, "ph": "X", "name": "ReadAsync 665", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749486031387827, "dur": 19, "ph": "X", "name": "ReadAsync 459", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749486031387849, "dur": 23, "ph": "X", "name": "ReadAsync 497", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749486031387874, "dur": 22, "ph": "X", "name": "ReadAsync 766", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749486031387899, "dur": 20, "ph": "X", "name": "ReadAsync 795", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749486031387921, "dur": 12, "ph": "X", "name": "ReadAsync 768", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749486031387936, "dur": 17, "ph": "X", "name": "ReadAsync 136", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749486031387955, "dur": 20, "ph": "X", "name": "ReadAsync 417", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749486031387978, "dur": 21, "ph": "X", "name": "ReadAsync 543", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749486031388001, "dur": 166, "ph": "X", "name": "ReadAsync 533", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749486031388176, "dur": 2, "ph": "X", "name": "ProcessMessages 235", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749486031388180, "dur": 90, "ph": "X", "name": "ReadAsync 235", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749486031388274, "dur": 3, "ph": "X", "name": "ProcessMessages 2378", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749486031388278, "dur": 49, "ph": "X", "name": "ReadAsync 2378", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749486031388329, "dur": 1, "ph": "X", "name": "ProcessMessages 1585", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749486031388332, "dur": 21, "ph": "X", "name": "ReadAsync 1585", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749486031388357, "dur": 28, "ph": "X", "name": "ReadAsync 466", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749486031388388, "dur": 1, "ph": "X", "name": "ProcessMessages 998", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749486031388390, "dur": 27, "ph": "X", "name": "ReadAsync 998", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749486031388419, "dur": 1, "ph": "X", "name": "ProcessMessages 1017", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749486031388421, "dur": 17, "ph": "X", "name": "ReadAsync 1017", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749486031388441, "dur": 29, "ph": "X", "name": "ReadAsync 322", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749486031388473, "dur": 33, "ph": "X", "name": "ReadAsync 668", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749486031388508, "dur": 18, "ph": "X", "name": "ReadAsync 584", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749486031388529, "dur": 20, "ph": "X", "name": "ReadAsync 388", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749486031388551, "dur": 21, "ph": "X", "name": "ReadAsync 477", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749486031388575, "dur": 21, "ph": "X", "name": "ReadAsync 626", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749486031388598, "dur": 19, "ph": "X", "name": "ReadAsync 728", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749486031388620, "dur": 17, "ph": "X", "name": "ReadAsync 556", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749486031388640, "dur": 22, "ph": "X", "name": "ReadAsync 213", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749486031388665, "dur": 20, "ph": "X", "name": "ReadAsync 558", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749486031388688, "dur": 20, "ph": "X", "name": "ReadAsync 725", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749486031388712, "dur": 18, "ph": "X", "name": "ReadAsync 480", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749486031388732, "dur": 20, "ph": "X", "name": "ReadAsync 304", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749486031388756, "dur": 23, "ph": "X", "name": "ReadAsync 689", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749486031388780, "dur": 1, "ph": "X", "name": "ProcessMessages 468", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749486031388782, "dur": 22, "ph": "X", "name": "ReadAsync 468", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749486031388806, "dur": 1, "ph": "X", "name": "ProcessMessages 509", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749486031388808, "dur": 19, "ph": "X", "name": "ReadAsync 509", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749486031388850, "dur": 28, "ph": "X", "name": "ReadAsync 142", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749486031388880, "dur": 1, "ph": "X", "name": "ProcessMessages 1561", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749486031388881, "dur": 25, "ph": "X", "name": "ReadAsync 1561", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749486031388910, "dur": 18, "ph": "X", "name": "ReadAsync 293", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749486031388932, "dur": 24, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749486031388959, "dur": 20, "ph": "X", "name": "ReadAsync 354", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749486031388981, "dur": 1, "ph": "X", "name": "ProcessMessages 666", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749486031388983, "dur": 21, "ph": "X", "name": "ReadAsync 666", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749486031389006, "dur": 1, "ph": "X", "name": "ProcessMessages 585", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749486031389007, "dur": 21, "ph": "X", "name": "ReadAsync 585", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749486031389036, "dur": 19, "ph": "X", "name": "ReadAsync 578", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749486031389059, "dur": 19, "ph": "X", "name": "ReadAsync 710", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749486031389082, "dur": 19, "ph": "X", "name": "ReadAsync 620", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749486031389102, "dur": 1, "ph": "X", "name": "ProcessMessages 609", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749486031389104, "dur": 17, "ph": "X", "name": "ReadAsync 609", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749486031389124, "dur": 17, "ph": "X", "name": "ReadAsync 310", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749486031389145, "dur": 19, "ph": "X", "name": "ReadAsync 330", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749486031389168, "dur": 22, "ph": "X", "name": "ReadAsync 650", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749486031389193, "dur": 20, "ph": "X", "name": "ReadAsync 437", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749486031389216, "dur": 19, "ph": "X", "name": "ReadAsync 582", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749486031389237, "dur": 21, "ph": "X", "name": "ReadAsync 296", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749486031389261, "dur": 20, "ph": "X", "name": "ReadAsync 842", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749486031389285, "dur": 20, "ph": "X", "name": "ReadAsync 643", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749486031389307, "dur": 18, "ph": "X", "name": "ReadAsync 696", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749486031389329, "dur": 22, "ph": "X", "name": "ReadAsync 429", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749486031389354, "dur": 22, "ph": "X", "name": "ReadAsync 977", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749486031389378, "dur": 18, "ph": "X", "name": "ReadAsync 838", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749486031389400, "dur": 47, "ph": "X", "name": "ReadAsync 489", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749486031389450, "dur": 1, "ph": "X", "name": "ProcessMessages 773", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749486031389452, "dur": 20, "ph": "X", "name": "ReadAsync 773", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749486031389475, "dur": 18, "ph": "X", "name": "ReadAsync 674", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749486031389495, "dur": 18, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749486031389516, "dur": 17, "ph": "X", "name": "ReadAsync 651", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749486031389536, "dur": 19, "ph": "X", "name": "ReadAsync 337", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749486031389557, "dur": 50, "ph": "X", "name": "ReadAsync 626", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749486031389609, "dur": 1, "ph": "X", "name": "ProcessMessages 1359", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749486031389610, "dur": 29, "ph": "X", "name": "ReadAsync 1359", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749486031389642, "dur": 80, "ph": "X", "name": "ReadAsync 680", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749486031389731, "dur": 4, "ph": "X", "name": "ProcessMessages 837", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749486031389737, "dur": 43, "ph": "X", "name": "ReadAsync 837", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749486031389782, "dur": 2, "ph": "X", "name": "ProcessMessages 2651", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749486031389785, "dur": 24, "ph": "X", "name": "ReadAsync 2651", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749486031389810, "dur": 1, "ph": "X", "name": "ProcessMessages 1200", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749486031389811, "dur": 21, "ph": "X", "name": "ReadAsync 1200", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749486031389834, "dur": 1, "ph": "X", "name": "ProcessMessages 792", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749486031389836, "dur": 18, "ph": "X", "name": "ReadAsync 792", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749486031389857, "dur": 18, "ph": "X", "name": "ReadAsync 587", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749486031389878, "dur": 24, "ph": "X", "name": "ReadAsync 601", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749486031389905, "dur": 24, "ph": "X", "name": "ReadAsync 711", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749486031389931, "dur": 1, "ph": "X", "name": "ProcessMessages 722", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749486031389932, "dur": 17, "ph": "X", "name": "ReadAsync 722", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749486031389952, "dur": 19, "ph": "X", "name": "ReadAsync 325", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749486031389973, "dur": 1, "ph": "X", "name": "ProcessMessages 675", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749486031389974, "dur": 18, "ph": "X", "name": "ReadAsync 675", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749486031389997, "dur": 34, "ph": "X", "name": "ReadAsync 528", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749486031390032, "dur": 1, "ph": "X", "name": "ProcessMessages 936", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749486031390034, "dur": 35, "ph": "X", "name": "ReadAsync 936", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749486031390072, "dur": 22, "ph": "X", "name": "ReadAsync 472", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749486031390097, "dur": 41, "ph": "X", "name": "ReadAsync 631", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749486031390139, "dur": 1, "ph": "X", "name": "ProcessMessages 779", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749486031390141, "dur": 28, "ph": "X", "name": "ReadAsync 779", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749486031390172, "dur": 20, "ph": "X", "name": "ReadAsync 840", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749486031390195, "dur": 24, "ph": "X", "name": "ReadAsync 736", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749486031390221, "dur": 1, "ph": "X", "name": "ProcessMessages 638", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749486031390222, "dur": 14, "ph": "X", "name": "ReadAsync 638", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749486031390238, "dur": 18, "ph": "X", "name": "ReadAsync 388", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749486031390259, "dur": 17, "ph": "X", "name": "ReadAsync 138", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749486031390279, "dur": 29, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749486031390310, "dur": 21, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749486031390334, "dur": 18, "ph": "X", "name": "ReadAsync 530", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749486031390354, "dur": 1, "ph": "X", "name": "ProcessMessages 447", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749486031390356, "dur": 54, "ph": "X", "name": "ReadAsync 447", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749486031390413, "dur": 25, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749486031390441, "dur": 19, "ph": "X", "name": "ReadAsync 451", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749486031390463, "dur": 47, "ph": "X", "name": "ReadAsync 604", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749486031390512, "dur": 31, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749486031390546, "dur": 100, "ph": "X", "name": "ReadAsync 403", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749486031390649, "dur": 1, "ph": "X", "name": "ProcessMessages 651", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749486031390651, "dur": 29, "ph": "X", "name": "ReadAsync 651", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749486031390682, "dur": 1, "ph": "X", "name": "ProcessMessages 1054", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749486031390684, "dur": 93, "ph": "X", "name": "ReadAsync 1054", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749486031390781, "dur": 45, "ph": "X", "name": "ReadAsync 176", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749486031390828, "dur": 1, "ph": "X", "name": "ProcessMessages 1696", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749486031390831, "dur": 127, "ph": "X", "name": "ReadAsync 1696", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749486031390961, "dur": 1, "ph": "X", "name": "ProcessMessages 261", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749486031390963, "dur": 104, "ph": "X", "name": "ReadAsync 261", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749486031391071, "dur": 2, "ph": "X", "name": "ProcessMessages 994", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749486031391074, "dur": 50, "ph": "X", "name": "ReadAsync 994", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749486031391127, "dur": 1, "ph": "X", "name": "ProcessMessages 1050", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749486031391129, "dur": 29, "ph": "X", "name": "ReadAsync 1050", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749486031391160, "dur": 1, "ph": "X", "name": "ProcessMessages 1078", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749486031391162, "dur": 43, "ph": "X", "name": "ReadAsync 1078", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749486031391207, "dur": 19, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749486031391229, "dur": 18, "ph": "X", "name": "ReadAsync 301", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749486031391249, "dur": 19, "ph": "X", "name": "ReadAsync 681", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749486031391271, "dur": 1, "ph": "X", "name": "ProcessMessages 66", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749486031391273, "dur": 36, "ph": "X", "name": "ReadAsync 66", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749486031391311, "dur": 32, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749486031391347, "dur": 31, "ph": "X", "name": "ReadAsync 448", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749486031391381, "dur": 1, "ph": "X", "name": "ProcessMessages 588", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749486031391382, "dur": 125, "ph": "X", "name": "ReadAsync 588", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749486031391511, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749486031391512, "dur": 39, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749486031391554, "dur": 1, "ph": "X", "name": "ProcessMessages 2264", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749486031391556, "dur": 23, "ph": "X", "name": "ReadAsync 2264", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749486031391582, "dur": 31, "ph": "X", "name": "ReadAsync 57", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749486031391616, "dur": 23, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749486031391641, "dur": 2, "ph": "X", "name": "ProcessMessages 754", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749486031391644, "dur": 21, "ph": "X", "name": "ReadAsync 754", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749486031391668, "dur": 38, "ph": "X", "name": "ReadAsync 529", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749486031391710, "dur": 21, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749486031391732, "dur": 1, "ph": "X", "name": "ProcessMessages 601", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749486031391734, "dur": 24, "ph": "X", "name": "ReadAsync 601", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749486031391761, "dur": 16, "ph": "X", "name": "ReadAsync 580", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749486031391780, "dur": 32, "ph": "X", "name": "ReadAsync 56", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749486031391814, "dur": 22, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749486031391840, "dur": 19, "ph": "X", "name": "ReadAsync 628", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749486031391862, "dur": 127, "ph": "X", "name": "ReadAsync 466", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749486031391992, "dur": 36, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749486031392036, "dur": 1, "ph": "X", "name": "ProcessMessages 2114", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749486031392039, "dur": 24, "ph": "X", "name": "ReadAsync 2114", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749486031392065, "dur": 19, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749486031392087, "dur": 21, "ph": "X", "name": "ReadAsync 421", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749486031392110, "dur": 1, "ph": "X", "name": "ProcessMessages 561", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749486031392112, "dur": 39, "ph": "X", "name": "ReadAsync 561", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749486031392154, "dur": 20, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749486031392175, "dur": 1, "ph": "X", "name": "ProcessMessages 573", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749486031392177, "dur": 19, "ph": "X", "name": "ReadAsync 573", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749486031392197, "dur": 1, "ph": "X", "name": "ProcessMessages 476", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749486031392198, "dur": 41, "ph": "X", "name": "ReadAsync 476", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749486031392242, "dur": 21, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749486031392266, "dur": 20, "ph": "X", "name": "ReadAsync 324", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749486031392290, "dur": 17, "ph": "X", "name": "ReadAsync 598", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749486031392310, "dur": 31, "ph": "X", "name": "ReadAsync 57", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749486031392344, "dur": 21, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749486031392368, "dur": 19, "ph": "X", "name": "ReadAsync 559", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749486031392388, "dur": 1, "ph": "X", "name": "ProcessMessages 459", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749486031392389, "dur": 41, "ph": "X", "name": "ReadAsync 459", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749486031392433, "dur": 20, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749486031392456, "dur": 18, "ph": "X", "name": "ReadAsync 481", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749486031392476, "dur": 18, "ph": "X", "name": "ReadAsync 339", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749486031392497, "dur": 41, "ph": "X", "name": "ReadAsync 237", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749486031392541, "dur": 21, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749486031392566, "dur": 19, "ph": "X", "name": "ReadAsync 576", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749486031392588, "dur": 41, "ph": "X", "name": "ReadAsync 454", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749486031392631, "dur": 22, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749486031392657, "dur": 18, "ph": "X", "name": "ReadAsync 724", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749486031392678, "dur": 38, "ph": "X", "name": "ReadAsync 364", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749486031392719, "dur": 99, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749486031392819, "dur": 1, "ph": "X", "name": "ProcessMessages 1503", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749486031392822, "dur": 26, "ph": "X", "name": "ReadAsync 1503", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749486031392850, "dur": 20, "ph": "X", "name": "ReadAsync 224", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749486031392873, "dur": 21, "ph": "X", "name": "ReadAsync 533", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749486031392898, "dur": 19, "ph": "X", "name": "ReadAsync 708", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749486031392919, "dur": 19, "ph": "X", "name": "ReadAsync 745", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749486031392941, "dur": 18, "ph": "X", "name": "ReadAsync 382", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749486031392961, "dur": 40, "ph": "X", "name": "ReadAsync 237", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749486031393003, "dur": 23, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749486031393028, "dur": 1, "ph": "X", "name": "ProcessMessages 669", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749486031393029, "dur": 19, "ph": "X", "name": "ReadAsync 669", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749486031393052, "dur": 41, "ph": "X", "name": "ReadAsync 352", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749486031393095, "dur": 21, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749486031393119, "dur": 24, "ph": "X", "name": "ReadAsync 615", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749486031393146, "dur": 18, "ph": "X", "name": "ReadAsync 345", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749486031393167, "dur": 32, "ph": "X", "name": "ReadAsync 140", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749486031393203, "dur": 24, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749486031393228, "dur": 1, "ph": "X", "name": "ProcessMessages 791", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749486031393231, "dur": 18, "ph": "X", "name": "ReadAsync 791", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749486031393251, "dur": 38, "ph": "X", "name": "ReadAsync 303", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749486031393292, "dur": 20, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749486031393315, "dur": 19, "ph": "X", "name": "ReadAsync 576", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749486031393337, "dur": 40, "ph": "X", "name": "ReadAsync 611", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749486031393380, "dur": 29, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749486031393411, "dur": 1, "ph": "X", "name": "ProcessMessages 940", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749486031393412, "dur": 18, "ph": "X", "name": "ReadAsync 940", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749486031393432, "dur": 36, "ph": "X", "name": "ReadAsync 237", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749486031393476, "dur": 20, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749486031393499, "dur": 19, "ph": "X", "name": "ReadAsync 710", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749486031393521, "dur": 45, "ph": "X", "name": "ReadAsync 516", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749486031393569, "dur": 25, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749486031393597, "dur": 31, "ph": "X", "name": "ReadAsync 829", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749486031393632, "dur": 24, "ph": "X", "name": "ReadAsync 304", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749486031393658, "dur": 60, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749486031393720, "dur": 20, "ph": "X", "name": "ReadAsync 443", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749486031393742, "dur": 1, "ph": "X", "name": "ProcessMessages 602", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749486031393744, "dur": 24, "ph": "X", "name": "ReadAsync 602", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749486031393770, "dur": 1, "ph": "X", "name": "ProcessMessages 502", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749486031393772, "dur": 20, "ph": "X", "name": "ReadAsync 502", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749486031393795, "dur": 38, "ph": "X", "name": "ReadAsync 618", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749486031393836, "dur": 20, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749486031393859, "dur": 19, "ph": "X", "name": "ReadAsync 505", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749486031393880, "dur": 1, "ph": "X", "name": "ProcessMessages 597", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749486031393882, "dur": 42, "ph": "X", "name": "ReadAsync 597", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749486031393927, "dur": 42, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749486031393970, "dur": 1, "ph": "X", "name": "ProcessMessages 825", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749486031393972, "dur": 20, "ph": "X", "name": "ReadAsync 825", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749486031393995, "dur": 31, "ph": "X", "name": "ReadAsync 260", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749486031394029, "dur": 23, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749486031394055, "dur": 18, "ph": "X", "name": "ReadAsync 699", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749486031394076, "dur": 39, "ph": "X", "name": "ReadAsync 358", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749486031394118, "dur": 22, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749486031394141, "dur": 1, "ph": "X", "name": "ProcessMessages 610", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749486031394142, "dur": 21, "ph": "X", "name": "ReadAsync 610", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749486031394166, "dur": 18, "ph": "X", "name": "ReadAsync 405", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749486031394187, "dur": 32, "ph": "X", "name": "ReadAsync 66", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749486031394222, "dur": 20, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749486031394244, "dur": 1, "ph": "X", "name": "ProcessMessages 529", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749486031394246, "dur": 20, "ph": "X", "name": "ReadAsync 529", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749486031394269, "dur": 21, "ph": "X", "name": "ReadAsync 823", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749486031394292, "dur": 20, "ph": "X", "name": "ReadAsync 758", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749486031394315, "dur": 29, "ph": "X", "name": "ReadAsync 681", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749486031394352, "dur": 35, "ph": "X", "name": "ReadAsync 435", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749486031394389, "dur": 21, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749486031394411, "dur": 1, "ph": "X", "name": "ProcessMessages 671", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749486031394413, "dur": 42, "ph": "X", "name": "ReadAsync 671", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749486031394456, "dur": 1, "ph": "X", "name": "ProcessMessages 1235", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749486031394458, "dur": 24, "ph": "X", "name": "ReadAsync 1235", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749486031394485, "dur": 19, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749486031394507, "dur": 18, "ph": "X", "name": "ReadAsync 609", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749486031394528, "dur": 18, "ph": "X", "name": "ReadAsync 316", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749486031394548, "dur": 36, "ph": "X", "name": "ReadAsync 237", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749486031394586, "dur": 24, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749486031394614, "dur": 20, "ph": "X", "name": "ReadAsync 464", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749486031394637, "dur": 17, "ph": "X", "name": "ReadAsync 696", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749486031394657, "dur": 35, "ph": "X", "name": "ReadAsync 21", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749486031394694, "dur": 19, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749486031394716, "dur": 20, "ph": "X", "name": "ReadAsync 517", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749486031394739, "dur": 39, "ph": "X", "name": "ReadAsync 591", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749486031394781, "dur": 21, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749486031394803, "dur": 1, "ph": "X", "name": "ProcessMessages 499", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749486031394805, "dur": 18, "ph": "X", "name": "ReadAsync 499", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749486031394826, "dur": 17, "ph": "X", "name": "ReadAsync 482", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749486031394846, "dur": 33, "ph": "X", "name": "ReadAsync 140", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749486031394882, "dur": 21, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749486031394905, "dur": 20, "ph": "X", "name": "ReadAsync 746", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749486031394928, "dur": 37, "ph": "X", "name": "ReadAsync 362", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749486031394967, "dur": 22, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749486031394992, "dur": 19, "ph": "X", "name": "ReadAsync 617", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749486031395014, "dur": 38, "ph": "X", "name": "ReadAsync 465", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749486031395055, "dur": 27, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749486031395085, "dur": 21, "ph": "X", "name": "ReadAsync 821", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749486031395108, "dur": 42, "ph": "X", "name": "ReadAsync 245", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749486031395152, "dur": 23, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749486031395178, "dur": 19, "ph": "X", "name": "ReadAsync 719", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749486031395200, "dur": 17, "ph": "X", "name": "ReadAsync 126", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749486031395220, "dur": 41, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749486031395263, "dur": 24, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749486031395289, "dur": 1, "ph": "X", "name": "ProcessMessages 842", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749486031395290, "dur": 18, "ph": "X", "name": "ReadAsync 842", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749486031395310, "dur": 38, "ph": "X", "name": "ReadAsync 318", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749486031395351, "dur": 20, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749486031395374, "dur": 19, "ph": "X", "name": "ReadAsync 720", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749486031395395, "dur": 37, "ph": "X", "name": "ReadAsync 511", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749486031395435, "dur": 20, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749486031395458, "dur": 22, "ph": "X", "name": "ReadAsync 609", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749486031395483, "dur": 17, "ph": "X", "name": "ReadAsync 439", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749486031395502, "dur": 35, "ph": "X", "name": "ReadAsync 138", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749486031395539, "dur": 23, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749486031395565, "dur": 17, "ph": "X", "name": "ReadAsync 703", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749486031395587, "dur": 23, "ph": "X", "name": "ReadAsync 303", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749486031395614, "dur": 17, "ph": "X", "name": "ReadAsync 645", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749486031395633, "dur": 20, "ph": "X", "name": "ReadAsync 244", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749486031395656, "dur": 18, "ph": "X", "name": "ReadAsync 558", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749486031395676, "dur": 40, "ph": "X", "name": "ReadAsync 237", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749486031395719, "dur": 20, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749486031395742, "dur": 19, "ph": "X", "name": "ReadAsync 550", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749486031395763, "dur": 45, "ph": "X", "name": "ReadAsync 460", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749486031395811, "dur": 28, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749486031395842, "dur": 18, "ph": "X", "name": "ReadAsync 705", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749486031395863, "dur": 39, "ph": "X", "name": "ReadAsync 364", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749486031395904, "dur": 19, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749486031395926, "dur": 21, "ph": "X", "name": "ReadAsync 493", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749486031395950, "dur": 40, "ph": "X", "name": "ReadAsync 600", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749486031395993, "dur": 21, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749486031396016, "dur": 19, "ph": "X", "name": "ReadAsync 652", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749486031396038, "dur": 39, "ph": "X", "name": "ReadAsync 486", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749486031396080, "dur": 23, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749486031396105, "dur": 20, "ph": "X", "name": "ReadAsync 691", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749486031396128, "dur": 20, "ph": "X", "name": "ReadAsync 293", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749486031396151, "dur": 20, "ph": "X", "name": "ReadAsync 581", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749486031396173, "dur": 18, "ph": "X", "name": "ReadAsync 506", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749486031396194, "dur": 17, "ph": "X", "name": "ReadAsync 336", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749486031396213, "dur": 46, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749486031396263, "dur": 20, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749486031396286, "dur": 24, "ph": "X", "name": "ReadAsync 565", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749486031396312, "dur": 22, "ph": "X", "name": "ReadAsync 778", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749486031396335, "dur": 1, "ph": "X", "name": "ProcessMessages 590", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749486031396337, "dur": 39, "ph": "X", "name": "ReadAsync 590", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749486031396378, "dur": 18, "ph": "X", "name": "ReadAsync 588", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749486031396399, "dur": 41, "ph": "X", "name": "ReadAsync 237", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749486031396442, "dur": 18, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749486031396462, "dur": 1, "ph": "X", "name": "ProcessMessages 224", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749486031396464, "dur": 159, "ph": "X", "name": "ReadAsync 224", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749486031396627, "dur": 49, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749486031396679, "dur": 269, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749486031396951, "dur": 280, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749486031397238, "dur": 61, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749486031397302, "dur": 2, "ph": "X", "name": "ProcessMessages 148", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749486031397306, "dur": 115, "ph": "X", "name": "ReadAsync 148", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749486031397425, "dur": 2, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749486031397429, "dur": 53, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749486031397485, "dur": 2, "ph": "X", "name": "ProcessMessages 176", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749486031397489, "dur": 56, "ph": "X", "name": "ReadAsync 176", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749486031397550, "dur": 2, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749486031397553, "dur": 60, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749486031397617, "dur": 1, "ph": "X", "name": "ProcessMessages 112", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749486031397620, "dur": 62, "ph": "X", "name": "ReadAsync 112", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749486031397685, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749486031397687, "dur": 47, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749486031397737, "dur": 1, "ph": "X", "name": "ProcessMessages 128", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749486031397740, "dur": 89, "ph": "X", "name": "ReadAsync 128", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749486031397832, "dur": 1, "ph": "X", "name": "ProcessMessages 112", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749486031397834, "dur": 36, "ph": "X", "name": "ReadAsync 112", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749486031397872, "dur": 1, "ph": "X", "name": "ProcessMessages 128", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749486031397874, "dur": 84, "ph": "X", "name": "ReadAsync 128", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749486031397962, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749486031397964, "dur": 51, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749486031398019, "dur": 1, "ph": "X", "name": "ProcessMessages 148", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749486031398022, "dur": 33, "ph": "X", "name": "ReadAsync 148", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749486031398060, "dur": 34, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749486031398096, "dur": 1, "ph": "X", "name": "ProcessMessages 96", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749486031398098, "dur": 69, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749486031398170, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749486031398172, "dur": 75, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749486031398251, "dur": 1, "ph": "X", "name": "ProcessMessages 160", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749486031398254, "dur": 41, "ph": "X", "name": "ReadAsync 160", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749486031398297, "dur": 1, "ph": "X", "name": "ProcessMessages 176", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749486031398300, "dur": 31, "ph": "X", "name": "ReadAsync 176", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749486031398334, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749486031398336, "dur": 47, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749486031398385, "dur": 1, "ph": "X", "name": "ProcessMessages 112", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749486031398387, "dur": 38, "ph": "X", "name": "ReadAsync 112", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749486031398427, "dur": 1, "ph": "X", "name": "ProcessMessages 128", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749486031398429, "dur": 54, "ph": "X", "name": "ReadAsync 128", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749486031398485, "dur": 1, "ph": "X", "name": "ProcessMessages 112", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749486031398487, "dur": 38, "ph": "X", "name": "ReadAsync 112", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749486031398528, "dur": 1, "ph": "X", "name": "ProcessMessages 176", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749486031398530, "dur": 34, "ph": "X", "name": "ReadAsync 176", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749486031398568, "dur": 30, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749486031398600, "dur": 1, "ph": "X", "name": "ProcessMessages 96", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749486031398601, "dur": 31, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749486031398634, "dur": 1, "ph": "X", "name": "ProcessMessages 128", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749486031398636, "dur": 28, "ph": "X", "name": "ReadAsync 128", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749486031398667, "dur": 1, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749486031398668, "dur": 41, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749486031398713, "dur": 1, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749486031398715, "dur": 41, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749486031398760, "dur": 1, "ph": "X", "name": "ProcessMessages 128", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749486031398762, "dur": 43, "ph": "X", "name": "ReadAsync 128", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749486031398807, "dur": 1, "ph": "X", "name": "ProcessMessages 108", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749486031398809, "dur": 35, "ph": "X", "name": "ReadAsync 108", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749486031398846, "dur": 1, "ph": "X", "name": "ProcessMessages 148", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749486031398848, "dur": 31, "ph": "X", "name": "ReadAsync 148", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749486031398881, "dur": 1, "ph": "X", "name": "ProcessMessages 144", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749486031398883, "dur": 33, "ph": "X", "name": "ReadAsync 144", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749486031398919, "dur": 1, "ph": "X", "name": "ProcessMessages 96", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749486031398921, "dur": 34, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749486031398958, "dur": 1, "ph": "X", "name": "ProcessMessages 160", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749486031398961, "dur": 31, "ph": "X", "name": "ReadAsync 160", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749486031398994, "dur": 1, "ph": "X", "name": "ProcessMessages 128", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749486031398996, "dur": 32, "ph": "X", "name": "ReadAsync 128", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749486031399031, "dur": 1, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749486031399033, "dur": 44, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749486031399079, "dur": 1, "ph": "X", "name": "ProcessMessages 128", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749486031399081, "dur": 31, "ph": "X", "name": "ReadAsync 128", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749486031399115, "dur": 1, "ph": "X", "name": "ProcessMessages 112", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749486031399116, "dur": 38, "ph": "X", "name": "ReadAsync 112", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749486031399157, "dur": 1, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749486031399161, "dur": 34, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749486031399197, "dur": 1, "ph": "X", "name": "ProcessMessages 160", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749486031399199, "dur": 39, "ph": "X", "name": "ReadAsync 160", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749486031399240, "dur": 1, "ph": "X", "name": "ProcessMessages 176", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749486031399243, "dur": 35, "ph": "X", "name": "ReadAsync 176", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749486031399280, "dur": 1, "ph": "X", "name": "ProcessMessages 160", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749486031399282, "dur": 34, "ph": "X", "name": "ReadAsync 160", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749486031399318, "dur": 1, "ph": "X", "name": "ProcessMessages 160", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749486031399320, "dur": 30, "ph": "X", "name": "ReadAsync 160", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749486031399352, "dur": 1, "ph": "X", "name": "ProcessMessages 112", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749486031399353, "dur": 32, "ph": "X", "name": "ReadAsync 112", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749486031399387, "dur": 1, "ph": "X", "name": "ProcessMessages 96", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749486031399389, "dur": 51, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749486031399443, "dur": 1, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749486031399445, "dur": 37, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749486031399485, "dur": 1, "ph": "X", "name": "ProcessMessages 128", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749486031399487, "dur": 35, "ph": "X", "name": "ReadAsync 128", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749486031399525, "dur": 1, "ph": "X", "name": "ProcessMessages 128", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749486031399527, "dur": 41, "ph": "X", "name": "ReadAsync 128", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749486031399571, "dur": 1, "ph": "X", "name": "ProcessMessages 144", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749486031399574, "dur": 33, "ph": "X", "name": "ReadAsync 144", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749486031399610, "dur": 1, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749486031399612, "dur": 36, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749486031399650, "dur": 1, "ph": "X", "name": "ProcessMessages 208", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749486031399653, "dur": 27, "ph": "X", "name": "ReadAsync 208", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749486031399684, "dur": 35, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749486031399722, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749486031399724, "dur": 39, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749486031399766, "dur": 1, "ph": "X", "name": "ProcessMessages 144", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749486031399769, "dur": 36, "ph": "X", "name": "ReadAsync 144", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749486031399807, "dur": 1, "ph": "X", "name": "ProcessMessages 112", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749486031399809, "dur": 32, "ph": "X", "name": "ReadAsync 112", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749486031399843, "dur": 1, "ph": "X", "name": "ProcessMessages 160", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749486031399846, "dur": 31, "ph": "X", "name": "ReadAsync 160", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749486031399879, "dur": 2, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749486031399882, "dur": 38, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749486031399924, "dur": 2, "ph": "X", "name": "ProcessMessages 112", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749486031399927, "dur": 35, "ph": "X", "name": "ReadAsync 112", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749486031399965, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749486031399967, "dur": 68, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749486031400038, "dur": 2, "ph": "X", "name": "ProcessMessages 208", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749486031400041, "dur": 101, "ph": "X", "name": "ReadAsync 208", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749486031400146, "dur": 2, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749486031400149, "dur": 19719, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749486031419876, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749486031419880, "dur": 50, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749486031419936, "dur": 2, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749486031419939, "dur": 1755, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749486031421698, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749486031421700, "dur": 42, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749486031421744, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749486031421746, "dur": 42, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749486031421793, "dur": 36, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749486031421833, "dur": 34, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749486031421871, "dur": 99, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749486031421974, "dur": 33, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749486031422012, "dur": 4739, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749486031426759, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749486031426763, "dur": 31, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749486031426795, "dur": 1, "ph": "X", "name": "ProcessMessages 52", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749486031426797, "dur": 33, "ph": "X", "name": "ReadAsync 52", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749486031426834, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749486031426836, "dur": 30, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749486031426871, "dur": 246, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749486031427120, "dur": 17, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749486031427139, "dur": 101, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749486031427244, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749486031427246, "dur": 154, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749486031427405, "dur": 33, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749486031427445, "dur": 26, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749486031427474, "dur": 33, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749486031427511, "dur": 33, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749486031427548, "dur": 26, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749486031427578, "dur": 113, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749486031427695, "dur": 40, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749486031427739, "dur": 114, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749486031427858, "dur": 28, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749486031427890, "dur": 126, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749486031428021, "dur": 33, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749486031428058, "dur": 29, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749486031428091, "dur": 36, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749486031428131, "dur": 33, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749486031428166, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749486031428168, "dur": 30, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749486031428201, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749486031428204, "dur": 36, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749486031428244, "dur": 31, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749486031428279, "dur": 28, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749486031428310, "dur": 76, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749486031428390, "dur": 25, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749486031428418, "dur": 27, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749486031428448, "dur": 28, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749486031428479, "dur": 27, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749486031428510, "dur": 25, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749486031428538, "dur": 61, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749486031428604, "dur": 44, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749486031428651, "dur": 1, "ph": "X", "name": "ProcessMessages 52", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749486031428654, "dur": 33, "ph": "X", "name": "ReadAsync 52", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749486031428690, "dur": 93, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749486031428788, "dur": 36, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749486031428828, "dur": 43, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749486031428875, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749486031428877, "dur": 34, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749486031428915, "dur": 31, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749486031428947, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749486031428949, "dur": 95, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749486031429048, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749486031429053, "dur": 35, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749486031429090, "dur": 1, "ph": "X", "name": "ProcessMessages 96", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749486031429092, "dur": 71, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749486031429166, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749486031429168, "dur": 30, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749486031429202, "dur": 35, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749486031429239, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749486031429241, "dur": 35, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749486031429280, "dur": 111, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749486031429396, "dur": 28, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749486031429428, "dur": 43, "ph": "X", "name": "ReadAsync 52", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749486031429475, "dur": 30, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749486031429508, "dur": 1, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749486031429510, "dur": 29, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749486031429549, "dur": 31, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749486031429584, "dur": 21, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749486031429608, "dur": 68, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749486031429680, "dur": 32, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749486031429715, "dur": 35, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749486031429754, "dur": 42, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749486031429800, "dur": 30, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749486031429834, "dur": 108, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749486031429945, "dur": 25, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749486031429974, "dur": 57, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749486031430034, "dur": 17, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749486031430054, "dur": 31, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749486031430088, "dur": 33, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749486031430123, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749486031430125, "dur": 37, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749486031430167, "dur": 31, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749486031430201, "dur": 28, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749486031430233, "dur": 27, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749486031430265, "dur": 32, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749486031430301, "dur": 38, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749486031430343, "dur": 33, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749486031430380, "dur": 31, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749486031430414, "dur": 38, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749486031430455, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749486031430458, "dur": 26, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749486031430487, "dur": 25, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749486031430514, "dur": 21, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749486031430537, "dur": 28, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749486031430570, "dur": 37, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749486031430611, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749486031430613, "dur": 32, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749486031430650, "dur": 42, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749486031430695, "dur": 28, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749486031430728, "dur": 33, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749486031430764, "dur": 24, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749486031430792, "dur": 26, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749486031430822, "dur": 57, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749486031430882, "dur": 24, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749486031430910, "dur": 32, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749486031430945, "dur": 71, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749486031431019, "dur": 29, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749486031431052, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749486031431054, "dur": 31, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749486031431089, "dur": 82, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749486031431174, "dur": 23, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749486031431201, "dur": 25, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749486031431229, "dur": 28, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749486031431261, "dur": 15, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749486031431278, "dur": 53, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749486031431336, "dur": 29, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749486031431368, "dur": 1, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749486031431370, "dur": 70, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749486031431445, "dur": 34, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749486031431483, "dur": 74, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749486031431562, "dur": 19, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749486031431584, "dur": 43, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749486031431631, "dur": 28, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749486031431662, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749486031431664, "dur": 25, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749486031431692, "dur": 47, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749486031431744, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749486031431746, "dur": 35, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749486031431786, "dur": 648, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749486031432437, "dur": 63, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749486031432504, "dur": 2, "ph": "X", "name": "ProcessMessages 256", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749486031432507, "dur": 86, "ph": "X", "name": "ReadAsync 256", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749486031432599, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749486031432603, "dur": 37, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749486031432645, "dur": 64, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749486031432711, "dur": 28, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749486031432741, "dur": 32, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749486031432777, "dur": 22, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749486031432803, "dur": 39, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749486031432844, "dur": 69, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749486031432917, "dur": 32, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749486031432954, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749486031432956, "dur": 30, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749486031432988, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749486031432990, "dur": 30, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749486031433024, "dur": 25, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749486031433051, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749486031433054, "dur": 36, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749486031433095, "dur": 100, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749486031433199, "dur": 19, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749486031433221, "dur": 23, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749486031433247, "dur": 38, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749486031433288, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749486031433290, "dur": 32, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749486031433326, "dur": 60, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749486031433389, "dur": 30, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749486031433422, "dur": 25, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749486031433450, "dur": 25, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749486031433479, "dur": 47, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749486031433530, "dur": 21, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749486031433555, "dur": 24, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749486031433582, "dur": 69, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749486031433654, "dur": 28, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749486031433686, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749486031433690, "dur": 34, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749486031433726, "dur": 20, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749486031433749, "dur": 18, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749486031433773, "dur": 33, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749486031433808, "dur": 15, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749486031433827, "dur": 225, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749486031434057, "dur": 35, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749486031434096, "dur": 81, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749486031434181, "dur": 70, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749486031434254, "dur": 89, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749486031434347, "dur": 59, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749486031434419, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749486031434421, "dur": 178, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749486031434603, "dur": 57, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749486031434666, "dur": 2, "ph": "X", "name": "ProcessMessages 8", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749486031434669, "dur": 51964, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749486031486641, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749486031486644, "dur": 80, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749486031486728, "dur": 1782, "ph": "X", "name": "ProcessMessages 199", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749486031488513, "dur": 6416, "ph": "X", "name": "ReadAsync 199", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749486031494938, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749486031494941, "dur": 58, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749486031495003, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749486031495005, "dur": 37, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749486031495045, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749486031495047, "dur": 36, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749486031495089, "dur": 323, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749486031495417, "dur": 37, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749486031495457, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749486031495459, "dur": 191, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749486031495655, "dur": 27, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749486031495684, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749486031495686, "dur": 264, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749486031495954, "dur": 31, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749486031495990, "dur": 260, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749486031496256, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749486031496258, "dur": 41, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749486031496302, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749486031496304, "dur": 142, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749486031496450, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749486031496452, "dur": 32, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749486031496488, "dur": 81, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749486031496572, "dur": 27, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749486031496603, "dur": 285, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749486031496893, "dur": 30, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749486031496925, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749486031496927, "dur": 24, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749486031496954, "dur": 360, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749486031497320, "dur": 33, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749486031497357, "dur": 342, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749486031497703, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749486031497705, "dur": 31, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749486031497738, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749486031497740, "dur": 91, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749486031497835, "dur": 27, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749486031497865, "dur": 291, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749486031498161, "dur": 27, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749486031498193, "dur": 302, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749486031498500, "dur": 32, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749486031498535, "dur": 51, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749486031498591, "dur": 28, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749486031498622, "dur": 25, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749486031498650, "dur": 409, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749486031499064, "dur": 19, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749486031499085, "dur": 660, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749486031499751, "dur": 28, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749486031499783, "dur": 57, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749486031499844, "dur": 28, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749486031499875, "dur": 90, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749486031499970, "dur": 20, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749486031499993, "dur": 38, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749486031500035, "dur": 95, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749486031500135, "dur": 29, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749486031500168, "dur": 43, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749486031500215, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749486031500217, "dur": 1271, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749486031501493, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749486031501495, "dur": 39, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749486031501537, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749486031501538, "dur": 56, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749486031501599, "dur": 37, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749486031501639, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749486031501641, "dur": 754, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749486031502400, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749486031502402, "dur": 39, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749486031502444, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749486031502446, "dur": 46, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749486031502496, "dur": 208, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749486031502709, "dur": 41, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749486031502753, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749486031502756, "dur": 64, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749486031502825, "dur": 36, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749486031502866, "dur": 36, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749486031502907, "dur": 37, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749486031502947, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749486031502949, "dur": 37, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749486031502989, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749486031502991, "dur": 42, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749486031503036, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749486031503038, "dur": 38, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749486031503079, "dur": 1, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749486031503083, "dur": 41, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749486031503127, "dur": 1, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749486031503130, "dur": 138, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749486031503273, "dur": 36, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749486031503311, "dur": 1, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749486031503313, "dur": 38, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749486031503354, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749486031503356, "dur": 38, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749486031503398, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749486031503400, "dur": 45, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749486031503448, "dur": 1, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749486031503450, "dur": 37, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749486031503491, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749486031503492, "dur": 40, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749486031503535, "dur": 1, "ph": "X", "name": "ProcessMessages 96", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749486031503538, "dur": 38, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749486031503579, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749486031503581, "dur": 43, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749486031503627, "dur": 1, "ph": "X", "name": "ProcessMessages 76", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749486031503629, "dur": 48, "ph": "X", "name": "ReadAsync 76", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749486031503680, "dur": 1, "ph": "X", "name": "ProcessMessages 84", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749486031503683, "dur": 43, "ph": "X", "name": "ReadAsync 84", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749486031503729, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749486031503731, "dur": 39, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749486031503773, "dur": 1, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749486031503775, "dur": 49, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749486031503827, "dur": 1, "ph": "X", "name": "ProcessMessages 96", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749486031503829, "dur": 42, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749486031503874, "dur": 1, "ph": "X", "name": "ProcessMessages 140", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749486031503877, "dur": 39, "ph": "X", "name": "ReadAsync 140", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749486031503919, "dur": 1, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749486031503921, "dur": 40, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749486031503964, "dur": 1, "ph": "X", "name": "ProcessMessages 112", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749486031503966, "dur": 43, "ph": "X", "name": "ReadAsync 112", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749486031504013, "dur": 1, "ph": "X", "name": "ProcessMessages 124", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749486031504016, "dur": 38, "ph": "X", "name": "ReadAsync 124", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749486031504056, "dur": 1, "ph": "X", "name": "ProcessMessages 132", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749486031504058, "dur": 49, "ph": "X", "name": "ReadAsync 132", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749486031504110, "dur": 1, "ph": "X", "name": "ProcessMessages 128", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749486031504112, "dur": 154, "ph": "X", "name": "ReadAsync 128", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749486031504276, "dur": 4, "ph": "X", "name": "ProcessMessages 96", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749486031504282, "dur": 36, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749486031504320, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749486031504322, "dur": 50, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749486031504375, "dur": 82, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749486031504463, "dur": 3, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749486031504469, "dur": 128, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749486031504600, "dur": 1, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749486031504602, "dur": 402, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749486031505011, "dur": 1, "ph": "X", "name": "ProcessMessages 96", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749486031505012, "dur": 192120, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749486031697141, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749486031697145, "dur": 25, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749486031697171, "dur": 2729, "ph": "X", "name": "ProcessMessages 353", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749486031699903, "dur": 115177, "ph": "X", "name": "ReadAsync 353", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749486031815089, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749486031815092, "dur": 578, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749486031815682, "dur": 5, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749486031815690, "dur": 623173, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749486032438870, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749486032438873, "dur": 86, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749486032438964, "dur": 2, "ph": "X", "name": "ProcessMessages 28", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749486032438967, "dur": 53591, "ph": "X", "name": "ReadAsync 28", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749486032492567, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749486032492570, "dur": 21, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749486032492594, "dur": 17, "ph": "X", "name": "ProcessMessages 429", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749486032492612, "dur": 15761, "ph": "X", "name": "ReadAsync 429", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749486032508380, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749486032508382, "dur": 18, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749486032508403, "dur": 4221, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749486032512629, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749486032512631, "dur": 29, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749486032512665, "dur": 19, "ph": "X", "name": "ProcessMessages 423", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749486032512686, "dur": 15606, "ph": "X", "name": "ReadAsync 423", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749486032528299, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749486032528302, "dur": 18, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749486032528321, "dur": 2, "ph": "X", "name": "ProcessMessages 8", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749486032528324, "dur": 1478, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749486032529807, "dur": 24, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749486032529833, "dur": 16, "ph": "X", "name": "ProcessMessages 34", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749486032529850, "dur": 94311, "ph": "X", "name": "ReadAsync 34", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749486032624167, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749486032624170, "dur": 163, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749486032624336, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749486032624337, "dur": 625828, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749486033250172, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749486033250175, "dur": 100, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749486033250282, "dur": 3, "ph": "X", "name": "ProcessMessages 8", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749486033250287, "dur": 1479, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749486033251770, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749486033251772, "dur": 120, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749486033251896, "dur": 32, "ph": "X", "name": "ProcessMessages 50", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749486033251930, "dur": 442, "ph": "X", "name": "ReadAsync 50", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749486033252377, "dur": 17, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749486033252396, "dur": 333, "ph": "X", "name": "ProcessMessages 13", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749486033252732, "dur": 9163, "ph": "X", "name": "ReadAsync 13", "args": {}}, {"pid": 18384, "tid": 606, "ts": 1749486033271068, "dur": 708, "ph": "X", "name": "ReadEntireBinlogFromIpcAsync", "args": {}}, {"pid": 18384, "tid": 8589934592, "ph": "M", "name": "thread_name", "args": {"name": "WaitForBuildProgramInputDataBeingWrittenAndSendDagReadyMessageAsync"}}, {"pid": 18384, "tid": 8589934592, "ts": 1749486031368468, "dur": 65556, "ph": "X", "name": "await writeBuildProgramInputDataTask", "args": {}}, {"pid": 18384, "tid": 8589934592, "ts": 1749486031434026, "dur": 3, "ph": "X", "name": "WritePipe.WaitConnectionAsync", "args": {}}, {"pid": 18384, "tid": 8589934592, "ts": 1749486031434030, "dur": 836, "ph": "X", "name": "WriteDagReadyMessage", "args": {}}, {"pid": 18384, "tid": 606, "ts": 1749486033271777, "dur": 3, "ph": "X", "name": "WaitForBuildProgramInputDataBeingWrittenAndSendDagReadyMessageAsync", "args": {}}, {"pid": 18384, "tid": 4294967296, "ph": "M", "name": "thread_name", "args": {"name": "BuildAsync"}}, {"pid": 18384, "tid": 4294967296, "ts": 1749486031355246, "dur": 1907417, "ph": "X", "name": "RunBackend", "args": {}}, {"pid": 18384, "tid": 4294967296, "ts": 1749486031357666, "dur": 5582, "ph": "X", "name": "BackendProgram.Start", "args": {}}, {"pid": 18384, "tid": 4294967296, "ts": 1749486033262675, "dur": 2884, "ph": "X", "name": "await WaitForAndApplyScriptUpdaters", "args": {}}, {"pid": 18384, "tid": 4294967296, "ts": 1749486033264387, "dur": 66, "ph": "X", "name": "await <PERSON><PERSON>tUp<PERSON><PERSON>", "args": {}}, {"pid": 18384, "tid": 4294967296, "ts": 1749486033265609, "dur": 8, "ph": "X", "name": "await taskToReadBuildProgramOutput", "args": {}}, {"pid": 18384, "tid": 606, "ts": 1749486033271782, "dur": 4, "ph": "X", "name": "BuildAsync", "args": {}}, {"cat": "", "pid": 12345, "tid": 0, "ts": 0, "ph": "M", "name": "process_name", "args": {"name": "bee_backend"}}, {"pid": 12345, "tid": 0, "ts": 1749486031377277, "dur": 1711, "ph": "X", "name": "DriverInitData", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1749486031378997, "dur": 649, "ph": "X", "name": "RemoveStaleOutputs", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1749486031379775, "dur": 61, "ph": "X", "name": "<PERSON><PERSON>", "args": {"detail": "PrepareNodes"}}, {"pid": 12345, "tid": 0, "ts": 1749486031379836, "dur": 524, "ph": "X", "name": "BuildQueueInit", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1749486031380962, "dur": 428, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TerrainPhysicsModule.dll_D293A48779142A71.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1749486031382099, "dur": 986, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Burst.Unsafe.dll_D2F5C6CBE8D78A56.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1749486031386719, "dur": 81, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.2D.Animation.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1749486031386927, "dur": 52, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.2D.PixelPerfect.ref.dll_88C9F7A57CCB704B.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1749486031380386, "dur": 14784, "ph": "X", "name": "EnqueueRequestedNodes", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1749486031395182, "dur": 1855405, "ph": "X", "name": "WaitForBuildFinished", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1749486033250588, "dur": 77, "ph": "X", "name": "JoinBuildThread", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1749486033250737, "dur": 153, "ph": "X", "name": "JoinBuildThread", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1749486033251089, "dur": 1574, "ph": "X", "name": "<PERSON><PERSON>", "args": {"detail": "Write AllBuiltNodes"}}, {"pid": 12345, "tid": 1, "ts": 1749486031380429, "dur": 14763, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1749486031395219, "dur": 113, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1749486031395389, "dur": 364, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Tools\\ScriptUpdater\\ApiUpdater.MovedFromExtractor.exe"}}, {"pid": 12345, "tid": 1, "ts": 1749486031395345, "dur": 409, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ClusterInputModule.dll_63198FADDC2DE5CF.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1749486031395896, "dur": 120, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1749486031396045, "dur": 65, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1749486031396131, "dur": 89, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.UIElementsModule.dll"}}, {"pid": 12345, "tid": 1, "ts": 1749486031396130, "dur": 93, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UIElementsModule.dll_D441A9CFEC32007A.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1749486031396223, "dur": 52, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1749486031396315, "dur": 363, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1749486031396685, "dur": 71, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.collab-proxy@2.5.2\\Lib\\Editor\\PlasticSCM\\Unity.Plastic.Newtonsoft.Json.dll"}}, {"pid": 12345, "tid": 1, "ts": 1749486031396684, "dur": 73, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Plastic.Newtonsoft.Json.dll_7880F7755F74374F.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1749486031397059, "dur": 392, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1749486031397724, "dur": 76, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1749486031397932, "dur": 55, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1749486031398250, "dur": 74, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1749486031398427, "dur": 233, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1749486031398694, "dur": 1252, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1749486031399946, "dur": 1497, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1749486031401444, "dur": 1670, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1749486031403114, "dur": 1598, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1749486031404721, "dur": 1504, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1749486031406226, "dur": 1290, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1749486031407517, "dur": 2230, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1749486031409748, "dur": 1633, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1749486031411382, "dur": 2064, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1749486031413447, "dur": 1620, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1749486031415067, "dur": 1596, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1749486031416663, "dur": 1871, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1749486031418534, "dur": 1805, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1749486031420340, "dur": 1753, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1749486031422094, "dur": 1413, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1749486031423508, "dur": 1334, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1749486031424843, "dur": 626, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1749486031425469, "dur": 498, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1749486031425969, "dur": 801, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.Timeline.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1749486031426771, "dur": 129, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1749486031426903, "dur": 511, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Timeline.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1749486031427415, "dur": 126, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1749486031427548, "dur": 1217, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1749486031428769, "dur": 181, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.Timeline.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1749486031428995, "dur": 730, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Timeline.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1749486031429726, "dur": 191, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1749486031429926, "dur": 52, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1749486031429982, "dur": 63700, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1749486031494296, "dur": 50, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Diagnostics.Debug.dll"}}, {"pid": 12345, "tid": 1, "ts": 1749486031494707, "dur": 1904, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\api-ms-win-core-console-l1-1-0.dll"}}, {"pid": 12345, "tid": 1, "ts": 1749486031497490, "dur": 56, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\System.Text.Json.dll"}}, {"pid": 12345, "tid": 1, "ts": 1749486031493682, "dur": 4972, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.2D.PixelPerfect.dll (+pdb)"}}, {"pid": 12345, "tid": 1, "ts": 1749486031498654, "dur": 185, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1749486031498878, "dur": 773, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEditor.TextCoreTextEngineModule.dll"}}, {"pid": 12345, "tid": 1, "ts": 1749486031500671, "dur": 92, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\api-ms-win-core-synch-l1-1-0.dll"}}, {"pid": 12345, "tid": 1, "ts": 1749486031498848, "dur": 3089, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.2D.PixelPerfect.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 1, "ts": 1749486031501938, "dur": 102, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1749486031502047, "dur": 59, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1749486031502144, "dur": 626, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1749486031502774, "dur": 224, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1749486031503002, "dur": 72, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1749486031503080, "dur": 1747486, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1749486031380532, "dur": 14693, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1749486031395242, "dur": 72, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.ARModule.dll"}}, {"pid": 12345, "tid": 2, "ts": 1749486031395315, "dur": 751, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Tools\\ScriptUpdater\\ApiUpdater.MovedFromExtractor.exe"}}, {"pid": 12345, "tid": 2, "ts": 1749486031395232, "dur": 835, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ARModule.dll_A3A3243B74248B90.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1749486031396067, "dur": 79, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1749486031396154, "dur": 76, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.UIModule.dll"}}, {"pid": 12345, "tid": 2, "ts": 1749486031396152, "dur": 81, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UIModule.dll_E5A3FD65734E3273.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1749486031396234, "dur": 103, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1749486031396348, "dur": 67, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.VideoModule.dll"}}, {"pid": 12345, "tid": 2, "ts": 1749486031396346, "dur": 71, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.VideoModule.dll_B8A7170076CF7080.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1749486031396418, "dur": 328, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1749486031396862, "dur": 76, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1749486031396974, "dur": 81, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1749486031397093, "dur": 58, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1749486031397419, "dur": 50, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1749486031397482, "dur": 68, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1749486031397550, "dur": 51, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.2D.Common.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 2, "ts": 1749486031397608, "dur": 176, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1749486031397784, "dur": 68, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.2D.SpriteShape.Runtime.rsp2"}}, {"pid": 12345, "tid": 2, "ts": 1749486031397854, "dur": 63, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1749486031397922, "dur": 77, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1749486031398035, "dur": 178, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1749486031398260, "dur": 61, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1749486031398324, "dur": 126, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/10571807241835812913.rsp"}}, {"pid": 12345, "tid": 2, "ts": 1749486031398451, "dur": 265, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1749486031398725, "dur": 1818, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1749486031400543, "dur": 1849, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1749486031402392, "dur": 2059, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1749486031404452, "dur": 1489, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1749486031405942, "dur": 2711, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1749486031408653, "dur": 1723, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1749486031410376, "dur": 1959, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1749486031412335, "dur": 1692, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1749486031414027, "dur": 1595, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1749486031415622, "dur": 1747, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1749486031417369, "dur": 1473, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1749486031418842, "dur": 1940, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1749486031420782, "dur": 1874, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1749486031422657, "dur": 1011, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1749486031423668, "dur": 1402, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1749486031425070, "dur": 365, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1749486031425435, "dur": 477, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1749486031425913, "dur": 90, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.Mathematics.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1749486031426003, "dur": 772, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1749486031426779, "dur": 903, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Mathematics.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1749486031427683, "dur": 418, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1749486031428124, "dur": 58, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1749486031428207, "dur": 182, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.2D.Common.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1749486031428389, "dur": 113, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1749486031428513, "dur": 520, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.2D.Common.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1749486031429034, "dur": 110, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1749486031429186, "dur": 79, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.2D.Common.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1749486031429302, "dur": 365, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.2D.Common.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1749486031429667, "dur": 669, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1749486031430350, "dur": 61, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1749486031430418, "dur": 118, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.2D.Animation.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1749486031430536, "dur": 66, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1749486031430606, "dur": 637, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.2D.Animation.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1749486031431244, "dur": 180, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1749486031431437, "dur": 55, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1749486031431504, "dur": 111, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.2D.Psdimporter.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1749486031431616, "dur": 59, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1749486031431679, "dur": 373, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.2D.Psdimporter.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1749486031432053, "dur": 79, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1749486031432166, "dur": 58799, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1749486031491233, "dur": 62, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.DirectorModule.dll"}}, {"pid": 12345, "tid": 2, "ts": 1749486031493578, "dur": 314, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\System.Threading.Overlapped.dll"}}, {"pid": 12345, "tid": 2, "ts": 1749486031494663, "dur": 153, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Reflection.Emit.Lightweight.dll"}}, {"pid": 12345, "tid": 2, "ts": 1749486031490969, "dur": 4367, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.2D.SpriteShape.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 2, "ts": 1749486031495337, "dur": 676, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1749486031496069, "dur": 94, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEditor.PresetsUIModule.dll"}}, {"pid": 12345, "tid": 2, "ts": 1749486031497106, "dur": 117, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\api-ms-win-core-sysinfo-l1-1-0.dll"}}, {"pid": 12345, "tid": 2, "ts": 1749486031498055, "dur": 52, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\System.Net.WebSockets.Client.dll"}}, {"pid": 12345, "tid": 2, "ts": 1749486031496025, "dur": 3369, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.2D.SpriteShape.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 2, "ts": 1749486031499395, "dur": 1695, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1749486031501097, "dur": 1957, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Burst.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 2, "ts": 1749486031503054, "dur": 83, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1749486031503144, "dur": 57, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1749486031503205, "dur": 1747383, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1749486031380611, "dur": 14810, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1749486031395445, "dur": 94, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.CoreModule.dll"}}, {"pid": 12345, "tid": 3, "ts": 1749486031395540, "dur": 638, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Tools\\ScriptUpdater\\ApiUpdater.MovedFromExtractor.exe"}}, {"pid": 12345, "tid": 3, "ts": 1749486031395433, "dur": 746, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.CoreModule.dll_71CF6729598465FE.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1749486031396180, "dur": 136, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1749486031396355, "dur": 279, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1749486031396637, "dur": 277, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\PlaybackEngines\\WindowsStandaloneSupport\\UnityEditor.WindowsStandalone.Extensions.dll"}}, {"pid": 12345, "tid": 3, "ts": 1749486031396636, "dur": 279, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.WindowsStandalone.Extensions.dll_9F8263619ADFDEFD.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1749486031396916, "dur": 240, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1749486031397335, "dur": 58, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/UnityEngine.UI.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1749486031397540, "dur": 78, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netfx\\mscorlib.dll"}}, {"pid": 12345, "tid": 3, "ts": 1749486031397620, "dur": 192, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netfx\\System.ComponentModel.Composition.dll"}}, {"pid": 12345, "tid": 3, "ts": 1749486031397946, "dur": 142, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netfx\\System.Drawing.dll"}}, {"pid": 12345, "tid": 3, "ts": 1749486031398139, "dur": 73, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netfx\\System.Net.dll"}}, {"pid": 12345, "tid": 3, "ts": 1749486031398259, "dur": 168, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netfx\\System.Runtime.Serialization.dll"}}, {"pid": 12345, "tid": 3, "ts": 1749486031398429, "dur": 99, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netfx\\System.ServiceModel.Web.dll"}}, {"pid": 12345, "tid": 3, "ts": 1749486031398529, "dur": 104, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netfx\\System.Transactions.dll"}}, {"pid": 12345, "tid": 3, "ts": 1749486031398634, "dur": 86, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netfx\\System.Web.dll"}}, {"pid": 12345, "tid": 3, "ts": 1749486031398721, "dur": 91, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netfx\\System.Windows.dll"}}, {"pid": 12345, "tid": 3, "ts": 1749486031398813, "dur": 78, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netfx\\System.Xml.dll"}}, {"pid": 12345, "tid": 3, "ts": 1749486031398892, "dur": 113, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netfx\\System.Xml.Linq.dll"}}, {"pid": 12345, "tid": 3, "ts": 1749486031399007, "dur": 245, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netfx\\System.Xml.Serialization.dll"}}, {"pid": 12345, "tid": 3, "ts": 1749486031399254, "dur": 181, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\Microsoft.Win32.Primitives.dll"}}, {"pid": 12345, "tid": 3, "ts": 1749486031399469, "dur": 81, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Buffers.dll"}}, {"pid": 12345, "tid": 3, "ts": 1749486031399552, "dur": 85, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Collections.Concurrent.dll"}}, {"pid": 12345, "tid": 3, "ts": 1749486031399638, "dur": 86, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Collections.dll"}}, {"pid": 12345, "tid": 3, "ts": 1749486031399726, "dur": 138, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Collections.NonGeneric.dll"}}, {"pid": 12345, "tid": 3, "ts": 1749486031399866, "dur": 162, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Collections.Specialized.dll"}}, {"pid": 12345, "tid": 3, "ts": 1749486031400030, "dur": 170, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.ComponentModel.dll"}}, {"pid": 12345, "tid": 3, "ts": 1749486031400201, "dur": 60, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.ComponentModel.EventBasedAsync.dll"}}, {"pid": 12345, "tid": 3, "ts": 1749486031400262, "dur": 74, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.ComponentModel.Primitives.dll"}}, {"pid": 12345, "tid": 3, "ts": 1749486031400340, "dur": 103, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.ComponentModel.TypeConverter.dll"}}, {"pid": 12345, "tid": 3, "ts": 1749486031400444, "dur": 75, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Console.dll"}}, {"pid": 12345, "tid": 3, "ts": 1749486031400520, "dur": 60, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Data.Common.dll"}}, {"pid": 12345, "tid": 3, "ts": 1749486031400581, "dur": 138, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Diagnostics.Contracts.dll"}}, {"pid": 12345, "tid": 3, "ts": 1749486031400720, "dur": 127, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Diagnostics.Debug.dll"}}, {"pid": 12345, "tid": 3, "ts": 1749486031400849, "dur": 112, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Diagnostics.FileVersionInfo.dll"}}, {"pid": 12345, "tid": 3, "ts": 1749486031400962, "dur": 107, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Diagnostics.Process.dll"}}, {"pid": 12345, "tid": 3, "ts": 1749486031401070, "dur": 83, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Diagnostics.StackTrace.dll"}}, {"pid": 12345, "tid": 3, "ts": 1749486031401154, "dur": 77, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Diagnostics.TextWriterTraceListener.dll"}}, {"pid": 12345, "tid": 3, "ts": 1749486031401232, "dur": 77, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Diagnostics.Tools.dll"}}, {"pid": 12345, "tid": 3, "ts": 1749486031401310, "dur": 117, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Diagnostics.TraceSource.dll"}}, {"pid": 12345, "tid": 3, "ts": 1749486031401429, "dur": 224, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Diagnostics.Tracing.dll"}}, {"pid": 12345, "tid": 3, "ts": 1749486031401654, "dur": 227, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Drawing.Primitives.dll"}}, {"pid": 12345, "tid": 3, "ts": 1749486031401884, "dur": 50, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Globalization.Calendars.dll"}}, {"pid": 12345, "tid": 3, "ts": 1749486031401981, "dur": 195, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Globalization.Extensions.dll"}}, {"pid": 12345, "tid": 3, "ts": 1749486031402177, "dur": 294, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.IO.Compression.dll"}}, {"pid": 12345, "tid": 3, "ts": 1749486031402475, "dur": 68, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.IO.FileSystem.dll"}}, {"pid": 12345, "tid": 3, "ts": 1749486031402544, "dur": 82, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.IO.FileSystem.DriveInfo.dll"}}, {"pid": 12345, "tid": 3, "ts": 1749486031402628, "dur": 81, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.IO.FileSystem.Primitives.dll"}}, {"pid": 12345, "tid": 3, "ts": 1749486031402710, "dur": 109, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.IO.FileSystem.Watcher.dll"}}, {"pid": 12345, "tid": 3, "ts": 1749486031402820, "dur": 107, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.IO.IsolatedStorage.dll"}}, {"pid": 12345, "tid": 3, "ts": 1749486031402929, "dur": 183, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.IO.MemoryMappedFiles.dll"}}, {"pid": 12345, "tid": 3, "ts": 1749486031403113, "dur": 190, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.IO.Pipes.dll"}}, {"pid": 12345, "tid": 3, "ts": 1749486031403305, "dur": 86, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.IO.UnmanagedMemoryStream.dll"}}, {"pid": 12345, "tid": 3, "ts": 1749486031403392, "dur": 115, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Linq.dll"}}, {"pid": 12345, "tid": 3, "ts": 1749486031403550, "dur": 56, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Linq.Parallel.dll"}}, {"pid": 12345, "tid": 3, "ts": 1749486031403610, "dur": 62, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Linq.Queryable.dll"}}, {"pid": 12345, "tid": 3, "ts": 1749486031403673, "dur": 182, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Memory.dll"}}, {"pid": 12345, "tid": 3, "ts": 1749486031404067, "dur": 123, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Net.Security.dll"}}, {"pid": 12345, "tid": 3, "ts": 1749486031404193, "dur": 112, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Net.WebHeaderCollection.dll"}}, {"pid": 12345, "tid": 3, "ts": 1749486031404307, "dur": 176, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Net.WebSockets.Client.dll"}}, {"pid": 12345, "tid": 3, "ts": 1749486031404484, "dur": 115, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Net.WebSockets.dll"}}, {"pid": 12345, "tid": 3, "ts": 1749486031404600, "dur": 145, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Numerics.Vectors.dll"}}, {"pid": 12345, "tid": 3, "ts": 1749486031404746, "dur": 69, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.ObjectModel.dll"}}, {"pid": 12345, "tid": 3, "ts": 1749486031404817, "dur": 111, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Reflection.DispatchProxy.dll"}}, {"pid": 12345, "tid": 3, "ts": 1749486031404929, "dur": 86, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Reflection.dll"}}, {"pid": 12345, "tid": 3, "ts": 1749486031405016, "dur": 194, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Reflection.Emit.dll"}}, {"pid": 12345, "tid": 3, "ts": 1749486031405212, "dur": 126, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Reflection.Emit.ILGeneration.dll"}}, {"pid": 12345, "tid": 3, "ts": 1749486031405339, "dur": 199, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Reflection.Emit.Lightweight.dll"}}, {"pid": 12345, "tid": 3, "ts": 1749486031405539, "dur": 79, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Reflection.Extensions.dll"}}, {"pid": 12345, "tid": 3, "ts": 1749486031405619, "dur": 231, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Reflection.Primitives.dll"}}, {"pid": 12345, "tid": 3, "ts": 1749486031405851, "dur": 62, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Resources.Reader.dll"}}, {"pid": 12345, "tid": 3, "ts": 1749486031405924, "dur": 162, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Resources.ResourceManager.dll"}}, {"pid": 12345, "tid": 3, "ts": 1749486031406087, "dur": 94, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Resources.Writer.dll"}}, {"pid": 12345, "tid": 3, "ts": 1749486031406182, "dur": 84, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Runtime.CompilerServices.VisualC.dll"}}, {"pid": 12345, "tid": 3, "ts": 1749486031406268, "dur": 87, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Runtime.dll"}}, {"pid": 12345, "tid": 3, "ts": 1749486031406356, "dur": 82, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Runtime.Extensions.dll"}}, {"pid": 12345, "tid": 3, "ts": 1749486031406439, "dur": 198, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Runtime.Handles.dll"}}, {"pid": 12345, "tid": 3, "ts": 1749486031406660, "dur": 124, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Runtime.Numerics.dll"}}, {"pid": 12345, "tid": 3, "ts": 1749486031406785, "dur": 72, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Runtime.Serialization.Formatters.dll"}}, {"pid": 12345, "tid": 3, "ts": 1749486031406859, "dur": 137, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Runtime.Serialization.Json.dll"}}, {"pid": 12345, "tid": 3, "ts": 1749486031406997, "dur": 83, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Runtime.Serialization.Primitives.dll"}}, {"pid": 12345, "tid": 3, "ts": 1749486031407085, "dur": 124, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Runtime.Serialization.Xml.dll"}}, {"pid": 12345, "tid": 3, "ts": 1749486031407210, "dur": 68, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Security.Claims.dll"}}, {"pid": 12345, "tid": 3, "ts": 1749486031407279, "dur": 54, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Security.Cryptography.Algorithms.dll"}}, {"pid": 12345, "tid": 3, "ts": 1749486031407334, "dur": 136, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Security.Cryptography.Csp.dll"}}, {"pid": 12345, "tid": 3, "ts": 1749486031407471, "dur": 105, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Security.Cryptography.Encoding.dll"}}, {"pid": 12345, "tid": 3, "ts": 1749486031407577, "dur": 510, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Security.Cryptography.Primitives.dll"}}, {"pid": 12345, "tid": 3, "ts": 1749486031408089, "dur": 79, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Security.Cryptography.X509Certificates.dll"}}, {"pid": 12345, "tid": 3, "ts": 1749486031408169, "dur": 101, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Security.Principal.dll"}}, {"pid": 12345, "tid": 3, "ts": 1749486031408271, "dur": 72, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Security.SecureString.dll"}}, {"pid": 12345, "tid": 3, "ts": 1749486031408344, "dur": 59, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Text.Encoding.dll"}}, {"pid": 12345, "tid": 3, "ts": 1749486031408404, "dur": 67, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Text.Encoding.Extensions.dll"}}, {"pid": 12345, "tid": 3, "ts": 1749486031408472, "dur": 59, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Text.RegularExpressions.dll"}}, {"pid": 12345, "tid": 3, "ts": 1749486031408532, "dur": 51, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Threading.dll"}}, {"pid": 12345, "tid": 3, "ts": 1749486031408633, "dur": 139, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Threading.Tasks.dll"}}, {"pid": 12345, "tid": 3, "ts": 1749486031408774, "dur": 63, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Threading.Tasks.Extensions.dll"}}, {"pid": 12345, "tid": 3, "ts": 1749486031408838, "dur": 110, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Threading.Tasks.Parallel.dll"}}, {"pid": 12345, "tid": 3, "ts": 1749486031408950, "dur": 98, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Threading.Thread.dll"}}, {"pid": 12345, "tid": 3, "ts": 1749486031409049, "dur": 86, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Threading.ThreadPool.dll"}}, {"pid": 12345, "tid": 3, "ts": 1749486031409139, "dur": 78, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Threading.Timer.dll"}}, {"pid": 12345, "tid": 3, "ts": 1749486031409218, "dur": 101, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.ValueTuple.dll"}}, {"pid": 12345, "tid": 3, "ts": 1749486031409320, "dur": 160, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Xml.ReaderWriter.dll"}}, {"pid": 12345, "tid": 3, "ts": 1749486031409568, "dur": 68, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Xml.XmlSerializer.dll"}}, {"pid": 12345, "tid": 3, "ts": 1749486031409637, "dur": 105, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Xml.XPath.dll"}}, {"pid": 12345, "tid": 3, "ts": 1749486031409743, "dur": 153, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Xml.XPath.XDocument.dll"}}, {"pid": 12345, "tid": 3, "ts": 1749486031409941, "dur": 85, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\NetStandard\\ref\\2.1.0\\netstandard.dll"}}, {"pid": 12345, "tid": 3, "ts": 1749486031410030, "dur": 160, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.collab-proxy@2.5.2\\Lib\\Editor\\PlasticSCM\\log4netPlastic.dll"}}, {"pid": 12345, "tid": 3, "ts": 1749486031410195, "dur": 52, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.collab-proxy@2.5.2\\Lib\\Editor\\PlasticSCM\\unityplastic.dll"}}, {"pid": 12345, "tid": 3, "ts": 1749486031410261, "dur": 136, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.render-pipelines.universal@14.0.11\\Editor\\ShaderGUI\\MaterialAssemblyReference\\RawRenderQueue.cs"}}, {"pid": 12345, "tid": 3, "ts": 1749486031410398, "dur": 154, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.shadergraph@14.0.11\\Editor\\Generation\\Targets\\BuiltIn\\Editor\\ShaderGUI\\MaterialAssemblyReference\\RawRenderQueue.cs"}}, {"pid": 12345, "tid": 3, "ts": 1749486031410553, "dur": 86, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.ugui@1.0.0\\Runtime\\EventSystem\\EventData\\AxisEventData.cs"}}, {"pid": 12345, "tid": 3, "ts": 1749486031410639, "dur": 51, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.ugui@1.0.0\\Runtime\\EventSystem\\EventData\\BaseEventData.cs"}}, {"pid": 12345, "tid": 3, "ts": 1749486031410691, "dur": 283, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.ugui@1.0.0\\Runtime\\EventSystem\\EventData\\PointerEventData.cs"}}, {"pid": 12345, "tid": 3, "ts": 1749486031410975, "dur": 183, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.ugui@1.0.0\\Runtime\\EventSystem\\EventHandle.cs"}}, {"pid": 12345, "tid": 3, "ts": 1749486031411159, "dur": 90, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.ugui@1.0.0\\Runtime\\EventSystem\\EventInterfaces.cs"}}, {"pid": 12345, "tid": 3, "ts": 1749486031411250, "dur": 128, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.ugui@1.0.0\\Runtime\\EventSystem\\EventSystem.cs"}}, {"pid": 12345, "tid": 3, "ts": 1749486031411379, "dur": 64, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.ugui@1.0.0\\Runtime\\EventSystem\\EventTrigger.cs"}}, {"pid": 12345, "tid": 3, "ts": 1749486031411444, "dur": 57, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.ugui@1.0.0\\Runtime\\EventSystem\\EventTriggerType.cs"}}, {"pid": 12345, "tid": 3, "ts": 1749486031411502, "dur": 60, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.ugui@1.0.0\\Runtime\\EventSystem\\ExecuteEvents.cs"}}, {"pid": 12345, "tid": 3, "ts": 1749486031411563, "dur": 81, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.ugui@1.0.0\\Runtime\\EventSystem\\InputModules\\BaseInput.cs"}}, {"pid": 12345, "tid": 3, "ts": 1749486031411645, "dur": 135, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.ugui@1.0.0\\Runtime\\EventSystem\\InputModules\\BaseInputModule.cs"}}, {"pid": 12345, "tid": 3, "ts": 1749486031411931, "dur": 61, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.ugui@1.0.0\\Runtime\\EventSystem\\RaycasterManager.cs"}}, {"pid": 12345, "tid": 3, "ts": 1749486031411993, "dur": 96, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.ugui@1.0.0\\Runtime\\EventSystem\\Raycasters\\BaseRaycaster.cs"}}, {"pid": 12345, "tid": 3, "ts": 1749486031412090, "dur": 72, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.ugui@1.0.0\\Runtime\\EventSystem\\Raycasters\\Physics2DRaycaster.cs"}}, {"pid": 12345, "tid": 3, "ts": 1749486031412163, "dur": 69, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.ugui@1.0.0\\Runtime\\EventSystem\\Raycasters\\PhysicsRaycaster.cs"}}, {"pid": 12345, "tid": 3, "ts": 1749486031412233, "dur": 61, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.ugui@1.0.0\\Runtime\\EventSystem\\RaycastResult.cs"}}, {"pid": 12345, "tid": 3, "ts": 1749486031412295, "dur": 183, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.ugui@1.0.0\\Runtime\\EventSystem\\UIBehaviour.cs"}}, {"pid": 12345, "tid": 3, "ts": 1749486031412479, "dur": 62, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.ugui@1.0.0\\Runtime\\EventSystem\\UIElements\\PanelEventHandler.cs"}}, {"pid": 12345, "tid": 3, "ts": 1749486031412542, "dur": 138, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.ugui@1.0.0\\Runtime\\EventSystem\\UIElements\\PanelRaycaster.cs"}}, {"pid": 12345, "tid": 3, "ts": 1749486031412747, "dur": 218, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.ugui@1.0.0\\Runtime\\UI\\Core\\AnimationTriggers.cs"}}, {"pid": 12345, "tid": 3, "ts": 1749486031412966, "dur": 91, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.ugui@1.0.0\\Runtime\\UI\\Core\\Button.cs"}}, {"pid": 12345, "tid": 3, "ts": 1749486031413058, "dur": 129, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.ugui@1.0.0\\Runtime\\UI\\Core\\CanvasUpdateRegistry.cs"}}, {"pid": 12345, "tid": 3, "ts": 1749486031413188, "dur": 83, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.ugui@1.0.0\\Runtime\\UI\\Core\\ColorBlock.cs"}}, {"pid": 12345, "tid": 3, "ts": 1749486031413272, "dur": 68, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.ugui@1.0.0\\Runtime\\UI\\Core\\Culling\\ClipperRegistry.cs"}}, {"pid": 12345, "tid": 3, "ts": 1749486031413340, "dur": 61, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.ugui@1.0.0\\Runtime\\UI\\Core\\Culling\\Clipping.cs"}}, {"pid": 12345, "tid": 3, "ts": 1749486031413402, "dur": 86, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.ugui@1.0.0\\Runtime\\UI\\Core\\Culling\\IClipRegion.cs"}}, {"pid": 12345, "tid": 3, "ts": 1749486031413494, "dur": 105, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.ugui@1.0.0\\Runtime\\UI\\Core\\Culling\\RectangularVertexClipper.cs"}}, {"pid": 12345, "tid": 3, "ts": 1749486031413600, "dur": 67, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.ugui@1.0.0\\Runtime\\UI\\Core\\DefaultControls.cs"}}, {"pid": 12345, "tid": 3, "ts": 1749486031413667, "dur": 75, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.ugui@1.0.0\\Runtime\\UI\\Core\\Dropdown.cs"}}, {"pid": 12345, "tid": 3, "ts": 1749486031413743, "dur": 70, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.ugui@1.0.0\\Runtime\\UI\\Core\\FontData.cs"}}, {"pid": 12345, "tid": 3, "ts": 1749486031413814, "dur": 59, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.ugui@1.0.0\\Runtime\\UI\\Core\\FontUpdateTracker.cs"}}, {"pid": 12345, "tid": 3, "ts": 1749486031413874, "dur": 175, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.ugui@1.0.0\\Runtime\\UI\\Core\\Graphic.cs"}}, {"pid": 12345, "tid": 3, "ts": 1749486031414050, "dur": 64, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.ugui@1.0.0\\Runtime\\UI\\Core\\GraphicRaycaster.cs"}}, {"pid": 12345, "tid": 3, "ts": 1749486031414115, "dur": 65, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.ugui@1.0.0\\Runtime\\UI\\Core\\GraphicRebuildTracker.cs"}}, {"pid": 12345, "tid": 3, "ts": 1749486031414181, "dur": 61, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.ugui@1.0.0\\Runtime\\UI\\Core\\GraphicRegistry.cs"}}, {"pid": 12345, "tid": 3, "ts": 1749486031414246, "dur": 75, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.ugui@1.0.0\\Runtime\\UI\\Core\\IGraphicEnabledDisabled.cs"}}, {"pid": 12345, "tid": 3, "ts": 1749486031414322, "dur": 89, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.ugui@1.0.0\\Runtime\\UI\\Core\\Image.cs"}}, {"pid": 12345, "tid": 3, "ts": 1749486031414412, "dur": 166, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.ugui@1.0.0\\Runtime\\UI\\Core\\IMask.cs"}}, {"pid": 12345, "tid": 3, "ts": 1749486031414579, "dur": 66, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.ugui@1.0.0\\Runtime\\UI\\Core\\IMaskable.cs"}}, {"pid": 12345, "tid": 3, "ts": 1749486031414646, "dur": 62, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.ugui@1.0.0\\Runtime\\UI\\Core\\InputField.cs"}}, {"pid": 12345, "tid": 3, "ts": 1749486031414709, "dur": 79, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.ugui@1.0.0\\Runtime\\UI\\Core\\Layout\\AspectRatioFitter.cs"}}, {"pid": 12345, "tid": 3, "ts": 1749486031414789, "dur": 80, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.ugui@1.0.0\\Runtime\\UI\\Core\\Layout\\CanvasScaler.cs"}}, {"pid": 12345, "tid": 3, "ts": 1749486031414870, "dur": 72, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.ugui@1.0.0\\Runtime\\UI\\Core\\Layout\\ContentSizeFitter.cs"}}, {"pid": 12345, "tid": 3, "ts": 1749486031414943, "dur": 198, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.ugui@1.0.0\\Runtime\\UI\\Core\\Layout\\GridLayoutGroup.cs"}}, {"pid": 12345, "tid": 3, "ts": 1749486031415142, "dur": 149, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.ugui@1.0.0\\Runtime\\UI\\Core\\Layout\\HorizontalLayoutGroup.cs"}}, {"pid": 12345, "tid": 3, "ts": 1749486031415292, "dur": 99, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.ugui@1.0.0\\Runtime\\UI\\Core\\Layout\\HorizontalOrVerticalLayoutGroup.cs"}}, {"pid": 12345, "tid": 3, "ts": 1749486031415437, "dur": 53, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.ugui@1.0.0\\Runtime\\UI\\Core\\Layout\\LayoutElement.cs"}}, {"pid": 12345, "tid": 3, "ts": 1749486031415491, "dur": 76, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.ugui@1.0.0\\Runtime\\UI\\Core\\Layout\\LayoutGroup.cs"}}, {"pid": 12345, "tid": 3, "ts": 1749486031415567, "dur": 137, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.ugui@1.0.0\\Runtime\\UI\\Core\\Layout\\LayoutRebuilder.cs"}}, {"pid": 12345, "tid": 3, "ts": 1749486031415706, "dur": 69, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.ugui@1.0.0\\Runtime\\UI\\Core\\Layout\\LayoutUtility.cs"}}, {"pid": 12345, "tid": 3, "ts": 1749486031415776, "dur": 200, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.ugui@1.0.0\\Runtime\\UI\\Core\\Layout\\VerticalLayoutGroup.cs"}}, {"pid": 12345, "tid": 3, "ts": 1749486031415977, "dur": 73, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.ugui@1.0.0\\Runtime\\UI\\Core\\Mask.cs"}}, {"pid": 12345, "tid": 3, "ts": 1749486031416051, "dur": 66, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.ugui@1.0.0\\Runtime\\UI\\Core\\MaskableGraphic.cs"}}, {"pid": 12345, "tid": 3, "ts": 1749486031416118, "dur": 60, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.ugui@1.0.0\\Runtime\\UI\\Core\\MaskUtilities.cs"}}, {"pid": 12345, "tid": 3, "ts": 1749486031416179, "dur": 196, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.ugui@1.0.0\\Runtime\\UI\\Core\\MaterialModifiers\\IMaterialModifier.cs"}}, {"pid": 12345, "tid": 3, "ts": 1749486031416376, "dur": 82, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.ugui@1.0.0\\Runtime\\UI\\Core\\Misc.cs"}}, {"pid": 12345, "tid": 3, "ts": 1749486031416459, "dur": 76, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.ugui@1.0.0\\Runtime\\UI\\Core\\MultipleDisplayUtilities.cs"}}, {"pid": 12345, "tid": 3, "ts": 1749486031416536, "dur": 113, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.ugui@1.0.0\\Runtime\\UI\\Core\\Navigation.cs"}}, {"pid": 12345, "tid": 3, "ts": 1749486031416901, "dur": 72, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.ugui@1.0.0\\Runtime\\UI\\Core\\Slider.cs"}}, {"pid": 12345, "tid": 3, "ts": 1749486031416974, "dur": 63, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.ugui@1.0.0\\Runtime\\UI\\Core\\SpecializedCollections\\IndexedSet.cs"}}, {"pid": 12345, "tid": 3, "ts": 1749486031417130, "dur": 80, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.ugui@1.0.0\\Runtime\\UI\\Core\\Text.cs"}}, {"pid": 12345, "tid": 3, "ts": 1749486031417210, "dur": 81, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.ugui@1.0.0\\Runtime\\UI\\Core\\Toggle.cs"}}, {"pid": 12345, "tid": 3, "ts": 1749486031417292, "dur": 74, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.ugui@1.0.0\\Runtime\\UI\\Core\\ToggleGroup.cs"}}, {"pid": 12345, "tid": 3, "ts": 1749486031417367, "dur": 90, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.ugui@1.0.0\\Runtime\\UI\\Core\\Utility\\ReflectionMethodsCache.cs"}}, {"pid": 12345, "tid": 3, "ts": 1749486031417458, "dur": 61, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.ugui@1.0.0\\Runtime\\UI\\Core\\Utility\\VertexHelper.cs"}}, {"pid": 12345, "tid": 3, "ts": 1749486031417520, "dur": 76, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.ugui@1.0.0\\Runtime\\UI\\Core\\VertexModifiers\\BaseMeshEffect.cs"}}, {"pid": 12345, "tid": 3, "ts": 1749486031417597, "dur": 81, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.ugui@1.0.0\\Runtime\\UI\\Core\\VertexModifiers\\IMeshModifier.cs"}}, {"pid": 12345, "tid": 3, "ts": 1749486031417718, "dur": 183, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.ugui@1.0.0\\Runtime\\UI\\Core\\VertexModifiers\\PositionAsUV1.cs"}}, {"pid": 12345, "tid": 3, "ts": 1749486031417943, "dur": 310, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Tools\\Unity.SourceGenerators\\Unity.Properties.SourceGenerator.dll"}}, {"pid": 12345, "tid": 3, "ts": 1749486031397430, "dur": 20874, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/UnityEngine.UI.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1749486031418305, "dur": 220, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1749486031418603, "dur": 1291, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1749486031419895, "dur": 1709, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1749486031421605, "dur": 1449, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1749486031423113, "dur": 1689, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1749486031424802, "dur": 678, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1749486031425480, "dur": 508, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1749486031425990, "dur": 515, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.Cursor.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1749486031426506, "dur": 362, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1749486031426876, "dur": 525, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Cursor.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1749486031427405, "dur": 137, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1749486031427588, "dur": 181, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1749486031427770, "dur": 79, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Config.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1749486031427849, "dur": 50, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1749486031427901, "dur": 587, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Config.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1749486031428488, "dur": 172, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1749486031428668, "dur": 1215, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1749486031429889, "dur": 72, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1749486031429961, "dur": 84, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1749486031430049, "dur": 453, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1749486031430502, "dur": 81, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1749486031430619, "dur": 59, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.2D.Aseprite.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1749486031430709, "dur": 258, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.2D.Aseprite.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1749486031430967, "dur": 87, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1749486031431085, "dur": 59865, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1749486031490951, "dur": 2632, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.2D.Animation.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 3, "ts": 1749486031493583, "dur": 1694, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1749486031495284, "dur": 1891, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.ShaderGraph.Utilities.dll (+pdb)"}}, {"pid": 12345, "tid": 3, "ts": 1749486031497176, "dur": 161, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1749486031498739, "dur": 53, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.VFXModule.dll"}}, {"pid": 12345, "tid": 3, "ts": 1749486031497341, "dur": 2143, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.VisualScripting.Shared.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 3, "ts": 1749486031499484, "dur": 2953, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1749486031502729, "dur": 73, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1749486031502811, "dur": 1023937, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1749486032526751, "dur": 146, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1900b0aE.dag\\post-processed\\Assembly-CSharp.pdb"}}, {"pid": 12345, "tid": 3, "ts": 1749486032526751, "dur": 147, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Assembly-CSharp.pdb"}}, {"pid": 12345, "tid": 3, "ts": 1749486032526977, "dur": 1434, "ph": "X", "name": "CopyFiles", "args": {"detail": "Library/ScriptAssemblies/Assembly-CSharp.pdb"}}, {"pid": 12345, "tid": 3, "ts": 1749486032528414, "dur": 722277, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1749486031380580, "dur": 14772, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1749486031395392, "dur": 501, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Tools\\ScriptUpdater\\ApiUpdater.MovedFromExtractor.exe"}}, {"pid": 12345, "tid": 4, "ts": 1749486031395358, "dur": 536, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ClusterRendererModule.dll_49D0198E18ABFDE2.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1749486031395895, "dur": 209, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1749486031396125, "dur": 76, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.TextCoreTextEngineModule.dll"}}, {"pid": 12345, "tid": 4, "ts": 1749486031396123, "dur": 81, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TextCoreTextEngineModule.dll_BD74DBBEF40E5ADA.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1749486031396205, "dur": 132, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1749486031396344, "dur": 282, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.VFXModule.dll"}}, {"pid": 12345, "tid": 4, "ts": 1749486031396343, "dur": 293, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.VFXModule.dll_A857FD1C4743904D.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1749486031397130, "dur": 318, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1749486031397574, "dur": 111, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1749486031397700, "dur": 71, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1749486031397803, "dur": 102, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1749486031397961, "dur": 152, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1749486031398117, "dur": 51, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.2D.SpriteShape.Editor.rsp"}}, {"pid": 12345, "tid": 4, "ts": 1749486031398169, "dur": 169, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1749486031398343, "dur": 77, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/11344994280883157806.rsp"}}, {"pid": 12345, "tid": 4, "ts": 1749486031398421, "dur": 90, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1749486031398539, "dur": 177, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1749486031398716, "dur": 52, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/9675442845102135732.rsp"}}, {"pid": 12345, "tid": 4, "ts": 1749486031398775, "dur": 1700, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1749486031400476, "dur": 1269, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1749486031401745, "dur": 1663, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1749486031403408, "dur": 1985, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1749486031405393, "dur": 1724, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1749486031407117, "dur": 1846, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1749486031408964, "dur": 1926, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1749486031410890, "dur": 2373, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1749486031413263, "dur": 1788, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1749486031415052, "dur": 2305, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1749486031417357, "dur": 2174, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1749486031419532, "dur": 1768, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1749486031421301, "dur": 1870, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1749486031423171, "dur": 1813, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1749486031424984, "dur": 459, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1749486031425443, "dur": 476, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1749486031425920, "dur": 173, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.Searcher.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1749486031426094, "dur": 67, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1749486031426166, "dur": 445, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Searcher.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1749486031426611, "dur": 311, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1749486031426932, "dur": 72, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1749486031427008, "dur": 509, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.ShaderLibrary.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1749486031427518, "dur": 549, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1749486031428120, "dur": 87, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1749486031428208, "dur": 688, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1749486031428896, "dur": 407, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1749486031429303, "dur": 2244, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1749486031431549, "dur": 61, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/PPv2URPConverters.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1749486031431611, "dur": 50, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1749486031431665, "dur": 237, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/PPv2URPConverters.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1749486031431902, "dur": 337, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1749486031432244, "dur": 58762, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1749486031491383, "dur": 64, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.VehiclesModule.dll"}}, {"pid": 12345, "tid": 4, "ts": 1749486031491902, "dur": 102, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Security.Principal.dll"}}, {"pid": 12345, "tid": 4, "ts": 1749486031492449, "dur": 143, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.ServiceModel.Http.dll"}}, {"pid": 12345, "tid": 4, "ts": 1749486031492593, "dur": 154, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.ServiceModel.NetTcp.dll"}}, {"pid": 12345, "tid": 4, "ts": 1749486031492803, "dur": 121, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Threading.Tasks.Parallel.dll"}}, {"pid": 12345, "tid": 4, "ts": 1749486031493032, "dur": 134, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.collab-proxy@2.5.2\\Lib\\Editor\\PlasticSCM\\log4netPlastic.dll"}}, {"pid": 12345, "tid": 4, "ts": 1749486031493667, "dur": 54, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\Microsoft.Extensions.Localization.dll"}}, {"pid": 12345, "tid": 4, "ts": 1749486031494043, "dur": 139, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\System.Resources.ResourceManager.dll"}}, {"pid": 12345, "tid": 4, "ts": 1749486031495042, "dur": 71, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Diagnostics.FileVersionInfo.dll"}}, {"pid": 12345, "tid": 4, "ts": 1749486031491009, "dur": 4483, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.Universal.Config.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 4, "ts": 1749486031495493, "dur": 165, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1749486031497344, "dur": 52, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\System.Net.Ping.dll"}}, {"pid": 12345, "tid": 4, "ts": 1749486031495664, "dur": 3122, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.2D.Common.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 4, "ts": 1749486031498786, "dur": 115, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1749486031499608, "dur": 53, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\api-ms-win-core-console-l1-1-0.dll"}}, {"pid": 12345, "tid": 4, "ts": 1749486031498906, "dur": 2581, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.TextMeshPro.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 4, "ts": 1749486031501488, "dur": 154, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1749486031501653, "dur": 136, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1749486031501795, "dur": 691, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1749486031502495, "dur": 65, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1749486031502568, "dur": 63, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1749486031502638, "dur": 57, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1749486031502701, "dur": 90, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1749486031502796, "dur": 692, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1749486031503488, "dur": 1747199, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1749486031380633, "dur": 14806, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1749486031395481, "dur": 481, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Tools\\ScriptUpdater\\ApiUpdater.MovedFromExtractor.exe"}}, {"pid": 12345, "tid": 5, "ts": 1749486031395445, "dur": 518, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.DirectorModule.dll_3AC0D0C527F5439B.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1749486031396040, "dur": 57, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1749486031396221, "dur": 55, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1749486031396320, "dur": 318, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1749486031396677, "dur": 64, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/UnityEngine.TestRunner.rsp"}}, {"pid": 12345, "tid": 5, "ts": 1749486031396798, "dur": 310, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1749486031397113, "dur": 59, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1749486031397182, "dur": 83, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1749486031397296, "dur": 74, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1749486031397374, "dur": 64, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1749486031397443, "dur": 59, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1749486031397523, "dur": 51, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1749486031397579, "dur": 62, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1749486031397724, "dur": 73, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1749486031397930, "dur": 229, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1749486031398162, "dur": 106, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1749486031398678, "dur": 1395, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1749486031400074, "dur": 1699, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1749486031401773, "dur": 1778, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1749486031403551, "dur": 2177, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1749486031405728, "dur": 1766, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1749486031407494, "dur": 1721, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1749486031409215, "dur": 2199, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1749486031411414, "dur": 1901, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1749486031413315, "dur": 1742, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1749486031415057, "dur": 1567, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1749486031416625, "dur": 1443, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1749486031418068, "dur": 1585, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1749486031419653, "dur": 1472, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1749486031421126, "dur": 1515, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1749486031422641, "dur": 1616, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1749486031424258, "dur": 1175, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1749486031425433, "dur": 487, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1749486031425921, "dur": 196, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.ShaderGraph.Utilities.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1749486031426118, "dur": 275, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1749486031426407, "dur": 704, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.ShaderGraph.Utilities.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1749486031427111, "dur": 451, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1749486031427570, "dur": 151, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1749486031427723, "dur": 222, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1749486031427945, "dur": 274, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1749486031428220, "dur": 675, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1749486031428895, "dur": 296, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1749486031429192, "dur": 140, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.2D.SpriteShape.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1749486031429333, "dur": 69, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1749486031429411, "dur": 447, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.2D.SpriteShape.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1749486031429859, "dur": 292, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1749486031430159, "dur": 102, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1749486031430270, "dur": 60719, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1749486031490990, "dur": 2592, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.VisualScripting.State.dll (+pdb)"}}, {"pid": 12345, "tid": 5, "ts": 1749486031493583, "dur": 2007, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1749486031495601, "dur": 2261, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.VisualScripting.Flow.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 5, "ts": 1749486031497862, "dur": 1033, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1749486031499610, "dur": 59, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\System.Data.dll"}}, {"pid": 12345, "tid": 5, "ts": 1749486031498899, "dur": 2040, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Cursor.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 5, "ts": 1749486031500939, "dur": 724, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1749486031501674, "dur": 63, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1749486031501744, "dur": 56, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1749486031501810, "dur": 263, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1749486031502080, "dur": 56, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1749486031502142, "dur": 72, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1749486031502220, "dur": 63, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1749486031502289, "dur": 74, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1749486031502371, "dur": 191, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1749486031502572, "dur": 73, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1749486031502651, "dur": 61, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1749486031502721, "dur": 88, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1749486031502815, "dur": 1747878, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1749486031380494, "dur": 14714, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1749486031395219, "dur": 115, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1749486031395385, "dur": 356, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Tools\\ScriptUpdater\\ApiUpdater.MovedFromExtractor.exe"}}, {"pid": 12345, "tid": 6, "ts": 1749486031395341, "dur": 401, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ClothModule.dll_FA85EF908E5A33BF.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1749486031395743, "dur": 68, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1749486031395945, "dur": 73, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.RuntimeInitializeOnLoadManagerInitializerModule.dll"}}, {"pid": 12345, "tid": 6, "ts": 1749486031395944, "dur": 76, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.RuntimeInitializeOnLoadManagerInitializerModule.dll_E586C88FEB060A87.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1749486031396072, "dur": 153, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.TerrainModule.dll"}}, {"pid": 12345, "tid": 6, "ts": 1749486031396071, "dur": 158, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TerrainModule.dll_55536420766141FA.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1749486031396230, "dur": 124, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1749486031396364, "dur": 355, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.VirtualTexturingModule.dll"}}, {"pid": 12345, "tid": 6, "ts": 1749486031396362, "dur": 359, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.VirtualTexturingModule.dll_609DF0C9F4BC7F1C.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1749486031396722, "dur": 105, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1749486031396833, "dur": 72, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1749486031396917, "dur": 390, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1749486031397355, "dur": 81, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1749486031397444, "dur": 68, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1749486031397561, "dur": 119, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1749486031397705, "dur": 68, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1749486031397883, "dur": 63, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1749486031397957, "dur": 71, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1749486031398104, "dur": 159, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1749486031398266, "dur": 68, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1749486031398337, "dur": 50, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/5546506141355401238.rsp"}}, {"pid": 12345, "tid": 6, "ts": 1749486031398568, "dur": 56, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/6210570011482043853.rsp"}}, {"pid": 12345, "tid": 6, "ts": 1749486031398687, "dur": 1907, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1749486031400594, "dur": 1691, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1749486031402286, "dur": 2068, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1749486031404355, "dur": 1801, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1749486031406156, "dur": 2201, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1749486031408357, "dur": 1720, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1749486031410077, "dur": 1466, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1749486031411543, "dur": 2176, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1749486031413719, "dur": 1646, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1749486031415365, "dur": 2049, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1749486031417415, "dur": 1740, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1749486031419155, "dur": 1578, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1749486031420733, "dur": 1662, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1749486031422396, "dur": 1190, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1749486031423587, "dur": 1144, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1749486031424731, "dur": 739, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1749486031425470, "dur": 470, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1749486031425949, "dur": 192, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.2D.Tilemap.Extras.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1749486031426142, "dur": 70, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1749486031426860, "dur": 64, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.2d.tilemap.extras@3.1.2\\Runtime\\Tiles\\IsometricRuleTile\\IsometricRuleTile.cs"}}, {"pid": 12345, "tid": 6, "ts": 1749486031426219, "dur": 721, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.2D.Tilemap.Extras.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1749486031426940, "dur": 215, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1749486031427165, "dur": 60, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1749486031427229, "dur": 370, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1749486031427599, "dur": 351, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1749486031427950, "dur": 256, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1749486031428207, "dur": 146, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.2D.Aseprite.Common.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1749486031428353, "dur": 62, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1749486031428418, "dur": 545, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.2D.Aseprite.Common.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1749486031428963, "dur": 116, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1749486031429120, "dur": 181, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1749486031429301, "dur": 335, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1749486031429637, "dur": 82, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Core.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1749486031429719, "dur": 250, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1749486031429976, "dur": 971, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Core.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1749486031430947, "dur": 112, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1749486031431106, "dur": 59852, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1749486031490977, "dur": 98, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1900b0aE.dag\\UnityEditor.TestRunner.dll"}}, {"pid": 12345, "tid": 6, "ts": 1749486031491167, "dur": 91, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.AccessibilityModule.dll"}}, {"pid": 12345, "tid": 6, "ts": 1749486031490961, "dur": 3761, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Burst.dll (+pdb)"}}, {"pid": 12345, "tid": 6, "ts": 1749486031494723, "dur": 178, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1749486031494919, "dur": 2696, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.Core.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 6, "ts": 1749486031497616, "dur": 146, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1749486031497774, "dur": 2587, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.2D.Animation.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 6, "ts": 1749486031500361, "dur": 1044, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1749486031501413, "dur": 1772, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/UnityEditor.UI.dll (+pdb)"}}, {"pid": 12345, "tid": 6, "ts": 1749486031503186, "dur": 81, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1749486031503313, "dur": 1747291, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1749486031380522, "dur": 14694, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1749486031395235, "dur": 74, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.AnimationModule.dll"}}, {"pid": 12345, "tid": 7, "ts": 1749486031395310, "dur": 421, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Tools\\ScriptUpdater\\ApiUpdater.MovedFromExtractor.exe"}}, {"pid": 12345, "tid": 7, "ts": 1749486031395223, "dur": 509, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AnimationModule.dll_C0FCC60981132629.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1749486031395733, "dur": 74, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1749486031395968, "dur": 73, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1749486031396048, "dur": 62, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.StreamingModule.dll"}}, {"pid": 12345, "tid": 7, "ts": 1749486031396047, "dur": 64, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.StreamingModule.dll_7591B359D09ADD67.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1749486031396154, "dur": 105, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.UmbraModule.dll"}}, {"pid": 12345, "tid": 7, "ts": 1749486031396153, "dur": 118, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UmbraModule.dll_0E723EAE4164004E.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1749486031396272, "dur": 257, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1749486031396537, "dur": 338, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEditor.SceneTemplateModule.dll"}}, {"pid": 12345, "tid": 7, "ts": 1749486031396536, "dur": 341, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.SceneTemplateModule.dll_64C5CD55CF2E68A6.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1749486031396877, "dur": 338, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1749486031397229, "dur": 79, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1749486031397337, "dur": 67, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1749486031397409, "dur": 65, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1749486031397519, "dur": 63, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1749486031397585, "dur": 150, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1749486031397755, "dur": 111, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1749486031397911, "dur": 81, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1749486031398003, "dur": 345, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1749486031398351, "dur": 53, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/10678863128556690338.rsp"}}, {"pid": 12345, "tid": 7, "ts": 1749486031398404, "dur": 61, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1749486031398627, "dur": 76, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1749486031398704, "dur": 55, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/14814235663552238418.rsp"}}, {"pid": 12345, "tid": 7, "ts": 1749486031398760, "dur": 1909, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1749486031400669, "dur": 2083, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1749486031402753, "dur": 1571, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1749486031404324, "dur": 1428, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1749486031405753, "dur": 1372, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1749486031407125, "dur": 2371, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1749486031409496, "dur": 2170, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1749486031411666, "dur": 1960, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1749486031413627, "dur": 1715, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1749486031415343, "dur": 1223, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1749486031416566, "dur": 1133, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1749486031417700, "dur": 2092, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1749486031419792, "dur": 1956, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1749486031421749, "dur": 1396, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1749486031423146, "dur": 1292, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1749486031424439, "dur": 979, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1749486031425418, "dur": 503, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1749486031425922, "dur": 197, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.2D.Common.Path.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1749486031426119, "dur": 98, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1749486031426221, "dur": 584, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.2D.Common.Path.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1749486031426806, "dur": 157, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1749486031427014, "dur": 180, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1749486031427201, "dur": 135, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1749486031427341, "dur": 251, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1749486031427592, "dur": 363, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1749486031427955, "dur": 249, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1749486031428204, "dur": 139, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.Collections.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1749486031428392, "dur": 628, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Collections.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1749486031429021, "dur": 191, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1749486031429221, "dur": 54, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1749486031429297, "dur": 143, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.2D.Animation.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1749486031429441, "dur": 66, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1749486031429511, "dur": 532, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.2D.Animation.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1749486031430043, "dur": 226, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1749486031430279, "dur": 62, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1749486031430347, "dur": 60640, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1749486031490988, "dur": 2529, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Collections.dll (+pdb)"}}, {"pid": 12345, "tid": 7, "ts": 1749486031493518, "dur": 139, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1749486031496419, "dur": 60, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Runtime.Handles.dll"}}, {"pid": 12345, "tid": 7, "ts": 1749486031493664, "dur": 3031, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.2D.Common.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 7, "ts": 1749486031496696, "dur": 159, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1749486031497556, "dur": 142, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.IO.FileSystem.dll"}}, {"pid": 12345, "tid": 7, "ts": 1749486031498691, "dur": 97, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\System.Diagnostics.Tools.dll"}}, {"pid": 12345, "tid": 7, "ts": 1749486031496867, "dur": 3093, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Collections.DocCodeSamples.dll (+pdb)"}}, {"pid": 12345, "tid": 7, "ts": 1749486031499960, "dur": 2069, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1749486031502081, "dur": 715, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1749486031502798, "dur": 197028, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1749486031699828, "dur": 735425, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1900b0aE.dag\\Assembly-CSharp.dll"}}, {"pid": 12345, "tid": 7, "ts": 1749486031699828, "dur": 736540, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Assembly-CSharp.dll (+pdb)"}}, {"pid": 12345, "tid": 7, "ts": 1749486032437453, "dur": 126, "ph": "X", "name": "StoreTimestampsOfNonGeneratedInputFiles", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1749486032437752, "dur": 73584, "ph": "X", "name": "ILPostProcess", "args": {"detail": "Library/Bee/artifacts/1900b0aE.dag/post-processed/Assembly-CSharp.dll (+pdb)"}}, {"pid": 12345, "tid": 7, "ts": 1749486032526701, "dur": 722083, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1900b0aE.dag\\post-processed\\Assembly-CSharp.dll"}}, {"pid": 12345, "tid": 7, "ts": 1749486032526701, "dur": 722089, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Assembly-CSharp.dll"}}, {"pid": 12345, "tid": 7, "ts": 1749486033248819, "dur": 1664, "ph": "X", "name": "CopyFiles", "args": {"detail": "Library/ScriptAssemblies/Assembly-CSharp.dll"}}, {"pid": 12345, "tid": 8, "ts": 1749486031380553, "dur": 14681, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1749486031395249, "dur": 63, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.AssetBundleModule.dll"}}, {"pid": 12345, "tid": 8, "ts": 1749486031395313, "dur": 620, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Tools\\ScriptUpdater\\ApiUpdater.MovedFromExtractor.exe"}}, {"pid": 12345, "tid": 8, "ts": 1749486031395244, "dur": 690, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AssetBundleModule.dll_DE98305BD6D68C77.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1749486031395934, "dur": 127, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1749486031396070, "dur": 139, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.SubsystemsModule.dll"}}, {"pid": 12345, "tid": 8, "ts": 1749486031396069, "dur": 142, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.SubsystemsModule.dll_D0034867E905C483.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1749486031396211, "dur": 315, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1749486031396533, "dur": 390, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEditor.SceneViewModule.dll"}}, {"pid": 12345, "tid": 8, "ts": 1749486031396532, "dur": 406, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.SceneViewModule.dll_31272CE89BD91E91.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1749486031396999, "dur": 222, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1749486031397323, "dur": 235, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1749486031397567, "dur": 81, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1749486031397650, "dur": 61, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1749486031397790, "dur": 163, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1749486031397959, "dur": 172, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1749486031398159, "dur": 147, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1749486031398312, "dur": 54, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp-Editor.rsp"}}, {"pid": 12345, "tid": 8, "ts": 1749486031398367, "dur": 68, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1749486031398465, "dur": 96, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1749486031398607, "dur": 74, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1749486031398690, "dur": 2121, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1749486031400812, "dur": 1775, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1749486031402588, "dur": 2262, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1749486031404850, "dur": 1471, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1749486031406322, "dur": 2160, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1749486031408482, "dur": 1440, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1749486031409922, "dur": 1315, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1749486031411238, "dur": 2227, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1749486031413465, "dur": 1884, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1749486031415350, "dur": 1997, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1749486031417347, "dur": 1604, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1749486031418952, "dur": 2048, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1749486031421001, "dur": 1999, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1749486031423000, "dur": 1620, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1749486031424621, "dur": 802, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1749486031425428, "dur": 489, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1749486031425918, "dur": 153, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.2D.Sprite.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1749486031426072, "dur": 71, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1749486031426148, "dur": 474, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.2D.Sprite.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": 1749486031426622, "dur": 594, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1749486031427229, "dur": 61, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1749486031427300, "dur": 122, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.InternalAPIEditorBridge.001.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1749486031427423, "dur": 203, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1749486031427631, "dur": 512, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.InternalAPIEditorBridge.001.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": 1749486031428144, "dur": 645, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1749486031428799, "dur": 233, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1749486031429037, "dur": 264, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1749486031429301, "dur": 1118, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1749486031430420, "dur": 68, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.2D.SpriteShape.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1749486031430530, "dur": 247, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.2D.SpriteShape.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": 1749486031430778, "dur": 77, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1749486031430891, "dur": 60093, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1749486031490985, "dur": 2615, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.VisualScripting.Core.dll (+pdb)"}}, {"pid": 12345, "tid": 8, "ts": 1749486031493601, "dur": 95, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1749486031493711, "dur": 2153, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.ShaderGraph.ShaderGraphLibrary.dll (+pdb)"}}, {"pid": 12345, "tid": 8, "ts": 1749486031495865, "dur": 2580, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1749486031498693, "dur": 51, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.ComponentModel.dll"}}, {"pid": 12345, "tid": 8, "ts": 1749486031498878, "dur": 91, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.IO.MemoryMappedFiles.dll"}}, {"pid": 12345, "tid": 8, "ts": 1749486031500131, "dur": 50, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\Microsoft.Extensions.Logging.Console.dll"}}, {"pid": 12345, "tid": 8, "ts": 1749486031500391, "dur": 51, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\System.IO.Compression.ZipFile.dll"}}, {"pid": 12345, "tid": 8, "ts": 1749486031498458, "dur": 3011, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.2D.Psdimporter.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 8, "ts": 1749486031501470, "dur": 723, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1749486031502381, "dur": 56, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1749486031502553, "dur": 263, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1749486031502819, "dur": 1747745, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1749486031380573, "dur": 14726, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1749486031395318, "dur": 66, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.AudioModule.dll"}}, {"pid": 12345, "tid": 9, "ts": 1749486031395385, "dur": 413, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Tools\\ScriptUpdater\\ApiUpdater.MovedFromExtractor.exe"}}, {"pid": 12345, "tid": 9, "ts": 1749486031395309, "dur": 490, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AudioModule.dll_939D9295B43D54DB.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1749486031396031, "dur": 74, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1749486031396161, "dur": 219, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1749486031396499, "dur": 270, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEditor.TextCoreFontEngineModule.dll"}}, {"pid": 12345, "tid": 9, "ts": 1749486031396498, "dur": 272, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.TextCoreFontEngineModule.dll_F72D29FA518CE2F1.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1749486031396771, "dur": 548, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1749486031397334, "dur": 83, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/UnityEngine.TestRunner.dll.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1749486031397418, "dur": 93, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1749486031397630, "dur": 256, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netfx\\System.ComponentModel.Composition.dll"}}, {"pid": 12345, "tid": 9, "ts": 1749486031397889, "dur": 58, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netfx\\System.Data.dll"}}, {"pid": 12345, "tid": 9, "ts": 1749486031397950, "dur": 86, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netfx\\System.Drawing.dll"}}, {"pid": 12345, "tid": 9, "ts": 1749486031398038, "dur": 83, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netfx\\System.IO.Compression.FileSystem.dll"}}, {"pid": 12345, "tid": 9, "ts": 1749486031398123, "dur": 77, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netfx\\System.Net.dll"}}, {"pid": 12345, "tid": 9, "ts": 1749486031398233, "dur": 222, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netfx\\System.Runtime.Serialization.dll"}}, {"pid": 12345, "tid": 9, "ts": 1749486031398456, "dur": 73, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netfx\\System.ServiceModel.Web.dll"}}, {"pid": 12345, "tid": 9, "ts": 1749486031398530, "dur": 119, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netfx\\System.Transactions.dll"}}, {"pid": 12345, "tid": 9, "ts": 1749486031398650, "dur": 83, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netfx\\System.Web.dll"}}, {"pid": 12345, "tid": 9, "ts": 1749486031398734, "dur": 111, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netfx\\System.Windows.dll"}}, {"pid": 12345, "tid": 9, "ts": 1749486031398847, "dur": 51, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netfx\\System.Xml.dll"}}, {"pid": 12345, "tid": 9, "ts": 1749486031398899, "dur": 102, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netfx\\System.Xml.Linq.dll"}}, {"pid": 12345, "tid": 9, "ts": 1749486031399003, "dur": 249, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netfx\\System.Xml.Serialization.dll"}}, {"pid": 12345, "tid": 9, "ts": 1749486031399254, "dur": 158, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\Microsoft.Win32.Primitives.dll"}}, {"pid": 12345, "tid": 9, "ts": 1749486031399413, "dur": 347, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.AppContext.dll"}}, {"pid": 12345, "tid": 9, "ts": 1749486031399764, "dur": 90, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Collections.NonGeneric.dll"}}, {"pid": 12345, "tid": 9, "ts": 1749486031399855, "dur": 68, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Collections.Specialized.dll"}}, {"pid": 12345, "tid": 9, "ts": 1749486031399924, "dur": 209, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.ComponentModel.dll"}}, {"pid": 12345, "tid": 9, "ts": 1749486031400135, "dur": 110, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.ComponentModel.EventBasedAsync.dll"}}, {"pid": 12345, "tid": 9, "ts": 1749486031400246, "dur": 201, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.ComponentModel.Primitives.dll"}}, {"pid": 12345, "tid": 9, "ts": 1749486031400450, "dur": 230, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Console.dll"}}, {"pid": 12345, "tid": 9, "ts": 1749486031400686, "dur": 55, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Diagnostics.Contracts.dll"}}, {"pid": 12345, "tid": 9, "ts": 1749486031400742, "dur": 123, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Diagnostics.Debug.dll"}}, {"pid": 12345, "tid": 9, "ts": 1749486031400867, "dur": 476, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Diagnostics.FileVersionInfo.dll"}}, {"pid": 12345, "tid": 9, "ts": 1749486031401351, "dur": 79, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Diagnostics.TraceSource.dll"}}, {"pid": 12345, "tid": 9, "ts": 1749486031401431, "dur": 208, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Diagnostics.Tracing.dll"}}, {"pid": 12345, "tid": 9, "ts": 1749486031401640, "dur": 96, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Drawing.Primitives.dll"}}, {"pid": 12345, "tid": 9, "ts": 1749486031401737, "dur": 80, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Dynamic.Runtime.dll"}}, {"pid": 12345, "tid": 9, "ts": 1749486031401818, "dur": 76, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Globalization.Calendars.dll"}}, {"pid": 12345, "tid": 9, "ts": 1749486031401895, "dur": 73, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Globalization.dll"}}, {"pid": 12345, "tid": 9, "ts": 1749486031401969, "dur": 68, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Globalization.Extensions.dll"}}, {"pid": 12345, "tid": 9, "ts": 1749486031402038, "dur": 144, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.IO.Compression.dll"}}, {"pid": 12345, "tid": 9, "ts": 1749486031402183, "dur": 215, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.IO.Compression.ZipFile.dll"}}, {"pid": 12345, "tid": 9, "ts": 1749486031402399, "dur": 65, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.IO.dll"}}, {"pid": 12345, "tid": 9, "ts": 1749486031402465, "dur": 232, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.IO.FileSystem.dll"}}, {"pid": 12345, "tid": 9, "ts": 1749486031402738, "dur": 92, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.IO.FileSystem.Watcher.dll"}}, {"pid": 12345, "tid": 9, "ts": 1749486031402832, "dur": 109, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.IO.IsolatedStorage.dll"}}, {"pid": 12345, "tid": 9, "ts": 1749486031402942, "dur": 154, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.IO.MemoryMappedFiles.dll"}}, {"pid": 12345, "tid": 9, "ts": 1749486031403098, "dur": 150, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.IO.Pipes.dll"}}, {"pid": 12345, "tid": 9, "ts": 1749486031403250, "dur": 120, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.IO.UnmanagedMemoryStream.dll"}}, {"pid": 12345, "tid": 9, "ts": 1749486031403372, "dur": 77, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Linq.dll"}}, {"pid": 12345, "tid": 9, "ts": 1749486031403450, "dur": 70, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Linq.Expressions.dll"}}, {"pid": 12345, "tid": 9, "ts": 1749486031403521, "dur": 230, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Linq.Parallel.dll"}}, {"pid": 12345, "tid": 9, "ts": 1749486031403754, "dur": 339, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Memory.dll"}}, {"pid": 12345, "tid": 9, "ts": 1749486031404100, "dur": 59, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Net.Security.dll"}}, {"pid": 12345, "tid": 9, "ts": 1749486031404190, "dur": 114, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Net.WebHeaderCollection.dll"}}, {"pid": 12345, "tid": 9, "ts": 1749486031404305, "dur": 182, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Net.WebSockets.Client.dll"}}, {"pid": 12345, "tid": 9, "ts": 1749486031404488, "dur": 109, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Net.WebSockets.dll"}}, {"pid": 12345, "tid": 9, "ts": 1749486031404602, "dur": 147, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Numerics.Vectors.dll"}}, {"pid": 12345, "tid": 9, "ts": 1749486031404750, "dur": 69, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.ObjectModel.dll"}}, {"pid": 12345, "tid": 9, "ts": 1749486031404821, "dur": 122, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Reflection.DispatchProxy.dll"}}, {"pid": 12345, "tid": 9, "ts": 1749486031404944, "dur": 64, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Reflection.dll"}}, {"pid": 12345, "tid": 9, "ts": 1749486031405010, "dur": 217, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Reflection.Emit.dll"}}, {"pid": 12345, "tid": 9, "ts": 1749486031405228, "dur": 110, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Reflection.Emit.ILGeneration.dll"}}, {"pid": 12345, "tid": 9, "ts": 1749486031405339, "dur": 174, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Reflection.Emit.Lightweight.dll"}}, {"pid": 12345, "tid": 9, "ts": 1749486031405514, "dur": 254, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Reflection.Extensions.dll"}}, {"pid": 12345, "tid": 9, "ts": 1749486031405769, "dur": 80, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Reflection.Primitives.dll"}}, {"pid": 12345, "tid": 9, "ts": 1749486031405851, "dur": 72, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Resources.Reader.dll"}}, {"pid": 12345, "tid": 9, "ts": 1749486031405924, "dur": 134, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Resources.ResourceManager.dll"}}, {"pid": 12345, "tid": 9, "ts": 1749486031406059, "dur": 101, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Resources.Writer.dll"}}, {"pid": 12345, "tid": 9, "ts": 1749486031406162, "dur": 98, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Runtime.CompilerServices.VisualC.dll"}}, {"pid": 12345, "tid": 9, "ts": 1749486031406261, "dur": 94, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Runtime.dll"}}, {"pid": 12345, "tid": 9, "ts": 1749486031406357, "dur": 116, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Runtime.Extensions.dll"}}, {"pid": 12345, "tid": 9, "ts": 1749486031406475, "dur": 53, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Runtime.Handles.dll"}}, {"pid": 12345, "tid": 9, "ts": 1749486031406529, "dur": 59, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Runtime.InteropServices.dll"}}, {"pid": 12345, "tid": 9, "ts": 1749486031406589, "dur": 53, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Runtime.InteropServices.RuntimeInformation.dll"}}, {"pid": 12345, "tid": 9, "ts": 1749486031406643, "dur": 122, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Runtime.Numerics.dll"}}, {"pid": 12345, "tid": 9, "ts": 1749486031406766, "dur": 89, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Runtime.Serialization.Formatters.dll"}}, {"pid": 12345, "tid": 9, "ts": 1749486031406856, "dur": 133, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Runtime.Serialization.Json.dll"}}, {"pid": 12345, "tid": 9, "ts": 1749486031406991, "dur": 85, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Runtime.Serialization.Primitives.dll"}}, {"pid": 12345, "tid": 9, "ts": 1749486031407077, "dur": 175, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Runtime.Serialization.Xml.dll"}}, {"pid": 12345, "tid": 9, "ts": 1749486031407253, "dur": 57, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Security.Claims.dll"}}, {"pid": 12345, "tid": 9, "ts": 1749486031407357, "dur": 77, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Security.Cryptography.Csp.dll"}}, {"pid": 12345, "tid": 9, "ts": 1749486031407438, "dur": 119, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Security.Cryptography.Encoding.dll"}}, {"pid": 12345, "tid": 9, "ts": 1749486031407558, "dur": 552, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Security.Cryptography.Primitives.dll"}}, {"pid": 12345, "tid": 9, "ts": 1749486031408111, "dur": 57, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Security.Cryptography.X509Certificates.dll"}}, {"pid": 12345, "tid": 9, "ts": 1749486031408170, "dur": 118, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Security.Principal.dll"}}, {"pid": 12345, "tid": 9, "ts": 1749486031408289, "dur": 104, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Security.SecureString.dll"}}, {"pid": 12345, "tid": 9, "ts": 1749486031408420, "dur": 209, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Text.Encoding.Extensions.dll"}}, {"pid": 12345, "tid": 9, "ts": 1749486031408635, "dur": 203, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Threading.Tasks.dll"}}, {"pid": 12345, "tid": 9, "ts": 1749486031408840, "dur": 140, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Threading.Tasks.Parallel.dll"}}, {"pid": 12345, "tid": 9, "ts": 1749486031408981, "dur": 220, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Threading.Thread.dll"}}, {"pid": 12345, "tid": 9, "ts": 1749486031409230, "dur": 90, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.ValueTuple.dll"}}, {"pid": 12345, "tid": 9, "ts": 1749486031409321, "dur": 134, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Xml.ReaderWriter.dll"}}, {"pid": 12345, "tid": 9, "ts": 1749486031409548, "dur": 88, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Xml.XmlSerializer.dll"}}, {"pid": 12345, "tid": 9, "ts": 1749486031409637, "dur": 94, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Xml.XPath.dll"}}, {"pid": 12345, "tid": 9, "ts": 1749486031409733, "dur": 97, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Xml.XPath.XDocument.dll"}}, {"pid": 12345, "tid": 9, "ts": 1749486031409832, "dur": 90, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\NetStandard\\Extensions\\2.0.0\\System.Runtime.InteropServices.WindowsRuntime.dll"}}, {"pid": 12345, "tid": 9, "ts": 1749486031409923, "dur": 103, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\NetStandard\\ref\\2.1.0\\netstandard.dll"}}, {"pid": 12345, "tid": 9, "ts": 1749486031410030, "dur": 115, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.ext.nunit@1.0.6\\net35\\unity-custom\\nunit.framework.dll"}}, {"pid": 12345, "tid": 9, "ts": 1749486031410148, "dur": 67, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.test-framework@1.1.33\\UnityEngine.TestRunner\\AssemblyInfo.cs"}}, {"pid": 12345, "tid": 9, "ts": 1749486031410216, "dur": 77, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.test-framework@1.1.33\\UnityEngine.TestRunner\\Assertions\\AllocatingGCMemoryConstraint.cs"}}, {"pid": 12345, "tid": 9, "ts": 1749486031410294, "dur": 62, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.test-framework@1.1.33\\UnityEngine.TestRunner\\Assertions\\ConstraintsExtensions.cs"}}, {"pid": 12345, "tid": 9, "ts": 1749486031410357, "dur": 51, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.test-framework@1.1.33\\UnityEngine.TestRunner\\Assertions\\InvalidSignatureException.cs"}}, {"pid": 12345, "tid": 9, "ts": 1749486031410408, "dur": 157, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.test-framework@1.1.33\\UnityEngine.TestRunner\\Assertions\\Is.cs"}}, {"pid": 12345, "tid": 9, "ts": 1749486031410566, "dur": 84, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.test-framework@1.1.33\\UnityEngine.TestRunner\\Assertions\\LogAssert.cs"}}, {"pid": 12345, "tid": 9, "ts": 1749486031410651, "dur": 66, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.test-framework@1.1.33\\UnityEngine.TestRunner\\Assertions\\LogScope\\ILogScope.cs"}}, {"pid": 12345, "tid": 9, "ts": 1749486031410718, "dur": 55, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.test-framework@1.1.33\\UnityEngine.TestRunner\\Assertions\\LogScope\\LogEvent.cs"}}, {"pid": 12345, "tid": 9, "ts": 1749486031410774, "dur": 52, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.test-framework@1.1.33\\UnityEngine.TestRunner\\Assertions\\LogScope\\LogMatch.cs"}}, {"pid": 12345, "tid": 9, "ts": 1749486031410827, "dur": 167, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.test-framework@1.1.33\\UnityEngine.TestRunner\\Assertions\\LogScope\\LogScope.cs"}}, {"pid": 12345, "tid": 9, "ts": 1749486031410995, "dur": 80, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.test-framework@1.1.33\\UnityEngine.TestRunner\\Assertions\\OutOfOrderExpectedLogMessageException.cs"}}, {"pid": 12345, "tid": 9, "ts": 1749486031411076, "dur": 124, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.test-framework@1.1.33\\UnityEngine.TestRunner\\Assertions\\UnexpectedLogMessageException.cs"}}, {"pid": 12345, "tid": 9, "ts": 1749486031411205, "dur": 77, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.test-framework@1.1.33\\UnityEngine.TestRunner\\Assertions\\UnhandledLogMessageException.cs"}}, {"pid": 12345, "tid": 9, "ts": 1749486031411283, "dur": 71, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.test-framework@1.1.33\\UnityEngine.TestRunner\\Assertions\\UnityTestTimeoutException.cs"}}, {"pid": 12345, "tid": 9, "ts": 1749486031411355, "dur": 71, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.test-framework@1.1.33\\UnityEngine.TestRunner\\NUnitExtensions\\ActionDelegator.cs"}}, {"pid": 12345, "tid": 9, "ts": 1749486031411427, "dur": 85, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.test-framework@1.1.33\\UnityEngine.TestRunner\\NUnitExtensions\\Attributes\\ConditionalIgnoreAttribute.cs"}}, {"pid": 12345, "tid": 9, "ts": 1749486031411513, "dur": 88, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.test-framework@1.1.33\\UnityEngine.TestRunner\\NUnitExtensions\\Attributes\\TestEnumerator.cs"}}, {"pid": 12345, "tid": 9, "ts": 1749486031411602, "dur": 85, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.test-framework@1.1.33\\UnityEngine.TestRunner\\NUnitExtensions\\Attributes\\TestMustExpectAllLogsAttribute.cs"}}, {"pid": 12345, "tid": 9, "ts": 1749486031411688, "dur": 85, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.test-framework@1.1.33\\UnityEngine.TestRunner\\NUnitExtensions\\Attributes\\UnityCombinatorialStrategy.cs"}}, {"pid": 12345, "tid": 9, "ts": 1749486031411774, "dur": 183, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.test-framework@1.1.33\\UnityEngine.TestRunner\\NUnitExtensions\\Attributes\\UnityPlatformAttribute.cs"}}, {"pid": 12345, "tid": 9, "ts": 1749486031411960, "dur": 73, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.test-framework@1.1.33\\UnityEngine.TestRunner\\NUnitExtensions\\Attributes\\UnitySetUpAttribute.cs"}}, {"pid": 12345, "tid": 9, "ts": 1749486031412034, "dur": 93, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.test-framework@1.1.33\\UnityEngine.TestRunner\\NUnitExtensions\\Attributes\\UnityTearDownAttribute.cs"}}, {"pid": 12345, "tid": 9, "ts": 1749486031412128, "dur": 229, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.test-framework@1.1.33\\UnityEngine.TestRunner\\NUnitExtensions\\Attributes\\UnityTestAttribute.cs"}}, {"pid": 12345, "tid": 9, "ts": 1749486031412358, "dur": 111, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.test-framework@1.1.33\\UnityEngine.TestRunner\\NUnitExtensions\\BaseDelegator.cs"}}, {"pid": 12345, "tid": 9, "ts": 1749486031412651, "dur": 56, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.test-framework@1.1.33\\UnityEngine.TestRunner\\NUnitExtensions\\Commands\\EnumerableRetryTestCommand.cs"}}, {"pid": 12345, "tid": 9, "ts": 1749486031412707, "dur": 102, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.test-framework@1.1.33\\UnityEngine.TestRunner\\NUnitExtensions\\Commands\\EnumerableSetUpTearDownCommand.cs"}}, {"pid": 12345, "tid": 9, "ts": 1749486031412992, "dur": 52, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.test-framework@1.1.33\\UnityEngine.TestRunner\\NUnitExtensions\\Commands\\SetUpTearDownCommand.cs"}}, {"pid": 12345, "tid": 9, "ts": 1749486031413044, "dur": 51, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.test-framework@1.1.33\\UnityEngine.TestRunner\\NUnitExtensions\\Commands\\TestActionCommand.cs"}}, {"pid": 12345, "tid": 9, "ts": 1749486031413096, "dur": 51, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.test-framework@1.1.33\\UnityEngine.TestRunner\\NUnitExtensions\\Commands\\TestCommandPcHelper.cs"}}, {"pid": 12345, "tid": 9, "ts": 1749486031413148, "dur": 54, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.test-framework@1.1.33\\UnityEngine.TestRunner\\NUnitExtensions\\Commands\\UnityTestMethodCommand.cs"}}, {"pid": 12345, "tid": 9, "ts": 1749486031413203, "dur": 97, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.test-framework@1.1.33\\UnityEngine.TestRunner\\NUnitExtensions\\ConstructDelegator.cs"}}, {"pid": 12345, "tid": 9, "ts": 1749486031413302, "dur": 134, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.test-framework@1.1.33\\UnityEngine.TestRunner\\NUnitExtensions\\Filters\\AssemblyNameFilter.cs"}}, {"pid": 12345, "tid": 9, "ts": 1749486031413437, "dur": 78, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.test-framework@1.1.33\\UnityEngine.TestRunner\\NUnitExtensions\\Filters\\CategoryFilterExtended.cs"}}, {"pid": 12345, "tid": 9, "ts": 1749486031413516, "dur": 56, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.test-framework@1.1.33\\UnityEngine.TestRunner\\NUnitExtensions\\Filters\\FullNameFilter.cs"}}, {"pid": 12345, "tid": 9, "ts": 1749486031413573, "dur": 88, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.test-framework@1.1.33\\UnityEngine.TestRunner\\NUnitExtensions\\IAsyncTestAssemblyBuilder.cs"}}, {"pid": 12345, "tid": 9, "ts": 1749486031413662, "dur": 100, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.test-framework@1.1.33\\UnityEngine.TestRunner\\NUnitExtensions\\IStateSerializer.cs"}}, {"pid": 12345, "tid": 9, "ts": 1749486031413763, "dur": 78, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.test-framework@1.1.33\\UnityEngine.TestRunner\\NUnitExtensions\\ITestSuiteModifier.cs"}}, {"pid": 12345, "tid": 9, "ts": 1749486031413842, "dur": 90, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.test-framework@1.1.33\\UnityEngine.TestRunner\\NUnitExtensions\\OrderedTestSuiteModifier.cs"}}, {"pid": 12345, "tid": 9, "ts": 1749486031413933, "dur": 76, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.test-framework@1.1.33\\UnityEngine.TestRunner\\NUnitExtensions\\Runner\\CompositeWorkItem.cs"}}, {"pid": 12345, "tid": 9, "ts": 1749486031414012, "dur": 83, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.test-framework@1.1.33\\UnityEngine.TestRunner\\NUnitExtensions\\Runner\\CoroutineTestWorkItem.cs"}}, {"pid": 12345, "tid": 9, "ts": 1749486031414096, "dur": 96, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.test-framework@1.1.33\\UnityEngine.TestRunner\\NUnitExtensions\\Runner\\DefaultTestWorkItem.cs"}}, {"pid": 12345, "tid": 9, "ts": 1749486031414193, "dur": 60, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.test-framework@1.1.33\\UnityEngine.TestRunner\\NUnitExtensions\\Runner\\FailCommand.cs"}}, {"pid": 12345, "tid": 9, "ts": 1749486031414254, "dur": 57, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.test-framework@1.1.33\\UnityEngine.TestRunner\\NUnitExtensions\\Runner\\IEnumerableTestMethodCommand.cs"}}, {"pid": 12345, "tid": 9, "ts": 1749486031414312, "dur": 90, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.test-framework@1.1.33\\UnityEngine.TestRunner\\NUnitExtensions\\Runner\\PlaymodeWorkItemFactory.cs"}}, {"pid": 12345, "tid": 9, "ts": 1749486031414406, "dur": 80, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.test-framework@1.1.33\\UnityEngine.TestRunner\\NUnitExtensions\\Runner\\RestoreTestContextAfterDomainReload.cs"}}, {"pid": 12345, "tid": 9, "ts": 1749486031414487, "dur": 98, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.test-framework@1.1.33\\UnityEngine.TestRunner\\NUnitExtensions\\Runner\\TestCommandBuilder.cs"}}, {"pid": 12345, "tid": 9, "ts": 1749486031414586, "dur": 92, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.test-framework@1.1.33\\UnityEngine.TestRunner\\NUnitExtensions\\Runner\\UnityLogCheckDelegatingCommand.cs"}}, {"pid": 12345, "tid": 9, "ts": 1749486031414679, "dur": 122, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.test-framework@1.1.33\\UnityEngine.TestRunner\\NUnitExtensions\\Runner\\UnityTestAssemblyRunner.cs"}}, {"pid": 12345, "tid": 9, "ts": 1749486031414802, "dur": 63, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.test-framework@1.1.33\\UnityEngine.TestRunner\\NUnitExtensions\\Runner\\UnityTestExecutionContext.cs"}}, {"pid": 12345, "tid": 9, "ts": 1749486031414865, "dur": 62, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.test-framework@1.1.33\\UnityEngine.TestRunner\\NUnitExtensions\\Runner\\UnityWorkItem.cs"}}, {"pid": 12345, "tid": 9, "ts": 1749486031414929, "dur": 51, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.test-framework@1.1.33\\UnityEngine.TestRunner\\NUnitExtensions\\Runner\\UnityWorkItemDataHolder.cs"}}, {"pid": 12345, "tid": 9, "ts": 1749486031414981, "dur": 173, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.test-framework@1.1.33\\UnityEngine.TestRunner\\NUnitExtensions\\Runner\\WorkItemFactory.cs"}}, {"pid": 12345, "tid": 9, "ts": 1749486031415155, "dur": 62, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.test-framework@1.1.33\\UnityEngine.TestRunner\\NUnitExtensions\\TestExtensions.cs"}}, {"pid": 12345, "tid": 9, "ts": 1749486031415265, "dur": 56, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.test-framework@1.1.33\\UnityEngine.TestRunner\\NUnitExtensions\\UnityTestAssemblyBuilder.cs"}}, {"pid": 12345, "tid": 9, "ts": 1749486031415322, "dur": 62, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.test-framework@1.1.33\\UnityEngine.TestRunner\\TestRunner\\Callbacks\\PlayerQuitHandler.cs"}}, {"pid": 12345, "tid": 9, "ts": 1749486031415384, "dur": 55, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.test-framework@1.1.33\\UnityEngine.TestRunner\\TestRunner\\Callbacks\\PlayModeRunnerCallback.cs"}}, {"pid": 12345, "tid": 9, "ts": 1749486031415440, "dur": 57, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.test-framework@1.1.33\\UnityEngine.TestRunner\\TestRunner\\Callbacks\\RemoteTestResultSender.cs"}}, {"pid": 12345, "tid": 9, "ts": 1749486031415498, "dur": 150, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.test-framework@1.1.33\\UnityEngine.TestRunner\\TestRunner\\Callbacks\\TestResultRenderer.cs"}}, {"pid": 12345, "tid": 9, "ts": 1749486031415649, "dur": 72, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.test-framework@1.1.33\\UnityEngine.TestRunner\\TestRunner\\Callbacks\\TestResultRendererCallback.cs"}}, {"pid": 12345, "tid": 9, "ts": 1749486031415772, "dur": 93, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.test-framework@1.1.33\\UnityEngine.TestRunner\\TestRunner\\Messages\\IEditModeTestYieldInstruction.cs"}}, {"pid": 12345, "tid": 9, "ts": 1749486031415867, "dur": 134, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.test-framework@1.1.33\\UnityEngine.TestRunner\\TestRunner\\PlaymodeTestsController.cs"}}, {"pid": 12345, "tid": 9, "ts": 1749486031416002, "dur": 86, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.test-framework@1.1.33\\UnityEngine.TestRunner\\TestRunner\\PlaymodeTestsControllerSettings.cs"}}, {"pid": 12345, "tid": 9, "ts": 1749486031416089, "dur": 59, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.test-framework@1.1.33\\UnityEngine.TestRunner\\TestRunner\\RemoteHelpers\\IRemoteTestResultDataFactory.cs"}}, {"pid": 12345, "tid": 9, "ts": 1749486031416149, "dur": 56, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.test-framework@1.1.33\\UnityEngine.TestRunner\\TestRunner\\RemoteHelpers\\PlayerConnectionMessageIds.cs"}}, {"pid": 12345, "tid": 9, "ts": 1749486031416206, "dur": 50, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.test-framework@1.1.33\\UnityEngine.TestRunner\\TestRunner\\RemoteHelpers\\RemoteTestData.cs"}}, {"pid": 12345, "tid": 9, "ts": 1749486031416257, "dur": 64, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.test-framework@1.1.33\\UnityEngine.TestRunner\\TestRunner\\RemoteHelpers\\RemoteTestResultData.cs"}}, {"pid": 12345, "tid": 9, "ts": 1749486031416322, "dur": 204, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.test-framework@1.1.33\\UnityEngine.TestRunner\\TestRunner\\RemoteHelpers\\RemoteTestResultDataFactory.cs"}}, {"pid": 12345, "tid": 9, "ts": 1749486031416530, "dur": 127, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.test-framework@1.1.33\\UnityEngine.TestRunner\\TestRunner\\RemoteHelpers\\RemoteTestResultDataWithTestData.cs"}}, {"pid": 12345, "tid": 9, "ts": 1749486031416658, "dur": 65, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.test-framework@1.1.33\\UnityEngine.TestRunner\\TestRunner\\RuntimeTestRunnerFilter.cs"}}, {"pid": 12345, "tid": 9, "ts": 1749486031416724, "dur": 114, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.test-framework@1.1.33\\UnityEngine.TestRunner\\TestRunner\\SynchronousFilter.cs"}}, {"pid": 12345, "tid": 9, "ts": 1749486031416840, "dur": 172, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.test-framework@1.1.33\\UnityEngine.TestRunner\\TestRunner\\TestEnumeratorWrapper.cs"}}, {"pid": 12345, "tid": 9, "ts": 1749486031417013, "dur": 53, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.test-framework@1.1.33\\UnityEngine.TestRunner\\TestRunner\\TestListenerWrapper.cs"}}, {"pid": 12345, "tid": 9, "ts": 1749486031417067, "dur": 56, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.test-framework@1.1.33\\UnityEngine.TestRunner\\TestRunner\\TestPlatform.cs"}}, {"pid": 12345, "tid": 9, "ts": 1749486031417124, "dur": 139, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.test-framework@1.1.33\\UnityEngine.TestRunner\\Utils\\AssemblyProvider\\AssemblyLoadProxy.cs"}}, {"pid": 12345, "tid": 9, "ts": 1749486031417265, "dur": 80, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.test-framework@1.1.33\\UnityEngine.TestRunner\\Utils\\AssemblyProvider\\AssemblyWrapper.cs"}}, {"pid": 12345, "tid": 9, "ts": 1749486031417346, "dur": 107, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.test-framework@1.1.33\\UnityEngine.TestRunner\\Utils\\AssemblyProvider\\IAssemblyLoadProxy.cs"}}, {"pid": 12345, "tid": 9, "ts": 1749486031417455, "dur": 300, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.test-framework@1.1.33\\UnityEngine.TestRunner\\Utils\\AssemblyProvider\\IAssemblyWrapper.cs"}}, {"pid": 12345, "tid": 9, "ts": 1749486031417756, "dur": 350, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.test-framework@1.1.33\\UnityEngine.TestRunner\\Utils\\AssemblyProvider\\IScriptingRuntimeProxy.cs"}}, {"pid": 12345, "tid": 9, "ts": 1749486031418192, "dur": 179, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.test-framework@1.1.33\\UnityEngine.TestRunner\\Utils\\AttributeHelper.cs"}}, {"pid": 12345, "tid": 9, "ts": 1749486031418372, "dur": 81, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.test-framework@1.1.33\\UnityEngine.TestRunner\\Utils\\ColorEqualityComparer.cs"}}, {"pid": 12345, "tid": 9, "ts": 1749486031418454, "dur": 90, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.test-framework@1.1.33\\UnityEngine.TestRunner\\Utils\\CoroutineRunner.cs"}}, {"pid": 12345, "tid": 9, "ts": 1749486031418545, "dur": 80, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.test-framework@1.1.33\\UnityEngine.TestRunner\\Utils\\FloatEqualityComparer.cs"}}, {"pid": 12345, "tid": 9, "ts": 1749486031418625, "dur": 78, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.test-framework@1.1.33\\UnityEngine.TestRunner\\Utils\\IOuterUnityTestAction.cs"}}, {"pid": 12345, "tid": 9, "ts": 1749486031418704, "dur": 83, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.test-framework@1.1.33\\UnityEngine.TestRunner\\Utils\\IPostBuildCleanup.cs"}}, {"pid": 12345, "tid": 9, "ts": 1749486031418788, "dur": 86, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.test-framework@1.1.33\\UnityEngine.TestRunner\\Utils\\IPrebuildSceneSetup.cs"}}, {"pid": 12345, "tid": 9, "ts": 1749486031418875, "dur": 83, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.test-framework@1.1.33\\UnityEngine.TestRunner\\Utils\\ITestRunCallback.cs"}}, {"pid": 12345, "tid": 9, "ts": 1749486031418959, "dur": 83, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.test-framework@1.1.33\\UnityEngine.TestRunner\\Utils\\MonoBehaviourTest\\IMonoBehaviourTest.cs"}}, {"pid": 12345, "tid": 9, "ts": 1749486031419043, "dur": 113, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.test-framework@1.1.33\\UnityEngine.TestRunner\\Utils\\MonoBehaviourTest\\MonoBehaviourTest.cs"}}, {"pid": 12345, "tid": 9, "ts": 1749486031419157, "dur": 81, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.test-framework@1.1.33\\UnityEngine.TestRunner\\Utils\\PostBuildCleanupAttribute.cs"}}, {"pid": 12345, "tid": 9, "ts": 1749486031419239, "dur": 88, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.test-framework@1.1.33\\UnityEngine.TestRunner\\Utils\\PrebuildSceneSetupAttribute.cs"}}, {"pid": 12345, "tid": 9, "ts": 1749486031419328, "dur": 79, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.test-framework@1.1.33\\UnityEngine.TestRunner\\Utils\\QuaternionEqualityComparer.cs"}}, {"pid": 12345, "tid": 9, "ts": 1749486031419408, "dur": 103, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.test-framework@1.1.33\\UnityEngine.TestRunner\\Utils\\StacktraceFilter.cs"}}, {"pid": 12345, "tid": 9, "ts": 1749486031419512, "dur": 102, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.test-framework@1.1.33\\UnityEngine.TestRunner\\Utils\\TestRunCallbackAttribute.cs"}}, {"pid": 12345, "tid": 9, "ts": 1749486031419615, "dur": 93, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.test-framework@1.1.33\\UnityEngine.TestRunner\\Utils\\TestRunCallbackListener.cs"}}, {"pid": 12345, "tid": 9, "ts": 1749486031419804, "dur": 225, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.test-framework@1.1.33\\UnityEngine.TestRunner\\Utils\\Vector2EqualityComparer.cs"}}, {"pid": 12345, "tid": 9, "ts": 1749486031420030, "dur": 57, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.test-framework@1.1.33\\UnityEngine.TestRunner\\Utils\\Vector3ComparerWithEqualsOperator.cs"}}, {"pid": 12345, "tid": 9, "ts": 1749486031420088, "dur": 50, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.test-framework@1.1.33\\UnityEngine.TestRunner\\Utils\\Vector3EqualityComparer.cs"}}, {"pid": 12345, "tid": 9, "ts": 1749486031420188, "dur": 51, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.test-framework@1.1.33\\UnityEngine.TestRunner\\Utils\\Vector4EqualityComparer.cs"}}, {"pid": 12345, "tid": 9, "ts": 1749486031397530, "dur": 22714, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/UnityEngine.TestRunner.dll (+2 others)"}}, {"pid": 12345, "tid": 9, "ts": 1749486031420244, "dur": 143, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1749486031420406, "dur": 58, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1749486031420472, "dur": 83, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/UnityEditor.TestRunner.dll.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1749486031420555, "dur": 120, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1749486031420852, "dur": 118, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\Microsoft.Win32.Primitives.dll"}}, {"pid": 12345, "tid": 9, "ts": 1749486031420971, "dur": 69, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\netstandard.dll"}}, {"pid": 12345, "tid": 9, "ts": 1749486031421041, "dur": 72, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.AppContext.dll"}}, {"pid": 12345, "tid": 9, "ts": 1749486031421114, "dur": 84, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Buffers.dll"}}, {"pid": 12345, "tid": 9, "ts": 1749486031421199, "dur": 80, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Collections.Concurrent.dll"}}, {"pid": 12345, "tid": 9, "ts": 1749486031421280, "dur": 103, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Collections.dll"}}, {"pid": 12345, "tid": 9, "ts": 1749486031421384, "dur": 65, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Collections.NonGeneric.dll"}}, {"pid": 12345, "tid": 9, "ts": 1749486031421498, "dur": 51, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.ComponentModel.Annotations.dll"}}, {"pid": 12345, "tid": 9, "ts": 1749486031421550, "dur": 50, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.ComponentModel.dll"}}, {"pid": 12345, "tid": 9, "ts": 1749486031421601, "dur": 66, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.ComponentModel.EventBasedAsync.dll"}}, {"pid": 12345, "tid": 9, "ts": 1749486031421716, "dur": 54, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.ComponentModel.TypeConverter.dll"}}, {"pid": 12345, "tid": 9, "ts": 1749486031421816, "dur": 185, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Data.Common.dll"}}, {"pid": 12345, "tid": 9, "ts": 1749486031422003, "dur": 53, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Diagnostics.Contracts.dll"}}, {"pid": 12345, "tid": 9, "ts": 1749486031422107, "dur": 51, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Diagnostics.FileVersionInfo.dll"}}, {"pid": 12345, "tid": 9, "ts": 1749486031422209, "dur": 114, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Diagnostics.StackTrace.dll"}}, {"pid": 12345, "tid": 9, "ts": 1749486031422325, "dur": 78, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Diagnostics.TextWriterTraceListener.dll"}}, {"pid": 12345, "tid": 9, "ts": 1749486031422404, "dur": 63, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Diagnostics.Tools.dll"}}, {"pid": 12345, "tid": 9, "ts": 1749486031422468, "dur": 56, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Diagnostics.TraceSource.dll"}}, {"pid": 12345, "tid": 9, "ts": 1749486031422525, "dur": 53, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Drawing.Primitives.dll"}}, {"pid": 12345, "tid": 9, "ts": 1749486031422579, "dur": 50, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Dynamic.Runtime.dll"}}, {"pid": 12345, "tid": 9, "ts": 1749486031422630, "dur": 50, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Globalization.Calendars.dll"}}, {"pid": 12345, "tid": 9, "ts": 1749486031422681, "dur": 62, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Globalization.dll"}}, {"pid": 12345, "tid": 9, "ts": 1749486031422744, "dur": 51, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Globalization.Extensions.dll"}}, {"pid": 12345, "tid": 9, "ts": 1749486031422799, "dur": 62, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.IO.Compression.ZipFile.dll"}}, {"pid": 12345, "tid": 9, "ts": 1749486031422898, "dur": 71, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.IO.FileSystem.dll"}}, {"pid": 12345, "tid": 9, "ts": 1749486031422970, "dur": 125, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.IO.FileSystem.DriveInfo.dll"}}, {"pid": 12345, "tid": 9, "ts": 1749486031423096, "dur": 91, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.IO.FileSystem.Primitives.dll"}}, {"pid": 12345, "tid": 9, "ts": 1749486031423189, "dur": 63, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.IO.FileSystem.Watcher.dll"}}, {"pid": 12345, "tid": 9, "ts": 1749486031423253, "dur": 140, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.IO.IsolatedStorage.dll"}}, {"pid": 12345, "tid": 9, "ts": 1749486031423394, "dur": 69, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.IO.MemoryMappedFiles.dll"}}, {"pid": 12345, "tid": 9, "ts": 1749486031423464, "dur": 124, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.IO.Pipes.dll"}}, {"pid": 12345, "tid": 9, "ts": 1749486031423590, "dur": 80, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.IO.UnmanagedMemoryStream.dll"}}, {"pid": 12345, "tid": 9, "ts": 1749486031423671, "dur": 79, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Linq.dll"}}, {"pid": 12345, "tid": 9, "ts": 1749486031423751, "dur": 76, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Linq.Expressions.dll"}}, {"pid": 12345, "tid": 9, "ts": 1749486031423828, "dur": 85, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Linq.Parallel.dll"}}, {"pid": 12345, "tid": 9, "ts": 1749486031423914, "dur": 74, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Linq.Queryable.dll"}}, {"pid": 12345, "tid": 9, "ts": 1749486031423989, "dur": 100, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Memory.dll"}}, {"pid": 12345, "tid": 9, "ts": 1749486031424090, "dur": 108, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Net.Http.Rtc.dll"}}, {"pid": 12345, "tid": 9, "ts": 1749486031424199, "dur": 222, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Net.NameResolution.dll"}}, {"pid": 12345, "tid": 9, "ts": 1749486031424429, "dur": 61, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Net.Security.dll"}}, {"pid": 12345, "tid": 9, "ts": 1749486031424572, "dur": 50, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Net.WebSockets.Client.dll"}}, {"pid": 12345, "tid": 9, "ts": 1749486031424668, "dur": 56, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.ObjectModel.dll"}}, {"pid": 12345, "tid": 9, "ts": 1749486031424999, "dur": 74, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Text.Encoding.Extensions.dll"}}, {"pid": 12345, "tid": 9, "ts": 1749486031420688, "dur": 4584, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/UnityEditor.TestRunner.dll (+2 others)"}}, {"pid": 12345, "tid": 9, "ts": 1749486031425273, "dur": 87, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1749486031425468, "dur": 56, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/UnityEditor.UI.dll.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1749486031425552, "dur": 214, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/UnityEditor.UI.dll (+2 others)"}}, {"pid": 12345, "tid": 9, "ts": 1749486031425766, "dur": 69, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1749486031425922, "dur": 122, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.dll.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1749486031426045, "dur": 819, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1749486031426868, "dur": 455, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.dll (+2 others)"}}, {"pid": 12345, "tid": 9, "ts": 1749486031427324, "dur": 555, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1749486031427942, "dur": 208, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.CodeGen.dll.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1749486031428207, "dur": 541, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.CodeGen.dll (+2 others)"}}, {"pid": 12345, "tid": 9, "ts": 1749486031428749, "dur": 119, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1749486031429317, "dur": 58, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Console.dll"}}, {"pid": 12345, "tid": 9, "ts": 1749486031428883, "dur": 733, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPP-Configuration Library/ilpp-configuration.nevergeneratedoutput"}}, {"pid": 12345, "tid": 9, "ts": 1749486031429616, "dur": 117, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1749486031429738, "dur": 225, "ph": "X", "name": "StoreTimestampsOfNonGeneratedInputFiles", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1749486031430517, "dur": 54795, "ph": "X", "name": "ILPP-Configuration", "args": {"detail": "Library/ilpp-configuration.nevergeneratedoutput"}}, {"pid": 12345, "tid": 9, "ts": 1749486031490955, "dur": 2627, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.2D.IK.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 9, "ts": 1749486031493583, "dur": 153, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1749486031493744, "dur": 2510, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.VisualScripting.Flow.dll (+pdb)"}}, {"pid": 12345, "tid": 9, "ts": 1749486031496255, "dur": 177, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1749486031496438, "dur": 2029, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Collections.BurstCompatibilityGen.dll (+pdb)"}}, {"pid": 12345, "tid": 9, "ts": 1749486031498468, "dur": 88, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1749486031499882, "dur": 88, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\System.Net.WebProxy.dll"}}, {"pid": 12345, "tid": 9, "ts": 1749486031500343, "dur": 50, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.DSPGraphModule.dll"}}, {"pid": 12345, "tid": 9, "ts": 1749486031500753, "dur": 116, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Net.Sockets.dll"}}, {"pid": 12345, "tid": 9, "ts": 1749486031498560, "dur": 2845, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.InternalAPIEditorBridge.001.dll (+pdb)"}}, {"pid": 12345, "tid": 9, "ts": 1749486031501406, "dur": 179, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1749486031501594, "dur": 72, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1749486031501829, "dur": 369, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1749486031502259, "dur": 60, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1749486031502434, "dur": 81, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1749486031502544, "dur": 81, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1749486031502634, "dur": 71, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1749486031502712, "dur": 62, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1749486031502778, "dur": 319, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1749486031503127, "dur": 1747569, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1749486031380600, "dur": 14759, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1749486031395398, "dur": 532, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Tools\\ScriptUpdater\\ApiUpdater.MovedFromExtractor.exe"}}, {"pid": 12345, "tid": 10, "ts": 1749486031395365, "dur": 567, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ContentLoadModule.dll_77AE899CA5593464.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1749486031395933, "dur": 112, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1749486031396054, "dur": 64, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.SubstanceModule.dll"}}, {"pid": 12345, "tid": 10, "ts": 1749486031396053, "dur": 67, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.SubstanceModule.dll_523CC7A31BEDDB75.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1749486031396120, "dur": 80, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1749486031396208, "dur": 83, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.UnityAnalyticsModule.dll"}}, {"pid": 12345, "tid": 10, "ts": 1749486031396206, "dur": 88, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityAnalyticsModule.dll_F1B86C7E0BF881E0.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1749486031396294, "dur": 428, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1749486031396726, "dur": 342, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting@1.9.4\\Editor\\VisualScripting.Core\\Dependencies\\YamlDotNet\\Unity.VisualScripting.YamlDotNet.dll"}}, {"pid": 12345, "tid": 10, "ts": 1749486031396725, "dur": 345, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.VisualScripting.YamlDotNet.dll_4A5905A13DE9A491.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1749486031397071, "dur": 94, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1749486031397232, "dur": 317, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1749486031397566, "dur": 67, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.ShaderGraph.Editor.rsp"}}, {"pid": 12345, "tid": 10, "ts": 1749486031397634, "dur": 115, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1749486031397795, "dur": 149, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1749486031397952, "dur": 97, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1749486031398063, "dur": 68, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1749486031398170, "dur": 66, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1749486031398241, "dur": 78, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1749486031398327, "dur": 58, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/1410676725171572189.rsp"}}, {"pid": 12345, "tid": 10, "ts": 1749486031398386, "dur": 93, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1749486031398547, "dur": 68, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/16942202937359522267.rsp"}}, {"pid": 12345, "tid": 10, "ts": 1749486031398685, "dur": 1170, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1749486031399855, "dur": 1726, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1749486031401581, "dur": 1353, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1749486031402935, "dur": 1968, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1749486031404904, "dur": 1502, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1749486031407551, "dur": 502, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.shadergraph@14.0.11\\Editor\\Generation\\Targets\\BuiltIn\\Editor\\ShaderGraph\\AssetCallbacks\\CreateUnlitShaderGraph.cs"}}, {"pid": 12345, "tid": 10, "ts": 1749486031406406, "dur": 2429, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1749486031408835, "dur": 1768, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1749486031410603, "dur": 1985, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1749486031412588, "dur": 1623, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1749486031414211, "dur": 1540, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1749486031415752, "dur": 1562, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1749486031417315, "dur": 2131, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1749486031419447, "dur": 1321, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1749486031420769, "dur": 1957, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1749486031422727, "dur": 1498, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1749486031424225, "dur": 1209, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1749486031425434, "dur": 480, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1749486031425915, "dur": 168, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Core.dll.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1749486031426083, "dur": 1266, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1749486031427354, "dur": 1320, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Core.dll (+2 others)"}}, {"pid": 12345, "tid": 10, "ts": 1749486031428674, "dur": 919, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1749486031429633, "dur": 66, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Flow.dll.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1749486031429728, "dur": 1114, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Flow.dll (+2 others)"}}, {"pid": 12345, "tid": 10, "ts": 1749486031430843, "dur": 218, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1749486031431072, "dur": 72, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1749486031431156, "dur": 103, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.State.dll.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1749486031431259, "dur": 66, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1749486031431329, "dur": 402, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.State.dll (+2 others)"}}, {"pid": 12345, "tid": 10, "ts": 1749486031431732, "dur": 178, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1749486031431923, "dur": 64, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1749486031432005, "dur": 61, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.State.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1749486031432100, "dur": 238, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.State.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 10, "ts": 1749486031432338, "dur": 87, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1749486031432531, "dur": 164, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Shared.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 10, "ts": 1749486031432696, "dur": 65, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1749486031432800, "dur": 63, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp.dll.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1749486031432891, "dur": 200, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp.dll (+2 others)"}}, {"pid": 12345, "tid": 10, "ts": 1749486031433382, "dur": 262399, "ph": "X", "name": "Csc", "args": {"detail": "Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp.dll (+2 others)"}}, {"pid": 12345, "tid": 10, "ts": 1749486031699990, "dur": 113464, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1900b0aE.dag\\Assembly-CSharp.ref.dll"}}, {"pid": 12345, "tid": 10, "ts": 1749486031699784, "dur": 113722, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp-Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 10, "ts": 1749486031813507, "dur": 133, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1749486031813653, "dur": 621643, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1900b0aE.dag\\Assembly-CSharp.dll"}}, {"pid": 12345, "tid": 10, "ts": 1749486031813652, "dur": 622715, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Assembly-CSharp-Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 10, "ts": 1749486032437178, "dur": 145, "ph": "X", "name": "StoreTimestampsOfNonGeneratedInputFiles", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1749486032437793, "dur": 53482, "ph": "X", "name": "ILPostProcess", "args": {"detail": "Library/Bee/artifacts/1900b0aE.dag/post-processed/Assembly-CSharp-Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 10, "ts": 1749486032506829, "dur": 98, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1900b0aE.dag\\post-processed\\Assembly-CSharp-Editor.pdb"}}, {"pid": 12345, "tid": 10, "ts": 1749486032506828, "dur": 100, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Assembly-CSharp-Editor.pdb"}}, {"pid": 12345, "tid": 10, "ts": 1749486032506978, "dur": 743666, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1749486031380628, "dur": 14804, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1749486031395475, "dur": 589, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Tools\\ScriptUpdater\\ApiUpdater.MovedFromExtractor.exe"}}, {"pid": 12345, "tid": 11, "ts": 1749486031395437, "dur": 628, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.CrashReportingModule.dll_7F06B267F1BFA456.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1749486031396124, "dur": 56, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.TextRenderingModule.dll"}}, {"pid": 12345, "tid": 11, "ts": 1749486031396124, "dur": 58, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TextRenderingModule.dll_3AA9E1B89A839D24.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1749486031396182, "dur": 52, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1749486031396246, "dur": 50, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestAssetBundleModule.dll_EC1D9C7602E10CB1.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1749486031396296, "dur": 122, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1749486031396519, "dur": 553, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1749486031397405, "dur": 67, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1749486031397496, "dur": 132, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1749486031397633, "dur": 207, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1749486031397850, "dur": 83, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1749486031397942, "dur": 69, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1749486031398025, "dur": 84, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1749486031398149, "dur": 77, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1749486031398236, "dur": 91, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1749486031398332, "dur": 75, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/1418726328684876121.rsp"}}, {"pid": 12345, "tid": 11, "ts": 1749486031398407, "dur": 66, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1749486031398506, "dur": 71, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1749486031398604, "dur": 87, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1749486031398691, "dur": 56, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/15183149355271759364.rsp"}}, {"pid": 12345, "tid": 11, "ts": 1749486031398748, "dur": 1877, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1749486031400625, "dur": 1517, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1749486031402143, "dur": 1650, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1749486031403793, "dur": 1365, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1749486031405158, "dur": 1532, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1749486031407547, "dur": 500, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.shadergraph@14.0.11\\Editor\\Generation\\Data\\FieldDependency.cs"}}, {"pid": 12345, "tid": 11, "ts": 1749486031406691, "dur": 2119, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1749486031408811, "dur": 2423, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1749486031411235, "dur": 1325, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1749486031412561, "dur": 1767, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1749486031414329, "dur": 1899, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1749486031416228, "dur": 1844, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1749486031418073, "dur": 1742, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1749486031419815, "dur": 1756, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1749486031421571, "dur": 1708, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1749486031423280, "dur": 1570, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1749486031424851, "dur": 622, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1749486031425473, "dur": 528, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1749486031426003, "dur": 179, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.ShaderLibrary.dll.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1749486031426183, "dur": 66, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1749486031426255, "dur": 131, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.ShaderGraph.ShaderGraphLibrary.dll.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1749486031426386, "dur": 168, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1749486031426951, "dur": 76, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Linq.Queryable.dll"}}, {"pid": 12345, "tid": 11, "ts": 1749486031426563, "dur": 599, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.ShaderGraph.ShaderGraphLibrary.dll (+2 others)"}}, {"pid": 12345, "tid": 11, "ts": 1749486031427162, "dur": 177, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1749486031427388, "dur": 196, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1749486031427587, "dur": 68, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1749486031427660, "dur": 285, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1749486031427945, "dur": 281, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1749486031428226, "dur": 659, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1749486031428888, "dur": 138, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1749486031429031, "dur": 268, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1749486031429300, "dur": 120, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.Collections.DocCodeSamples.dll.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1749486031429420, "dur": 311, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1749486031429736, "dur": 383, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Collections.DocCodeSamples.dll (+2 others)"}}, {"pid": 12345, "tid": 11, "ts": 1749486031430120, "dur": 256, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1749486031430380, "dur": 60575, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1749486031492714, "dur": 63, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.DSPGraphModule.dll"}}, {"pid": 12345, "tid": 11, "ts": 1749486031490956, "dur": 2366, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.Core.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 11, "ts": 1749486031493323, "dur": 453, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1749486031494709, "dur": 171, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\aspnetcorev2_inprocess.dll"}}, {"pid": 12345, "tid": 11, "ts": 1749486031493783, "dur": 2173, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.VisualScripting.SettingsProvider.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 11, "ts": 1749486031495956, "dur": 1812, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1749486031499610, "dur": 1137, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netfx\\System.Xml.Serialization.dll"}}, {"pid": 12345, "tid": 11, "ts": 1749486031497776, "dur": 3446, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Timeline.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 11, "ts": 1749486031501223, "dur": 1342, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1749486031502580, "dur": 158, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1749486031502747, "dur": 107, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1749486031502859, "dur": 1747710, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1749486031380654, "dur": 14793, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1749486031395485, "dur": 544, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Tools\\ScriptUpdater\\ApiUpdater.MovedFromExtractor.exe"}}, {"pid": 12345, "tid": 12, "ts": 1749486031395452, "dur": 578, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.dll_38FFBE07FFE62337.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1749486031396107, "dur": 118, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1749486031396234, "dur": 68, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.UnityConnectModule.dll"}}, {"pid": 12345, "tid": 12, "ts": 1749486031396233, "dur": 71, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityConnectModule.dll_D0DC9EA7252E13B4.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1749486031396305, "dur": 55, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1749486031396557, "dur": 99, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEditor.GraphViewModule.dll"}}, {"pid": 12345, "tid": 12, "ts": 1749486031396556, "dur": 101, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.GraphViewModule.dll_F27749E0A6B65183.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1749486031396721, "dur": 301, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1749486031397039, "dur": 425, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1749486031397605, "dur": 191, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1749486031397888, "dur": 57, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1749486031398037, "dur": 55, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1749486031398177, "dur": 168, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1749486031398463, "dur": 189, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1749486031398693, "dur": 1582, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1749486031400276, "dur": 1771, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1749486031402048, "dur": 1821, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1749486031403869, "dur": 1909, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1749486031405778, "dur": 1768, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1749486031407546, "dur": 1999, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1749486031409545, "dur": 2026, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1749486031411571, "dur": 1580, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1749486031413151, "dur": 1852, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1749486031415003, "dur": 1531, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1749486031416535, "dur": 1905, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1749486031418440, "dur": 1783, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1749486031420223, "dur": 1533, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1749486031421757, "dur": 1491, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1749486031423248, "dur": 1434, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1749486031424682, "dur": 758, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1749486031425440, "dur": 530, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1749486031425971, "dur": 246, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.PlasticSCM.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1749486031426217, "dur": 198, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1749486031426439, "dur": 133, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEditor.GraphViewModule.dll"}}, {"pid": 12345, "tid": 12, "ts": 1749486031426758, "dur": 107, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.VRModule.dll"}}, {"pid": 12345, "tid": 12, "ts": 1749486031426420, "dur": 1348, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.PlasticSCM.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 12, "ts": 1749486031427769, "dur": 188, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1749486031427967, "dur": 92, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1749486031428067, "dur": 137, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.CollabProxy.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1749486031428205, "dur": 445, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1749486031428658, "dur": 480, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.CollabProxy.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 12, "ts": 1749486031429139, "dur": 178, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1749486031429321, "dur": 4288, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1749486031433611, "dur": 3239, "ph": "X", "name": "CheckDagSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1749486031436851, "dur": 56768, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1749486031493620, "dur": 2190, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/UnityEngine.UI.dll (+pdb)"}}, {"pid": 12345, "tid": 12, "ts": 1749486031495810, "dur": 2900, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1749486031498832, "dur": 816, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.GridModule.dll"}}, {"pid": 12345, "tid": 12, "ts": 1749486031502496, "dur": 68, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.collab-proxy@2.5.2\\Lib\\Editor\\PlasticSCM\\log4netPlastic.dll"}}, {"pid": 12345, "tid": 12, "ts": 1749486031498715, "dur": 3917, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.ShaderGraph.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 12, "ts": 1749486031502633, "dur": 90, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1749486031502817, "dur": 1747751, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1749486031380677, "dur": 14777, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1749486031395489, "dur": 549, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Tools\\ScriptUpdater\\ApiUpdater.MovedFromExtractor.exe"}}, {"pid": 12345, "tid": 13, "ts": 1749486031395459, "dur": 580, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.DSPGraphModule.dll_4012322E9A5614BC.mvfrm"}}, {"pid": 12345, "tid": 13, "ts": 1749486031396040, "dur": 67, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1749486031396170, "dur": 65, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1749486031396285, "dur": 107, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1749486031396558, "dur": 146, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Managed\\UnityEditor.Graphs.dll"}}, {"pid": 12345, "tid": 13, "ts": 1749486031396558, "dur": 147, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.Graphs.dll_5AAB6A659692F205.mvfrm"}}, {"pid": 12345, "tid": 13, "ts": 1749486031396706, "dur": 335, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1749486031397095, "dur": 70, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1749486031397173, "dur": 67, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1749486031397252, "dur": 82, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1749486031397348, "dur": 68, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1749486031397425, "dur": 94, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1749486031397530, "dur": 55, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1749486031397597, "dur": 109, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1749486031397789, "dur": 88, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1749486031397924, "dur": 246, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1749486031398185, "dur": 75, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1749486031398264, "dur": 195, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1749486031398676, "dur": 1520, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1749486031400196, "dur": 1700, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1749486031401897, "dur": 1382, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1749486031403280, "dur": 1391, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1749486031404671, "dur": 2018, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1749486031406689, "dur": 1844, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1749486031408533, "dur": 1865, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1749486031410398, "dur": 1938, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1749486031412336, "dur": 1816, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1749486031414153, "dur": 2086, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1749486031416239, "dur": 2233, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1749486031418473, "dur": 2052, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1749486031420525, "dur": 1599, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1749486031422124, "dur": 1407, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1749486031423531, "dur": 1390, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1749486031424921, "dur": 556, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1749486031425477, "dur": 673, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1749486031426151, "dur": 197, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Shaders.dll.mvfrm"}}, {"pid": 12345, "tid": 13, "ts": 1749486031426348, "dur": 68, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1749486031426423, "dur": 595, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Shaders.dll (+2 others)"}}, {"pid": 12345, "tid": 13, "ts": 1749486031427019, "dur": 198, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1749486031427232, "dur": 67, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1749486031427302, "dur": 283, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1749486031427632, "dur": 312, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1749486031427945, "dur": 232, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/PsdPlugin.dll.mvfrm"}}, {"pid": 12345, "tid": 13, "ts": 1749486031428178, "dur": 59, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1749486031428239, "dur": 476, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/PsdPlugin.dll (+2 others)"}}, {"pid": 12345, "tid": 13, "ts": 1749486031428716, "dur": 1330, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1749486031430051, "dur": 60938, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1749486031490989, "dur": 2515, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Mathematics.dll (+pdb)"}}, {"pid": 12345, "tid": 13, "ts": 1749486031493505, "dur": 841, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1749486031494353, "dur": 1940, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.2D.Tilemap.Extras.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 13, "ts": 1749486031496294, "dur": 107, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1749486031496599, "dur": 632, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netfx\\System.dll"}}, {"pid": 12345, "tid": 13, "ts": 1749486031498842, "dur": 76, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Diagnostics.Process.dll"}}, {"pid": 12345, "tid": 13, "ts": 1749486031499452, "dur": 97, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Diagnostics.TextWriterTraceListener.dll"}}, {"pid": 12345, "tid": 13, "ts": 1749486031496408, "dur": 4620, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.VisualScripting.Core.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 13, "ts": 1749486031501029, "dur": 118, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1749486031501793, "dur": 54, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Runtime.InteropServices.WindowsRuntime.dll"}}, {"pid": 12345, "tid": 13, "ts": 1749486031501152, "dur": 1860, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.2D.Aseprite.Common.dll (+pdb)"}}, {"pid": 12345, "tid": 13, "ts": 1749486031503013, "dur": 74, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1749486031503122, "dur": 1747484, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1749486031380696, "dur": 14769, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1749486031395504, "dur": 621, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Tools\\ScriptUpdater\\ApiUpdater.MovedFromExtractor.exe"}}, {"pid": 12345, "tid": 14, "ts": 1749486031395469, "dur": 657, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.GameCenterModule.dll_7C296F75F94E46F6.mvfrm"}}, {"pid": 12345, "tid": 14, "ts": 1749486031396127, "dur": 101, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1749486031396241, "dur": 101, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.UnityCurlModule.dll"}}, {"pid": 12345, "tid": 14, "ts": 1749486031396239, "dur": 106, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityCurlModule.dll_F419673AF1271816.mvfrm"}}, {"pid": 12345, "tid": 14, "ts": 1749486031396346, "dur": 99, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1749486031396465, "dur": 70, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.AndroidJNIModule.dll"}}, {"pid": 12345, "tid": 14, "ts": 1749486031396463, "dur": 73, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AndroidJNIModule.dll_51DC255B330D5605.mvfrm"}}, {"pid": 12345, "tid": 14, "ts": 1749486031396536, "dur": 87, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1749486031396666, "dur": 314, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1749486031396986, "dur": 354, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.burst@1.8.15\\Unity.Burst.CodeGen\\Unity.Burst.Cecil.dll"}}, {"pid": 12345, "tid": 14, "ts": 1749486031396985, "dur": 356, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Burst.Cecil.dll_62273FC84B3E0F2D.mvfrm"}}, {"pid": 12345, "tid": 14, "ts": 1749486031397475, "dur": 124, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1749486031397654, "dur": 90, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1749486031398007, "dur": 51, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1749486031398347, "dur": 57, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/5916180605834378387.rsp"}}, {"pid": 12345, "tid": 14, "ts": 1749486031398476, "dur": 61, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1749486031398626, "dur": 53, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1749486031398683, "dur": 1731, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1749486031400414, "dur": 1891, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1749486031402306, "dur": 1432, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1749486031403738, "dur": 1571, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1749486031405309, "dur": 1792, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1749486031407101, "dur": 1729, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1749486031408831, "dur": 1651, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1749486031410483, "dur": 2425, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1749486031412908, "dur": 1817, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1749486031414725, "dur": 1613, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1749486031416339, "dur": 1843, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1749486031418182, "dur": 2255, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1749486031420440, "dur": 50, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1749486031420538, "dur": 1835, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1749486031422374, "dur": 1419, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1749486031423793, "dur": 943, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1749486031424736, "dur": 724, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1749486031425461, "dur": 510, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1749486031425972, "dur": 512, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualStudio.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 14, "ts": 1749486031426485, "dur": 345, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1749486031426841, "dur": 615, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualStudio.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 14, "ts": 1749486031427457, "dur": 112, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1749486031427588, "dur": 74, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1749486031427668, "dur": 275, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1749486031427944, "dur": 214, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 14, "ts": 1749486031428158, "dur": 139, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1749486031428302, "dur": 477, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 14, "ts": 1749486031428780, "dur": 194, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1749486031428987, "dur": 55, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1749486031429046, "dur": 255, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1749486031429302, "dur": 1857, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1749486031431160, "dur": 68, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Flow.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 14, "ts": 1749486031431229, "dur": 50, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1749486031431285, "dur": 380, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Flow.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 14, "ts": 1749486031431665, "dur": 83, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1749486031431789, "dur": 59163, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1749486031490954, "dur": 2494, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.Universal.Shaders.dll (+pdb)"}}, {"pid": 12345, "tid": 14, "ts": 1749486031493449, "dur": 146, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1749486031493666, "dur": 71, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.CoreModule.dll"}}, {"pid": 12345, "tid": 14, "ts": 1749486031494710, "dur": 1630, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\Microsoft.AspNetCore.Http.Connections.dll"}}, {"pid": 12345, "tid": 14, "ts": 1749486031497223, "dur": 1482, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Diagnostics.TextWriterTraceListener.dll"}}, {"pid": 12345, "tid": 14, "ts": 1749486031493608, "dur": 5658, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Timeline.dll (+pdb)"}}, {"pid": 12345, "tid": 14, "ts": 1749486031499267, "dur": 2255, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1749486031501529, "dur": 1766, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.2D.Aseprite.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 14, "ts": 1749486031503295, "dur": 68, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1749486031503413, "dur": 1747230, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1749486031380715, "dur": 14842, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1749486031395592, "dur": 574, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Tools\\ScriptUpdater\\ApiUpdater.MovedFromExtractor.exe"}}, {"pid": 12345, "tid": 15, "ts": 1749486031395562, "dur": 605, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.GIModule.dll_826CA845B2B614E1.mvfrm"}}, {"pid": 12345, "tid": 15, "ts": 1749486031396167, "dur": 84, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1749486031396257, "dur": 52, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.EditorToolbarModule.dll_41265C97D041BE6E.mvfrm"}}, {"pid": 12345, "tid": 15, "ts": 1749486031396310, "dur": 507, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1749486031396825, "dur": 83, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1749486031396914, "dur": 83, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1749486031397092, "dur": 51, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1749486031397189, "dur": 269, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1749486031397472, "dur": 85, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1749486031397594, "dur": 55, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1749486031397654, "dur": 132, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1749486031397787, "dur": 61, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.2D.Tilemap.Extras.rsp2"}}, {"pid": 12345, "tid": 15, "ts": 1749486031397890, "dur": 122, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1749486031398260, "dur": 66, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1749486031398333, "dur": 51, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/10654656162358534522.rsp"}}, {"pid": 12345, "tid": 15, "ts": 1749486031398748, "dur": 2044, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1749486031400792, "dur": 2134, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1749486031402927, "dur": 1684, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1749486031404612, "dur": 1496, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1749486031406109, "dur": 1485, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1749486031407595, "dur": 2258, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1749486031409853, "dur": 2156, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1749486031412010, "dur": 1768, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1749486031413778, "dur": 1594, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1749486031415372, "dur": 1379, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1749486031416752, "dur": 2098, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1749486031418850, "dur": 1702, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1749486031420552, "dur": 1963, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1749486031422515, "dur": 1403, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1749486031424098, "dur": 1332, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1749486031425431, "dur": 487, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1749486031425919, "dur": 131, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.InternalAPIEngineBridge.001.dll.mvfrm"}}, {"pid": 12345, "tid": 15, "ts": 1749486031426050, "dur": 54, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1749486031426111, "dur": 533, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.InternalAPIEngineBridge.001.dll (+2 others)"}}, {"pid": 12345, "tid": 15, "ts": 1749486031426645, "dur": 797, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1749486031427452, "dur": 53, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1749486031427507, "dur": 91, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.2D.IK.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 15, "ts": 1749486031427637, "dur": 525, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.2D.IK.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 15, "ts": 1749486031428162, "dur": 578, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1749486031428756, "dur": 57, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1749486031428817, "dur": 67, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1749486031428890, "dur": 893, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1749486031429786, "dur": 7107, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1749486031436894, "dur": 54101, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1749486031491448, "dur": 103, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.VRModule.dll"}}, {"pid": 12345, "tid": 15, "ts": 1749486031491552, "dur": 94, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.WindModule.dll"}}, {"pid": 12345, "tid": 15, "ts": 1749486031491711, "dur": 201, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netfx\\System.Xml.Linq.dll"}}, {"pid": 12345, "tid": 15, "ts": 1749486031492495, "dur": 74, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Reflection.Primitives.dll"}}, {"pid": 12345, "tid": 15, "ts": 1749486031492736, "dur": 51, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\System.Data.DataSetExtensions.dll"}}, {"pid": 12345, "tid": 15, "ts": 1749486031493065, "dur": 136, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\Microsoft.AspNetCore.Components.Authorization.dll"}}, {"pid": 12345, "tid": 15, "ts": 1749486031493274, "dur": 96, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\Microsoft.AspNetCore.HostFiltering.dll"}}, {"pid": 12345, "tid": 15, "ts": 1749486031493645, "dur": 305, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\Microsoft.Extensions.Logging.Debug.dll"}}, {"pid": 12345, "tid": 15, "ts": 1749486031494708, "dur": 307, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.IO.FileSystem.DriveInfo.dll"}}, {"pid": 12345, "tid": 15, "ts": 1749486031495464, "dur": 98, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Reflection.Emit.dll"}}, {"pid": 12345, "tid": 15, "ts": 1749486031490999, "dur": 4834, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.2D.Tilemap.Extras.dll (+pdb)"}}, {"pid": 12345, "tid": 15, "ts": 1749486031495834, "dur": 1457, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1749486031497301, "dur": 2778, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.2D.IK.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 15, "ts": 1749486031500079, "dur": 216, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1749486031500305, "dur": 2147, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.VisualScripting.State.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 15, "ts": 1749486031502452, "dur": 105, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1749486031502561, "dur": 127, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1749486031502740, "dur": 79, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1749486031502821, "dur": 1747749, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1749486031380737, "dur": 14827, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1749486031395595, "dur": 645, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Tools\\ScriptUpdater\\ApiUpdater.MovedFromExtractor.exe"}}, {"pid": 12345, "tid": 16, "ts": 1749486031395569, "dur": 672, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.GridModule.dll_4F40831771D8FBF5.mvfrm"}}, {"pid": 12345, "tid": 16, "ts": 1749486031396241, "dur": 50, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1749486031396335, "dur": 58, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1749486031396399, "dur": 302, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEditor.CoreModule.dll"}}, {"pid": 12345, "tid": 16, "ts": 1749486031396398, "dur": 305, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.CoreModule.dll_4EAE93D6B17EFAD3.mvfrm"}}, {"pid": 12345, "tid": 16, "ts": 1749486031396704, "dur": 86, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1749486031396798, "dur": 50, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.NVIDIAModule.dll"}}, {"pid": 12345, "tid": 16, "ts": 1749486031396797, "dur": 53, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.NVIDIAModule.dll_882D400E4D49662A.mvfrm"}}, {"pid": 12345, "tid": 16, "ts": 1749486031396850, "dur": 60, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1749486031397016, "dur": 253, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1749486031397413, "dur": 145, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1749486031397753, "dur": 50, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1749486031397875, "dur": 91, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1749486031397969, "dur": 55, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1749486031398104, "dur": 119, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1749486031398674, "dur": 1384, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1749486031400058, "dur": 1524, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1749486031401583, "dur": 1895, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1749486031403478, "dur": 1588, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1749486031405067, "dur": 1927, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1749486031406995, "dur": 1743, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1749486031408738, "dur": 1889, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1749486031410627, "dur": 2280, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1749486031412908, "dur": 1854, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1749486031414763, "dur": 1544, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1749486031416308, "dur": 1851, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1749486031418159, "dur": 1812, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1749486031419972, "dur": 1378, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1749486031421351, "dur": 1428, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1749486031422780, "dur": 1373, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1749486031424153, "dur": 1264, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1749486031425420, "dur": 55, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1749486031425479, "dur": 494, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1749486031425994, "dur": 196, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.Rider.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 16, "ts": 1749486031426191, "dur": 82, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1749486031426278, "dur": 661, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Rider.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 16, "ts": 1749486031426939, "dur": 158, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1749486031427196, "dur": 404, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1749486031427601, "dur": 355, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1749486031427956, "dur": 249, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1749486031428206, "dur": 172, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.Mathematics.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 16, "ts": 1749486031428433, "dur": 570, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Mathematics.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 16, "ts": 1749486031429004, "dur": 127, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1749486031429164, "dur": 138, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1749486031429303, "dur": 2207, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1749486031431511, "dur": 133, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.2D.IK.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 16, "ts": 1749486031431645, "dur": 84, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1749486031431740, "dur": 356, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.2D.IK.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 16, "ts": 1749486031432096, "dur": 173, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1749486031432278, "dur": 83, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1749486031432370, "dur": 58640, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1749486031492012, "dur": 248, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.NVIDIAModule.dll"}}, {"pid": 12345, "tid": 16, "ts": 1749486031492333, "dur": 53, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Data.Common.dll"}}, {"pid": 12345, "tid": 16, "ts": 1749486031492778, "dur": 135, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\mscorlib.dll"}}, {"pid": 12345, "tid": 16, "ts": 1749486031491012, "dur": 3803, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.InternalAPIEngineBridge.001.dll (+pdb)"}}, {"pid": 12345, "tid": 16, "ts": 1749486031494816, "dur": 331, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1749486031496232, "dur": 148, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\api-ms-win-crt-environment-l1-1-0.dll"}}, {"pid": 12345, "tid": 16, "ts": 1749486031497772, "dur": 125, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Collections.dll"}}, {"pid": 12345, "tid": 16, "ts": 1749486031495160, "dur": 3093, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/PPv2URPConverters.dll (+pdb)"}}, {"pid": 12345, "tid": 16, "ts": 1749486031498253, "dur": 291, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1749486031498871, "dur": 79, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\Microsoft.Win32.Primitives.dll"}}, {"pid": 12345, "tid": 16, "ts": 1749486031500569, "dur": 85, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEditor.UIBuilderModule.dll"}}, {"pid": 12345, "tid": 16, "ts": 1749486031500911, "dur": 1285, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Diagnostics.StackTrace.dll"}}, {"pid": 12345, "tid": 16, "ts": 1749486031502675, "dur": 66, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Threading.Tasks.Extensions.dll"}}, {"pid": 12345, "tid": 16, "ts": 1749486031498555, "dur": 4273, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.2D.Sprite.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 16, "ts": 1749486031502834, "dur": 105, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1749486031502946, "dur": 68, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1749486031503016, "dur": 1747597, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1749486031380758, "dur": 14813, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1749486031395606, "dur": 601, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Tools\\ScriptUpdater\\ApiUpdater.MovedFromExtractor.exe"}}, {"pid": 12345, "tid": 17, "ts": 1749486031395575, "dur": 633, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.HotReloadModule.dll_4245840F5C4641AB.mvfrm"}}, {"pid": 12345, "tid": 17, "ts": 1749486031396299, "dur": 88, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1749486031396543, "dur": 107, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEditor.PresetsUIModule.dll"}}, {"pid": 12345, "tid": 17, "ts": 1749486031396543, "dur": 109, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.PresetsUIModule.dll_ADDD82C2A90EA988.mvfrm"}}, {"pid": 12345, "tid": 17, "ts": 1749486031396652, "dur": 387, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1749486031397046, "dur": 69, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Core.rsp"}}, {"pid": 12345, "tid": 17, "ts": 1749486031397116, "dur": 161, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1749486031397295, "dur": 90, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1749486031397390, "dur": 132, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1749486031397737, "dur": 62, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1749486031397819, "dur": 119, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1749486031397983, "dur": 101, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1749486031398105, "dur": 117, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1749486031398268, "dur": 96, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1749486031398367, "dur": 51, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/9870516708743257357.rsp"}}, {"pid": 12345, "tid": 17, "ts": 1749486031398621, "dur": 50, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1749486031398687, "dur": 1508, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1749486031400196, "dur": 1228, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1749486031401424, "dur": 1633, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1749486031403057, "dur": 2081, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1749486031405139, "dur": 1379, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1749486031406518, "dur": 1692, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1749486031408211, "dur": 1801, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1749486031410013, "dur": 2028, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1749486031412041, "dur": 2082, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1749486031414123, "dur": 1677, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1749486031415801, "dur": 2250, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1749486031418051, "dur": 1406, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1749486031419458, "dur": 1743, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1749486031421202, "dur": 1477, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1749486031422679, "dur": 1277, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1749486031424096, "dur": 1339, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1749486031425435, "dur": 480, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1749486031425917, "dur": 160, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 17, "ts": 1749486031426078, "dur": 59, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1749486031426140, "dur": 1016, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 17, "ts": 1749486031427157, "dur": 553, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1749486031427766, "dur": 109, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipeline.Universal.ShaderLibrary.dll.mvfrm"}}, {"pid": 12345, "tid": 17, "ts": 1749486031427918, "dur": 559, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipeline.Universal.ShaderLibrary.dll (+2 others)"}}, {"pid": 12345, "tid": 17, "ts": 1749486031428478, "dur": 749, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1749486031429238, "dur": 71, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1749486031429313, "dur": 3498, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1749486031432813, "dur": 120, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp-Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 17, "ts": 1749486031432934, "dur": 118, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1749486031433062, "dur": 57924, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1749486031490987, "dur": 2727, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.TextMeshPro.dll (+pdb)"}}, {"pid": 12345, "tid": 17, "ts": 1749486031493714, "dur": 406, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1749486031494128, "dur": 2183, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.2D.Tilemap.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 17, "ts": 1749486031496312, "dur": 885, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1749486031498692, "dur": 81, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.VFXModule.dll"}}, {"pid": 12345, "tid": 17, "ts": 1749486031498872, "dur": 106, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Console.dll"}}, {"pid": 12345, "tid": 17, "ts": 1749486031499355, "dur": 52, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.ComponentModel.dll"}}, {"pid": 12345, "tid": 17, "ts": 1749486031499608, "dur": 1316, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Security.Claims.dll"}}, {"pid": 12345, "tid": 17, "ts": 1749486031497206, "dur": 3872, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Searcher.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 17, "ts": 1749486031501079, "dur": 552, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1749486031501751, "dur": 218, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1749486031501980, "dur": 134, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1749486031502170, "dur": 52, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1749486031502228, "dur": 63, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1749486031502297, "dur": 58, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1749486031502361, "dur": 55, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1749486031502423, "dur": 66, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1749486031502494, "dur": 50, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/PPv2URPConverters.dll"}}, {"pid": 12345, "tid": 17, "ts": 1749486031502544, "dur": 127, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1749486031502781, "dur": 495, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1749486031503363, "dur": 1747224, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1749486031380790, "dur": 14788, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1749486031395613, "dur": 648, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Tools\\ScriptUpdater\\ApiUpdater.MovedFromExtractor.exe"}}, {"pid": 12345, "tid": 18, "ts": 1749486031395583, "dur": 679, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ImageConversionModule.dll_B5C2173E9AD0B5D6.mvfrm"}}, {"pid": 12345, "tid": 18, "ts": 1749486031396263, "dur": 127, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1749486031396562, "dur": 120, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1749486031396711, "dur": 53, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1749486031396867, "dur": 219, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.burst@1.8.15\\Unity.Burst.Unsafe.dll"}}, {"pid": 12345, "tid": 18, "ts": 1749486031396866, "dur": 221, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Burst.Unsafe.dll_D2F5C6CBE8D78A56.mvfrm"}}, {"pid": 12345, "tid": 18, "ts": 1749486031397130, "dur": 379, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1749486031397510, "dur": 65, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.2D.Sprite.Editor.rsp"}}, {"pid": 12345, "tid": 18, "ts": 1749486031397576, "dur": 107, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1749486031397800, "dur": 122, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1749486031397925, "dur": 94, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1749486031398026, "dur": 282, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1749486031398330, "dur": 62, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1749486031398418, "dur": 117, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1749486031398566, "dur": 62, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1749486031398652, "dur": 60, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1749486031398712, "dur": 50, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/3388064732626934676.rsp"}}, {"pid": 12345, "tid": 18, "ts": 1749486031398762, "dur": 1677, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1749486031400439, "dur": 2135, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1749486031402575, "dur": 2060, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1749486031404635, "dur": 1511, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1749486031406146, "dur": 2037, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1749486031408183, "dur": 1992, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1749486031410176, "dur": 1670, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1749486031411847, "dur": 1734, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1749486031413581, "dur": 1246, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1749486031414827, "dur": 1750, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1749486031416577, "dur": 1796, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1749486031418373, "dur": 2125, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1749486031420499, "dur": 1938, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1749486031422438, "dur": 1404, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1749486031423842, "dur": 575, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1749486031424418, "dur": 997, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1749486031425420, "dur": 74, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1749486031425501, "dur": 423, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1749486031425932, "dur": 185, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.2D.PixelPerfect.dll.mvfrm"}}, {"pid": 12345, "tid": 18, "ts": 1749486031426118, "dur": 86, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1749486031426209, "dur": 468, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.2D.PixelPerfect.dll (+2 others)"}}, {"pid": 12345, "tid": 18, "ts": 1749486031426677, "dur": 973, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1749486031427696, "dur": 246, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1749486031427943, "dur": 139, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 18, "ts": 1749486031428082, "dur": 144, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1749486031428228, "dur": 800, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 18, "ts": 1749486031429028, "dur": 134, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1749486031429174, "dur": 97, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1749486031429276, "dur": 115, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.ShaderGraph.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 18, "ts": 1749486031429424, "dur": 1304, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.ShaderGraph.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 18, "ts": 1749486031430729, "dur": 140, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1749486031430908, "dur": 56, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 18, "ts": 1749486031430995, "dur": 419, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 18, "ts": 1749486031431415, "dur": 84, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1749486031431544, "dur": 54, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.2D.PixelPerfect.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 18, "ts": 1749486031431630, "dur": 216, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.2D.PixelPerfect.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 18, "ts": 1749486031431846, "dur": 82, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1749486031431969, "dur": 59032, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1749486031491004, "dur": 3569, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.Core.ShaderLibrary.dll (+pdb)"}}, {"pid": 12345, "tid": 18, "ts": 1749486031494574, "dur": 1960, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1749486031496545, "dur": 2022, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/PsdPlugin.dll (+pdb)"}}, {"pid": 12345, "tid": 18, "ts": 1749486031498568, "dur": 102, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1749486031498999, "dur": 58, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.UnityCurlModule.dll"}}, {"pid": 12345, "tid": 18, "ts": 1749486031499608, "dur": 169, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\PlaybackEngines\\WindowsStandaloneSupport\\UnityEditor.WindowsStandalone.Extensions.dll"}}, {"pid": 12345, "tid": 18, "ts": 1749486031500910, "dur": 182, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEditor.UnityConnectModule.dll"}}, {"pid": 12345, "tid": 18, "ts": 1749486031498678, "dur": 3187, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.Universal.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 18, "ts": 1749486031501866, "dur": 613, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1749486031502488, "dur": 71, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1749486031502567, "dur": 56, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1749486031502631, "dur": 56, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1749486031502693, "dur": 59, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1749486031502757, "dur": 51, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1749486031502816, "dur": 1747755, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1749486031380805, "dur": 14780, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1749486031395638, "dur": 671, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Tools\\ScriptUpdater\\ApiUpdater.MovedFromExtractor.exe"}}, {"pid": 12345, "tid": 19, "ts": 1749486031395602, "dur": 708, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.IMGUIModule.dll_CBFD31FC7A6EE8BC.mvfrm"}}, {"pid": 12345, "tid": 19, "ts": 1749486031396310, "dur": 71, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1749486031396511, "dur": 319, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1749486031396841, "dur": 53, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1749486031397021, "dur": 512, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1749486031397676, "dur": 159, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1749486031397843, "dur": 125, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1749486031398241, "dur": 74, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1749486031398415, "dur": 98, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1749486031398680, "dur": 1963, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1749486031400644, "dur": 1728, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1749486031402372, "dur": 1643, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1749486031404015, "dur": 2004, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1749486031406019, "dur": 2277, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1749486031408296, "dur": 1102, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1749486031409399, "dur": 1522, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1749486031410922, "dur": 1854, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1749486031412777, "dur": 1922, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1749486031414700, "dur": 2100, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1749486031416800, "dur": 1784, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1749486031418585, "dur": 1341, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1749486031419927, "dur": 1454, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1749486031421381, "dur": 1670, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1749486031423051, "dur": 384, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1749486031423435, "dur": 1511, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1749486031424946, "dur": 504, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1749486031425452, "dur": 503, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1749486031425963, "dur": 364, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.TextMeshPro.dll.mvfrm"}}, {"pid": 12345, "tid": 19, "ts": 1749486031426328, "dur": 66, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1749486031426394, "dur": 339, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.TextMeshPro.dll.mvfrm"}}, {"pid": 12345, "tid": 19, "ts": 1749486031426952, "dur": 116, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netfx\\System.ComponentModel.Composition.dll"}}, {"pid": 12345, "tid": 19, "ts": 1749486031426735, "dur": 831, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.TextMeshPro.dll (+2 others)"}}, {"pid": 12345, "tid": 19, "ts": 1749486031427566, "dur": 92, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1749486031427702, "dur": 65, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.TextMeshPro.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 19, "ts": 1749486031427807, "dur": 414, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.TextMeshPro.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 19, "ts": 1749486031428222, "dur": 937, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1749486031429205, "dur": 93, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1749486031429299, "dur": 104, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.Collections.BurstCompatibilityGen.dll.mvfrm"}}, {"pid": 12345, "tid": 19, "ts": 1749486031429403, "dur": 70, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1749486031429481, "dur": 423, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Collections.BurstCompatibilityGen.dll (+2 others)"}}, {"pid": 12345, "tid": 19, "ts": 1749486031429904, "dur": 171, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1749486031430079, "dur": 60883, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1749486031490963, "dur": 2655, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipeline.Universal.ShaderLibrary.dll (+pdb)"}}, {"pid": 12345, "tid": 19, "ts": 1749486031493619, "dur": 1036, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1749486031496597, "dur": 155, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Text.Encoding.dll"}}, {"pid": 12345, "tid": 19, "ts": 1749486031494664, "dur": 2176, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.PlasticSCM.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 19, "ts": 1749486031496841, "dur": 3340, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1749486031500189, "dur": 1896, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.2D.Common.Path.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 19, "ts": 1749486031502086, "dur": 87, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1749486031502508, "dur": 269, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1749486031502779, "dur": 371, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1749486031503178, "dur": 1747424, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1749486031380828, "dur": 14775, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1749486031395630, "dur": 637, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Tools\\ScriptUpdater\\ApiUpdater.MovedFromExtractor.exe"}}, {"pid": 12345, "tid": 20, "ts": 1749486031395604, "dur": 665, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.InputLegacyModule.dll_6FDD4B7A6EC84268.mvfrm"}}, {"pid": 12345, "tid": 20, "ts": 1749486031396269, "dur": 374, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1749486031396655, "dur": 78, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1749486031396741, "dur": 78, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting@1.9.4\\Editor\\VisualScripting.Core\\Dependencies\\DotNetZip\\Unity.VisualScripting.IonicZip.dll"}}, {"pid": 12345, "tid": 20, "ts": 1749486031396739, "dur": 83, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.VisualScripting.IonicZip.dll_840FAE5C8A141D5B.mvfrm"}}, {"pid": 12345, "tid": 20, "ts": 1749486031396822, "dur": 110, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1749486031396949, "dur": 71, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1749486031397037, "dur": 66, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1749486031397107, "dur": 63, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1749486031397201, "dur": 315, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1749486031397636, "dur": 84, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1749486031397775, "dur": 60, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.2D.IK.Runtime.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 20, "ts": 1749486031398016, "dur": 140, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1749486031398187, "dur": 154, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1749486031398345, "dur": 69, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/8899139255040401798.rsp"}}, {"pid": 12345, "tid": 20, "ts": 1749486031398415, "dur": 59, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1749486031398680, "dur": 1549, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1749486031400229, "dur": 1502, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1749486031401732, "dur": 1395, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1749486031403127, "dur": 1758, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1749486031404885, "dur": 1426, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1749486031406311, "dur": 1990, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1749486031408302, "dur": 1711, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1749486031410014, "dur": 1292, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1749486031411306, "dur": 967, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1749486031412274, "dur": 1625, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1749486031413899, "dur": 1926, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1749486031415825, "dur": 1826, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1749486031417652, "dur": 1762, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1749486031419414, "dur": 1576, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1749486031420991, "dur": 1455, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1749486031422447, "dur": 1281, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1749486031423729, "dur": 1221, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1749486031424950, "dur": 482, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1749486031425432, "dur": 517, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1749486031425950, "dur": 184, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.2D.Tilemap.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 20, "ts": 1749486031426134, "dur": 138, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1749486031426759, "dur": 87, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.ext.nunit@1.0.6\\net35\\unity-custom\\nunit.framework.dll"}}, {"pid": 12345, "tid": 20, "ts": 1749486031426274, "dur": 856, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.2D.Tilemap.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 20, "ts": 1749486031427131, "dur": 527, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1749486031427669, "dur": 57, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1749486031427730, "dur": 108, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.2D.Tilemap.Extras.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 20, "ts": 1749486031427838, "dur": 346, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1749486031428219, "dur": 547, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.2D.Tilemap.Extras.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 20, "ts": 1749486031428767, "dur": 146, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1749486031428919, "dur": 58, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1749486031428982, "dur": 322, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1749486031429304, "dur": 2704, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1749486031432010, "dur": 59, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.SettingsProvider.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 20, "ts": 1749486031432069, "dur": 56, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1749486031432128, "dur": 202, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.SettingsProvider.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 20, "ts": 1749486031432330, "dur": 97, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1749486031432469, "dur": 58513, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1749486031491038, "dur": 75, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.AudioModule.dll"}}, {"pid": 12345, "tid": 20, "ts": 1749486031490984, "dur": 2622, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.Universal.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 20, "ts": 1749486031493607, "dur": 1996, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1749486031495608, "dur": 2217, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.CollabProxy.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 20, "ts": 1749486031497826, "dur": 1074, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1749486031498904, "dur": 2123, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Mathematics.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 20, "ts": 1749486031501028, "dur": 620, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1749486031501829, "dur": 140, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1749486031502180, "dur": 352, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1749486031502799, "dur": 1004033, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1749486032506835, "dur": 115763, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1900b0aE.dag\\post-processed\\Assembly-CSharp-Editor.dll"}}, {"pid": 12345, "tid": 20, "ts": 1749486032506834, "dur": 115765, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Assembly-CSharp-Editor.dll"}}, {"pid": 12345, "tid": 20, "ts": 1749486032622600, "dur": 56, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1749486032622667, "dur": 627911, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1749486033256840, "dur": 3220, "ph": "X", "name": "ProfilerWriteOutput"}, {"pid": 18384, "tid": 606, "ts": 1749486033275628, "dur": 2895, "ph": "X", "name": "Wait for external events", "args": {"First to finish": "backend1.traceevents"}}, {"pid": 18384, "tid": 606, "ts": 1749486033278598, "dur": 3969, "ph": "X", "name": "backend1.traceevents", "args": {}}, {"pid": 18384, "tid": 606, "ts": 1749486033269400, "dur": 13934, "ph": "X", "name": "Write chrome-trace events", "args": {}}, {}]}