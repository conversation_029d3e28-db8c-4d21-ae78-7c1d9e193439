{"Instructions Readme": "1) Open Chrome or Edge, 2) go to chrome://tracing, 3) click Load, 4) navigate to this file.", "processInfo": {}, "traceEvents": [{"pid": 18384, "tid": -1, "ph": "M", "name": "process_name", "args": {"name": "BeeDriver"}}, {"pid": 18384, "tid": -1, "ph": "M", "name": "process_sort_index", "args": {"sort_index": "-2"}}, {"pid": 18384, "tid": 771, "ph": "M", "name": "thread_name", "args": {"name": "Thread Pool Worker"}}, {"pid": 18384, "tid": 771, "ts": 1749487378070065, "dur": 426, "ph": "X", "name": "ChromeTraceHeader", "args": {}}, {"pid": 18384, "tid": 771, "ts": 1749487378073061, "dur": 685, "ph": "X", "name": "Thread Pool Worker", "args": {}}, {"pid": 18384, "tid": 1, "ph": "M", "name": "thread_name", "args": {"name": ""}}, {"pid": 18384, "tid": 1, "ts": 1749487376511632, "dur": 4088, "ph": "X", "name": "<Add>b__0", "args": {}}, {"pid": 18384, "tid": 1, "ts": 1749487376515724, "dur": 25061, "ph": "X", "name": "<Add>b__0", "args": {}}, {"pid": 18384, "tid": 1, "ts": 1749487376540795, "dur": 57886, "ph": "X", "name": "WriteJson", "args": {}}, {"pid": 18384, "tid": 771, "ts": 1749487378073750, "dur": 9, "ph": "X", "name": "", "args": {}}, {"pid": 18384, "tid": 12884901888, "ph": "M", "name": "thread_name", "args": {"name": "ReadEntireBinlogFromIpcAsync"}}, {"pid": 18384, "tid": 12884901888, "ts": 1749487376510156, "dur": 6729, "ph": "X", "name": "WaitForConnectionAsync", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749487376516887, "dur": 1547399, "ph": "X", "name": "UpdateFromStreamAsync", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749487376517653, "dur": 1930, "ph": "X", "name": "ReadAsync 0", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749487376519589, "dur": 1040, "ph": "X", "name": "ProcessMessages 20485", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749487376520634, "dur": 258, "ph": "X", "name": "ReadAsync 20485", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749487376520894, "dur": 9, "ph": "X", "name": "ProcessMessages 20488", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749487376520904, "dur": 29, "ph": "X", "name": "ReadAsync 20488", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749487376520935, "dur": 1, "ph": "X", "name": "ProcessMessages 1403", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749487376520937, "dur": 398, "ph": "X", "name": "ReadAsync 1403", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749487376521338, "dur": 91, "ph": "X", "name": "ReadAsync 458", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749487376521431, "dur": 4, "ph": "X", "name": "ProcessMessages 9749", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749487376521437, "dur": 28, "ph": "X", "name": "ReadAsync 9749", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749487376521467, "dur": 34, "ph": "X", "name": "ReadAsync 567", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749487376521504, "dur": 29, "ph": "X", "name": "ReadAsync 638", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749487376521535, "dur": 1, "ph": "X", "name": "ProcessMessages 1342", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749487376521537, "dur": 20, "ph": "X", "name": "ReadAsync 1342", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749487376521560, "dur": 22, "ph": "X", "name": "ReadAsync 617", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749487376521584, "dur": 51, "ph": "X", "name": "ReadAsync 773", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749487376521638, "dur": 25, "ph": "X", "name": "ReadAsync 661", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749487376521665, "dur": 31, "ph": "X", "name": "ReadAsync 375", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749487376521698, "dur": 20, "ph": "X", "name": "ReadAsync 999", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749487376521722, "dur": 18, "ph": "X", "name": "ReadAsync 641", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749487376521742, "dur": 18, "ph": "X", "name": "ReadAsync 322", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749487376521762, "dur": 24, "ph": "X", "name": "ReadAsync 324", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749487376521789, "dur": 27, "ph": "X", "name": "ReadAsync 706", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749487376521819, "dur": 18, "ph": "X", "name": "ReadAsync 662", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749487376521840, "dur": 20, "ph": "X", "name": "ReadAsync 271", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749487376521863, "dur": 21, "ph": "X", "name": "ReadAsync 598", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749487376521887, "dur": 22, "ph": "X", "name": "ReadAsync 696", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749487376521912, "dur": 18, "ph": "X", "name": "ReadAsync 603", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749487376521932, "dur": 20, "ph": "X", "name": "ReadAsync 351", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749487376521955, "dur": 21, "ph": "X", "name": "ReadAsync 659", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749487376521979, "dur": 20, "ph": "X", "name": "ReadAsync 519", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749487376522001, "dur": 20, "ph": "X", "name": "ReadAsync 632", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749487376522024, "dur": 39, "ph": "X", "name": "ReadAsync 322", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749487376522066, "dur": 1, "ph": "X", "name": "ProcessMessages 658", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749487376522068, "dur": 34, "ph": "X", "name": "ReadAsync 658", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749487376522104, "dur": 1, "ph": "X", "name": "ProcessMessages 1241", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749487376522107, "dur": 22, "ph": "X", "name": "ReadAsync 1241", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749487376522133, "dur": 1, "ph": "X", "name": "ProcessMessages 493", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749487376522137, "dur": 28, "ph": "X", "name": "ReadAsync 493", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749487376522166, "dur": 1, "ph": "X", "name": "ProcessMessages 826", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749487376522168, "dur": 20, "ph": "X", "name": "ReadAsync 826", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749487376522191, "dur": 40, "ph": "X", "name": "ReadAsync 518", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749487376522234, "dur": 22, "ph": "X", "name": "ReadAsync 848", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749487376522259, "dur": 23, "ph": "X", "name": "ReadAsync 907", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749487376522285, "dur": 25, "ph": "X", "name": "ReadAsync 802", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749487376522312, "dur": 22, "ph": "X", "name": "ReadAsync 411", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749487376522337, "dur": 22, "ph": "X", "name": "ReadAsync 1039", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749487376522362, "dur": 18, "ph": "X", "name": "ReadAsync 793", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749487376522383, "dur": 23, "ph": "X", "name": "ReadAsync 358", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749487376522409, "dur": 22, "ph": "X", "name": "ReadAsync 989", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749487376522434, "dur": 21, "ph": "X", "name": "ReadAsync 672", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749487376522457, "dur": 45, "ph": "X", "name": "ReadAsync 654", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749487376522505, "dur": 32, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749487376522539, "dur": 1, "ph": "X", "name": "ProcessMessages 940", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749487376522540, "dur": 23, "ph": "X", "name": "ReadAsync 940", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749487376522565, "dur": 1, "ph": "X", "name": "ProcessMessages 561", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749487376522566, "dur": 21, "ph": "X", "name": "ReadAsync 561", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749487376522590, "dur": 23, "ph": "X", "name": "ReadAsync 538", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749487376522616, "dur": 21, "ph": "X", "name": "ReadAsync 681", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749487376522639, "dur": 20, "ph": "X", "name": "ReadAsync 610", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749487376522663, "dur": 18, "ph": "X", "name": "ReadAsync 591", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749487376522684, "dur": 23, "ph": "X", "name": "ReadAsync 301", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749487376522709, "dur": 21, "ph": "X", "name": "ReadAsync 592", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749487376522733, "dur": 21, "ph": "X", "name": "ReadAsync 711", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749487376522755, "dur": 1, "ph": "X", "name": "ProcessMessages 693", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749487376522757, "dur": 18, "ph": "X", "name": "ReadAsync 693", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749487376522778, "dur": 109, "ph": "X", "name": "ReadAsync 319", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749487376522889, "dur": 23, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749487376522915, "dur": 39, "ph": "X", "name": "ReadAsync 805", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749487376522957, "dur": 26, "ph": "X", "name": "ReadAsync 712", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749487376522986, "dur": 23, "ph": "X", "name": "ReadAsync 632", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749487376523011, "dur": 24, "ph": "X", "name": "ReadAsync 351", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749487376523038, "dur": 20, "ph": "X", "name": "ReadAsync 868", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749487376523061, "dur": 19, "ph": "X", "name": "ReadAsync 623", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749487376523083, "dur": 20, "ph": "X", "name": "ReadAsync 319", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749487376523106, "dur": 20, "ph": "X", "name": "ReadAsync 541", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749487376523129, "dur": 21, "ph": "X", "name": "ReadAsync 677", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749487376523152, "dur": 18, "ph": "X", "name": "ReadAsync 569", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749487376523172, "dur": 20, "ph": "X", "name": "ReadAsync 286", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749487376523195, "dur": 20, "ph": "X", "name": "ReadAsync 586", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749487376523217, "dur": 20, "ph": "X", "name": "ReadAsync 615", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749487376523240, "dur": 18, "ph": "X", "name": "ReadAsync 701", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749487376523261, "dur": 23, "ph": "X", "name": "ReadAsync 413", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749487376523286, "dur": 20, "ph": "X", "name": "ReadAsync 619", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749487376523309, "dur": 19, "ph": "X", "name": "ReadAsync 633", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749487376523331, "dur": 18, "ph": "X", "name": "ReadAsync 573", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749487376523352, "dur": 19, "ph": "X", "name": "ReadAsync 292", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749487376523373, "dur": 27, "ph": "X", "name": "ReadAsync 512", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749487376523403, "dur": 19, "ph": "X", "name": "ReadAsync 935", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749487376523424, "dur": 17, "ph": "X", "name": "ReadAsync 675", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749487376523444, "dur": 18, "ph": "X", "name": "ReadAsync 325", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749487376523465, "dur": 21, "ph": "X", "name": "ReadAsync 557", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749487376523488, "dur": 19, "ph": "X", "name": "ReadAsync 670", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749487376523509, "dur": 35, "ph": "X", "name": "ReadAsync 378", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749487376523547, "dur": 27, "ph": "X", "name": "ReadAsync 578", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749487376523576, "dur": 23, "ph": "X", "name": "ReadAsync 357", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749487376523602, "dur": 23, "ph": "X", "name": "ReadAsync 871", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749487376523628, "dur": 18, "ph": "X", "name": "ReadAsync 673", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749487376523648, "dur": 20, "ph": "X", "name": "ReadAsync 304", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749487376523670, "dur": 20, "ph": "X", "name": "ReadAsync 666", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749487376523693, "dur": 22, "ph": "X", "name": "ReadAsync 611", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749487376523717, "dur": 17, "ph": "X", "name": "ReadAsync 434", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749487376523736, "dur": 25, "ph": "X", "name": "ReadAsync 274", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749487376523764, "dur": 20, "ph": "X", "name": "ReadAsync 668", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749487376523787, "dur": 19, "ph": "X", "name": "ReadAsync 692", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749487376523809, "dur": 17, "ph": "X", "name": "ReadAsync 587", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749487376523828, "dur": 21, "ph": "X", "name": "ReadAsync 214", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749487376523852, "dur": 20, "ph": "X", "name": "ReadAsync 624", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749487376523873, "dur": 2, "ph": "X", "name": "ProcessMessages 692", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749487376523876, "dur": 21, "ph": "X", "name": "ReadAsync 692", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749487376523900, "dur": 18, "ph": "X", "name": "ReadAsync 444", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749487376523920, "dur": 23, "ph": "X", "name": "ReadAsync 313", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749487376523945, "dur": 22, "ph": "X", "name": "ReadAsync 364", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749487376523970, "dur": 20, "ph": "X", "name": "ReadAsync 716", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749487376523992, "dur": 21, "ph": "X", "name": "ReadAsync 704", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749487376524016, "dur": 19, "ph": "X", "name": "ReadAsync 531", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749487376524037, "dur": 26, "ph": "X", "name": "ReadAsync 562", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749487376524066, "dur": 21, "ph": "X", "name": "ReadAsync 665", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749487376524089, "dur": 18, "ph": "X", "name": "ReadAsync 634", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749487376524111, "dur": 25, "ph": "X", "name": "ReadAsync 322", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749487376524138, "dur": 100, "ph": "X", "name": "ReadAsync 766", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749487376524242, "dur": 1, "ph": "X", "name": "ProcessMessages 258", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749487376524244, "dur": 106, "ph": "X", "name": "ReadAsync 258", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749487376524353, "dur": 2, "ph": "X", "name": "ProcessMessages 3169", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749487376524356, "dur": 162, "ph": "X", "name": "ReadAsync 3169", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749487376524521, "dur": 1, "ph": "X", "name": "ProcessMessages 607", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749487376524524, "dur": 92, "ph": "X", "name": "ReadAsync 607", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749487376524618, "dur": 3, "ph": "X", "name": "ProcessMessages 4241", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749487376524622, "dur": 25, "ph": "X", "name": "ReadAsync 4241", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749487376524650, "dur": 26, "ph": "X", "name": "ReadAsync 715", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749487376524678, "dur": 18, "ph": "X", "name": "ReadAsync 881", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749487376524699, "dur": 21, "ph": "X", "name": "ReadAsync 322", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749487376524723, "dur": 20, "ph": "X", "name": "ReadAsync 668", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749487376524746, "dur": 22, "ph": "X", "name": "ReadAsync 697", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749487376524772, "dur": 18, "ph": "X", "name": "ReadAsync 460", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749487376524793, "dur": 31, "ph": "X", "name": "ReadAsync 292", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749487376524827, "dur": 20, "ph": "X", "name": "ReadAsync 922", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749487376524850, "dur": 44, "ph": "X", "name": "ReadAsync 499", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749487376524896, "dur": 1, "ph": "X", "name": "ProcessMessages 564", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749487376524898, "dur": 31, "ph": "X", "name": "ReadAsync 564", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749487376524932, "dur": 21, "ph": "X", "name": "ReadAsync 1141", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749487376524956, "dur": 19, "ph": "X", "name": "ReadAsync 484", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749487376524978, "dur": 18, "ph": "X", "name": "ReadAsync 580", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749487376524998, "dur": 22, "ph": "X", "name": "ReadAsync 354", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749487376525023, "dur": 21, "ph": "X", "name": "ReadAsync 642", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749487376525046, "dur": 18, "ph": "X", "name": "ReadAsync 618", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749487376525086, "dur": 154, "ph": "X", "name": "ReadAsync 194", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749487376525242, "dur": 2, "ph": "X", "name": "ProcessMessages 1244", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749487376525245, "dur": 134, "ph": "X", "name": "ReadAsync 1244", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749487376525381, "dur": 2, "ph": "X", "name": "ProcessMessages 3551", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749487376525385, "dur": 50, "ph": "X", "name": "ReadAsync 3551", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749487376525436, "dur": 2, "ph": "X", "name": "ProcessMessages 3241", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749487376525439, "dur": 19, "ph": "X", "name": "ReadAsync 3241", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749487376525461, "dur": 28, "ph": "X", "name": "ReadAsync 538", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749487376525492, "dur": 27, "ph": "X", "name": "ReadAsync 877", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749487376525521, "dur": 23, "ph": "X", "name": "ReadAsync 310", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749487376525547, "dur": 18, "ph": "X", "name": "ReadAsync 669", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749487376525567, "dur": 19, "ph": "X", "name": "ReadAsync 340", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749487376525589, "dur": 23, "ph": "X", "name": "ReadAsync 844", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749487376525615, "dur": 21, "ph": "X", "name": "ReadAsync 772", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749487376525638, "dur": 18, "ph": "X", "name": "ReadAsync 741", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749487376525658, "dur": 20, "ph": "X", "name": "ReadAsync 376", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749487376525681, "dur": 24, "ph": "X", "name": "ReadAsync 709", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749487376525708, "dur": 16, "ph": "X", "name": "ReadAsync 717", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749487376525726, "dur": 26, "ph": "X", "name": "ReadAsync 21", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749487376525755, "dur": 25, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749487376525783, "dur": 1, "ph": "X", "name": "ProcessMessages 988", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749487376525787, "dur": 26, "ph": "X", "name": "ReadAsync 988", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749487376525815, "dur": 1, "ph": "X", "name": "ProcessMessages 626", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749487376525816, "dur": 23, "ph": "X", "name": "ReadAsync 626", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749487376525843, "dur": 21, "ph": "X", "name": "ReadAsync 795", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749487376525868, "dur": 20, "ph": "X", "name": "ReadAsync 564", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749487376525891, "dur": 33, "ph": "X", "name": "ReadAsync 345", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749487376525927, "dur": 27, "ph": "X", "name": "ReadAsync 907", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749487376525957, "dur": 20, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749487376525980, "dur": 17, "ph": "X", "name": "ReadAsync 573", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749487376525999, "dur": 20, "ph": "X", "name": "ReadAsync 280", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749487376526022, "dur": 21, "ph": "X", "name": "ReadAsync 647", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749487376526046, "dur": 23, "ph": "X", "name": "ReadAsync 671", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749487376526073, "dur": 20, "ph": "X", "name": "ReadAsync 660", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749487376526095, "dur": 121, "ph": "X", "name": "ReadAsync 227", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749487376526219, "dur": 1, "ph": "X", "name": "ProcessMessages 661", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749487376526221, "dur": 40, "ph": "X", "name": "ReadAsync 661", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749487376526264, "dur": 1, "ph": "X", "name": "ProcessMessages 2509", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749487376526267, "dur": 25, "ph": "X", "name": "ReadAsync 2509", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749487376526294, "dur": 1, "ph": "X", "name": "ProcessMessages 927", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749487376526296, "dur": 23, "ph": "X", "name": "ReadAsync 927", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749487376526320, "dur": 1, "ph": "X", "name": "ProcessMessages 687", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749487376526322, "dur": 48, "ph": "X", "name": "ReadAsync 687", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749487376526373, "dur": 26, "ph": "X", "name": "ReadAsync 917", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749487376526402, "dur": 25, "ph": "X", "name": "ReadAsync 348", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749487376526429, "dur": 18, "ph": "X", "name": "ReadAsync 806", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749487376526450, "dur": 21, "ph": "X", "name": "ReadAsync 239", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749487376526473, "dur": 1, "ph": "X", "name": "ProcessMessages 537", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749487376526474, "dur": 21, "ph": "X", "name": "ReadAsync 537", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749487376526498, "dur": 28, "ph": "X", "name": "ReadAsync 561", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749487376526528, "dur": 22, "ph": "X", "name": "ReadAsync 613", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749487376526553, "dur": 24, "ph": "X", "name": "ReadAsync 644", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749487376526580, "dur": 20, "ph": "X", "name": "ReadAsync 503", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749487376526603, "dur": 21, "ph": "X", "name": "ReadAsync 227", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749487376526626, "dur": 18, "ph": "X", "name": "ReadAsync 563", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749487376526647, "dur": 20, "ph": "X", "name": "ReadAsync 277", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749487376526670, "dur": 18, "ph": "X", "name": "ReadAsync 526", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749487376526690, "dur": 17, "ph": "X", "name": "ReadAsync 187", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749487376526710, "dur": 17, "ph": "X", "name": "ReadAsync 53", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749487376526730, "dur": 20, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749487376526752, "dur": 20, "ph": "X", "name": "ReadAsync 404", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749487376526775, "dur": 62, "ph": "X", "name": "ReadAsync 573", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749487376526839, "dur": 29, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749487376526871, "dur": 19, "ph": "X", "name": "ReadAsync 815", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749487376526893, "dur": 42, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749487376526937, "dur": 23, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749487376526963, "dur": 22, "ph": "X", "name": "ReadAsync 174", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749487376526987, "dur": 22, "ph": "X", "name": "ReadAsync 298", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749487376527011, "dur": 49, "ph": "X", "name": "ReadAsync 582", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749487376527063, "dur": 23, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749487376527088, "dur": 21, "ph": "X", "name": "ReadAsync 497", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749487376527112, "dur": 18, "ph": "X", "name": "ReadAsync 488", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749487376527133, "dur": 36, "ph": "X", "name": "ReadAsync 57", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749487376527171, "dur": 20, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749487376527195, "dur": 20, "ph": "X", "name": "ReadAsync 585", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749487376527217, "dur": 44, "ph": "X", "name": "ReadAsync 466", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749487376527263, "dur": 23, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749487376527289, "dur": 22, "ph": "X", "name": "ReadAsync 695", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749487376527314, "dur": 40, "ph": "X", "name": "ReadAsync 363", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749487376527357, "dur": 20, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749487376527380, "dur": 19, "ph": "X", "name": "ReadAsync 430", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749487376527402, "dur": 47, "ph": "X", "name": "ReadAsync 552", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749487376527451, "dur": 20, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749487376527474, "dur": 21, "ph": "X", "name": "ReadAsync 410", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749487376527498, "dur": 45, "ph": "X", "name": "ReadAsync 588", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749487376527545, "dur": 24, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749487376527572, "dur": 19, "ph": "X", "name": "ReadAsync 621", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749487376527594, "dur": 43, "ph": "X", "name": "ReadAsync 485", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749487376527640, "dur": 19, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749487376527662, "dur": 20, "ph": "X", "name": "ReadAsync 593", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749487376527684, "dur": 43, "ph": "X", "name": "ReadAsync 455", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749487376527729, "dur": 19, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749487376527751, "dur": 23, "ph": "X", "name": "ReadAsync 448", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749487376527777, "dur": 48, "ph": "X", "name": "ReadAsync 588", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749487376527827, "dur": 24, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749487376527854, "dur": 19, "ph": "X", "name": "ReadAsync 809", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749487376527876, "dur": 39, "ph": "X", "name": "ReadAsync 311", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749487376527917, "dur": 35, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749487376527955, "dur": 17, "ph": "X", "name": "ReadAsync 1132", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749487376527974, "dur": 38, "ph": "X", "name": "ReadAsync 57", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749487376528014, "dur": 51, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749487376528067, "dur": 1, "ph": "X", "name": "ProcessMessages 951", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749487376528068, "dur": 68, "ph": "X", "name": "ReadAsync 951", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749487376528139, "dur": 22, "ph": "X", "name": "ReadAsync 332", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749487376528164, "dur": 18, "ph": "X", "name": "ReadAsync 1012", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749487376528185, "dur": 37, "ph": "X", "name": "ReadAsync 237", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749487376528224, "dur": 23, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749487376528250, "dur": 19, "ph": "X", "name": "ReadAsync 734", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749487376528272, "dur": 39, "ph": "X", "name": "ReadAsync 360", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749487376528313, "dur": 23, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749487376528338, "dur": 19, "ph": "X", "name": "ReadAsync 607", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749487376528360, "dur": 40, "ph": "X", "name": "ReadAsync 465", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749487376528403, "dur": 24, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749487376528431, "dur": 19, "ph": "X", "name": "ReadAsync 737", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749487376528452, "dur": 42, "ph": "X", "name": "ReadAsync 293", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749487376528497, "dur": 21, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749487376528521, "dur": 22, "ph": "X", "name": "ReadAsync 421", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749487376528548, "dur": 42, "ph": "X", "name": "ReadAsync 561", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749487376528593, "dur": 20, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749487376528616, "dur": 22, "ph": "X", "name": "ReadAsync 573", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749487376528640, "dur": 38, "ph": "X", "name": "ReadAsync 476", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749487376528681, "dur": 22, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749487376528706, "dur": 20, "ph": "X", "name": "ReadAsync 545", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749487376528729, "dur": 38, "ph": "X", "name": "ReadAsync 434", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749487376528770, "dur": 24, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749487376528796, "dur": 20, "ph": "X", "name": "ReadAsync 427", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749487376528820, "dur": 40, "ph": "X", "name": "ReadAsync 591", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749487376528863, "dur": 30, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749487376528896, "dur": 19, "ph": "X", "name": "ReadAsync 820", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749487376528917, "dur": 111, "ph": "X", "name": "ReadAsync 237", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749487376529031, "dur": 29, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749487376529062, "dur": 1, "ph": "X", "name": "ProcessMessages 1517", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749487376529064, "dur": 45, "ph": "X", "name": "ReadAsync 1517", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749487376529111, "dur": 39, "ph": "X", "name": "ReadAsync 613", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749487376529153, "dur": 36, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749487376529192, "dur": 19, "ph": "X", "name": "ReadAsync 905", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749487376529213, "dur": 16, "ph": "X", "name": "ReadAsync 457", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749487376529232, "dur": 34, "ph": "X", "name": "ReadAsync 141", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749487376529269, "dur": 19, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749487376529290, "dur": 21, "ph": "X", "name": "ReadAsync 454", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749487376529314, "dur": 23, "ph": "X", "name": "ReadAsync 809", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749487376529339, "dur": 2, "ph": "X", "name": "ProcessMessages 717", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749487376529342, "dur": 20, "ph": "X", "name": "ReadAsync 717", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749487376529364, "dur": 2, "ph": "X", "name": "ProcessMessages 524", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749487376529367, "dur": 21, "ph": "X", "name": "ReadAsync 524", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749487376529391, "dur": 19, "ph": "X", "name": "ReadAsync 175", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749487376529412, "dur": 34, "ph": "X", "name": "ReadAsync 138", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749487376529449, "dur": 29, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749487376529481, "dur": 21, "ph": "X", "name": "ReadAsync 392", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749487376529505, "dur": 27, "ph": "X", "name": "ReadAsync 563", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749487376529534, "dur": 40, "ph": "X", "name": "ReadAsync 66", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749487376529576, "dur": 23, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749487376529603, "dur": 19, "ph": "X", "name": "ReadAsync 787", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749487376529625, "dur": 44, "ph": "X", "name": "ReadAsync 313", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749487376529672, "dur": 22, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749487376529697, "dur": 16, "ph": "X", "name": "ReadAsync 731", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749487376529715, "dur": 44, "ph": "X", "name": "ReadAsync 363", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749487376529763, "dur": 21, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749487376529787, "dur": 21, "ph": "X", "name": "ReadAsync 532", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749487376529810, "dur": 41, "ph": "X", "name": "ReadAsync 655", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749487376529854, "dur": 21, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749487376529878, "dur": 22, "ph": "X", "name": "ReadAsync 603", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749487376529903, "dur": 43, "ph": "X", "name": "ReadAsync 574", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749487376529949, "dur": 20, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749487376529972, "dur": 20, "ph": "X", "name": "ReadAsync 565", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749487376529994, "dur": 43, "ph": "X", "name": "ReadAsync 661", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749487376530040, "dur": 23, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749487376530065, "dur": 1, "ph": "X", "name": "ProcessMessages 769", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749487376530067, "dur": 61, "ph": "X", "name": "ReadAsync 769", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749487376530130, "dur": 1, "ph": "X", "name": "ProcessMessages 364", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749487376530133, "dur": 23, "ph": "X", "name": "ReadAsync 364", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749487376530158, "dur": 1, "ph": "X", "name": "ProcessMessages 242", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749487376530160, "dur": 24, "ph": "X", "name": "ReadAsync 242", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749487376530185, "dur": 1, "ph": "X", "name": "ProcessMessages 795", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749487376530187, "dur": 17, "ph": "X", "name": "ReadAsync 795", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749487376530207, "dur": 33, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749487376530242, "dur": 22, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749487376530266, "dur": 1, "ph": "X", "name": "ProcessMessages 871", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749487376530267, "dur": 19, "ph": "X", "name": "ReadAsync 871", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749487376530289, "dur": 32, "ph": "X", "name": "ReadAsync 237", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749487376530324, "dur": 20, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749487376530347, "dur": 22, "ph": "X", "name": "ReadAsync 505", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749487376530372, "dur": 46, "ph": "X", "name": "ReadAsync 597", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749487376530421, "dur": 19, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749487376530443, "dur": 23, "ph": "X", "name": "ReadAsync 163", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749487376530469, "dur": 20, "ph": "X", "name": "ReadAsync 321", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749487376530490, "dur": 1, "ph": "X", "name": "ProcessMessages 601", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749487376530492, "dur": 37, "ph": "X", "name": "ReadAsync 601", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749487376530532, "dur": 35, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749487376530569, "dur": 41, "ph": "X", "name": "ReadAsync 1057", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749487376530612, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749487376530613, "dur": 76, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749487376530692, "dur": 1, "ph": "X", "name": "ProcessMessages 722", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749487376530694, "dur": 25, "ph": "X", "name": "ReadAsync 722", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749487376530721, "dur": 1, "ph": "X", "name": "ProcessMessages 613", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749487376530722, "dur": 21, "ph": "X", "name": "ReadAsync 613", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749487376530745, "dur": 1, "ph": "X", "name": "ProcessMessages 553", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749487376530747, "dur": 30, "ph": "X", "name": "ReadAsync 553", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749487376530778, "dur": 1, "ph": "X", "name": "ProcessMessages 924", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749487376530780, "dur": 23, "ph": "X", "name": "ReadAsync 924", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749487376530805, "dur": 1, "ph": "X", "name": "ProcessMessages 909", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749487376530806, "dur": 33, "ph": "X", "name": "ReadAsync 909", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749487376530841, "dur": 20, "ph": "X", "name": "ReadAsync 358", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749487376530864, "dur": 24, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749487376530891, "dur": 26, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749487376530920, "dur": 24, "ph": "X", "name": "ReadAsync 852", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749487376530945, "dur": 1, "ph": "X", "name": "ProcessMessages 760", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749487376530947, "dur": 20, "ph": "X", "name": "ReadAsync 760", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749487376530970, "dur": 20, "ph": "X", "name": "ReadAsync 415", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749487376530993, "dur": 19, "ph": "X", "name": "ReadAsync 500", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749487376531019, "dur": 45, "ph": "X", "name": "ReadAsync 316", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749487376531066, "dur": 140, "ph": "X", "name": "ReadAsync 237", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749487376531209, "dur": 1, "ph": "X", "name": "ProcessMessages 1047", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749487376531211, "dur": 29, "ph": "X", "name": "ReadAsync 1047", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749487376531243, "dur": 1, "ph": "X", "name": "ProcessMessages 1266", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749487376531245, "dur": 18, "ph": "X", "name": "ReadAsync 1266", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749487376531266, "dur": 27, "ph": "X", "name": "ReadAsync 44", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749487376531296, "dur": 18, "ph": "X", "name": "ReadAsync 849", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749487376531317, "dur": 42, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749487376531362, "dur": 67, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749487376531431, "dur": 1, "ph": "X", "name": "ProcessMessages 745", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749487376531433, "dur": 28, "ph": "X", "name": "ReadAsync 745", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749487376531464, "dur": 24, "ph": "X", "name": "ReadAsync 594", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749487376531491, "dur": 18, "ph": "X", "name": "ReadAsync 623", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749487376531513, "dur": 40, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749487376531557, "dur": 26, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749487376531584, "dur": 1, "ph": "X", "name": "ProcessMessages 829", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749487376531587, "dur": 18, "ph": "X", "name": "ReadAsync 829", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749487376531608, "dur": 32, "ph": "X", "name": "ReadAsync 237", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749487376531643, "dur": 33, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749487376531678, "dur": 18, "ph": "X", "name": "ReadAsync 824", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749487376531699, "dur": 45, "ph": "X", "name": "ReadAsync 261", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749487376531747, "dur": 20, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749487376531769, "dur": 1, "ph": "X", "name": "ProcessMessages 660", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749487376531771, "dur": 19, "ph": "X", "name": "ReadAsync 660", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749487376531793, "dur": 40, "ph": "X", "name": "ReadAsync 500", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749487376531836, "dur": 22, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749487376531861, "dur": 19, "ph": "X", "name": "ReadAsync 855", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749487376531884, "dur": 37, "ph": "X", "name": "ReadAsync 376", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749487376531923, "dur": 118, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749487376532043, "dur": 1, "ph": "X", "name": "ProcessMessages 821", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749487376532045, "dur": 31, "ph": "X", "name": "ReadAsync 821", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749487376532079, "dur": 1, "ph": "X", "name": "ProcessMessages 1486", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749487376532081, "dur": 22, "ph": "X", "name": "ReadAsync 1486", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749487376532106, "dur": 18, "ph": "X", "name": "ReadAsync 534", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749487376532126, "dur": 22, "ph": "X", "name": "ReadAsync 114", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749487376532151, "dur": 26, "ph": "X", "name": "ReadAsync 622", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749487376532180, "dur": 18, "ph": "X", "name": "ReadAsync 291", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749487376532201, "dur": 27, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749487376532230, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749487376532231, "dur": 86, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749487376532319, "dur": 1, "ph": "X", "name": "ProcessMessages 1010", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749487376532321, "dur": 29, "ph": "X", "name": "ReadAsync 1010", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749487376532352, "dur": 19, "ph": "X", "name": "ReadAsync 609", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749487376532375, "dur": 43, "ph": "X", "name": "ReadAsync 472", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749487376532421, "dur": 20, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749487376532446, "dur": 21, "ph": "X", "name": "ReadAsync 505", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749487376532469, "dur": 40, "ph": "X", "name": "ReadAsync 588", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749487376532512, "dur": 28, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749487376532543, "dur": 18, "ph": "X", "name": "ReadAsync 901", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749487376532564, "dur": 40, "ph": "X", "name": "ReadAsync 237", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749487376532607, "dur": 27, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749487376532636, "dur": 22, "ph": "X", "name": "ReadAsync 806", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749487376532665, "dur": 22, "ph": "X", "name": "ReadAsync 754", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749487376532690, "dur": 21, "ph": "X", "name": "ReadAsync 582", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749487376532714, "dur": 17, "ph": "X", "name": "ReadAsync 265", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749487376532733, "dur": 38, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749487376532774, "dur": 23, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749487376532799, "dur": 20, "ph": "X", "name": "ReadAsync 676", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749487376532822, "dur": 19, "ph": "X", "name": "ReadAsync 470", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749487376532844, "dur": 19, "ph": "X", "name": "ReadAsync 639", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749487376532866, "dur": 18, "ph": "X", "name": "ReadAsync 570", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749487376532886, "dur": 17, "ph": "X", "name": "ReadAsync 166", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749487376532906, "dur": 32, "ph": "X", "name": "ReadAsync 237", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749487376532940, "dur": 18, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749487376532961, "dur": 302, "ph": "X", "name": "ReadAsync 224", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749487376533267, "dur": 24, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749487376533293, "dur": 237, "ph": "X", "name": "ProcessMessages 52", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749487376533532, "dur": 62, "ph": "X", "name": "ReadAsync 52", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749487376533597, "dur": 2, "ph": "X", "name": "ProcessMessages 336", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749487376533601, "dur": 35, "ph": "X", "name": "ReadAsync 336", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749487376533638, "dur": 1, "ph": "X", "name": "ProcessMessages 156", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749487376533641, "dur": 25, "ph": "X", "name": "ReadAsync 156", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749487376533668, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749487376533670, "dur": 25, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749487376533698, "dur": 1, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749487376533699, "dur": 21, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749487376533722, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749487376533725, "dur": 37, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749487376533765, "dur": 1, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749487376533768, "dur": 25, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749487376533795, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749487376533797, "dur": 24, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749487376533825, "dur": 22, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749487376533850, "dur": 31, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749487376533882, "dur": 1, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749487376533885, "dur": 31, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749487376533919, "dur": 24, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749487376533946, "dur": 28, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749487376533976, "dur": 133, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749487376534113, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749487376534115, "dur": 39, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749487376534158, "dur": 1, "ph": "X", "name": "ProcessMessages 340", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749487376534161, "dur": 26, "ph": "X", "name": "ReadAsync 340", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749487376534189, "dur": 21, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749487376534213, "dur": 23, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749487376534238, "dur": 22, "ph": "X", "name": "ReadAsync 60", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749487376534264, "dur": 32, "ph": "X", "name": "ReadAsync 68", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749487376534297, "dur": 1, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749487376534299, "dur": 31, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749487376534332, "dur": 1, "ph": "X", "name": "ProcessMessages 160", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749487376534334, "dur": 21, "ph": "X", "name": "ReadAsync 160", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749487376534357, "dur": 1, "ph": "X", "name": "ProcessMessages 96", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749487376534359, "dur": 23, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749487376534383, "dur": 1, "ph": "X", "name": "ProcessMessages 160", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749487376534385, "dur": 23, "ph": "X", "name": "ReadAsync 160", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749487376534410, "dur": 1, "ph": "X", "name": "ProcessMessages 96", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749487376534412, "dur": 21, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749487376534435, "dur": 18, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749487376534456, "dur": 21, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749487376534479, "dur": 1, "ph": "X", "name": "ProcessMessages 128", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749487376534481, "dur": 24, "ph": "X", "name": "ReadAsync 128", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749487376534508, "dur": 1, "ph": "X", "name": "ProcessMessages 128", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749487376534510, "dur": 37, "ph": "X", "name": "ReadAsync 128", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749487376534550, "dur": 1, "ph": "X", "name": "ProcessMessages 96", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749487376534552, "dur": 31, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749487376534585, "dur": 2, "ph": "X", "name": "ProcessMessages 208", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749487376534587, "dur": 23, "ph": "X", "name": "ReadAsync 208", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749487376534612, "dur": 1, "ph": "X", "name": "ProcessMessages 108", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749487376534614, "dur": 21, "ph": "X", "name": "ReadAsync 108", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749487376534637, "dur": 1, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749487376534639, "dur": 19, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749487376534660, "dur": 1, "ph": "X", "name": "ProcessMessages 84", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749487376534661, "dur": 21, "ph": "X", "name": "ReadAsync 84", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749487376534684, "dur": 1, "ph": "X", "name": "ProcessMessages 96", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749487376534687, "dur": 28, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749487376534718, "dur": 32, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749487376534752, "dur": 1, "ph": "X", "name": "ProcessMessages 192", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749487376534754, "dur": 46, "ph": "X", "name": "ReadAsync 192", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749487376534804, "dur": 1, "ph": "X", "name": "ProcessMessages 96", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749487376534806, "dur": 48, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749487376534859, "dur": 1, "ph": "X", "name": "ProcessMessages 144", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749487376534861, "dur": 30, "ph": "X", "name": "ReadAsync 144", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749487376534894, "dur": 1, "ph": "X", "name": "ProcessMessages 112", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749487376534895, "dur": 30, "ph": "X", "name": "ReadAsync 112", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749487376534928, "dur": 1, "ph": "X", "name": "ProcessMessages 144", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749487376534930, "dur": 28, "ph": "X", "name": "ReadAsync 144", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749487376534961, "dur": 47, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749487376535010, "dur": 1, "ph": "X", "name": "ProcessMessages 160", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749487376535012, "dur": 31, "ph": "X", "name": "ReadAsync 160", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749487376535044, "dur": 1, "ph": "X", "name": "ProcessMessages 112", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749487376535046, "dur": 22, "ph": "X", "name": "ReadAsync 112", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749487376535070, "dur": 1, "ph": "X", "name": "ProcessMessages 128", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749487376535071, "dur": 29, "ph": "X", "name": "ReadAsync 128", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749487376535104, "dur": 1, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749487376535107, "dur": 43, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749487376535152, "dur": 1, "ph": "X", "name": "ProcessMessages 176", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749487376535155, "dur": 30, "ph": "X", "name": "ReadAsync 176", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749487376535188, "dur": 1, "ph": "X", "name": "ProcessMessages 112", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749487376535190, "dur": 25, "ph": "X", "name": "ReadAsync 112", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749487376535217, "dur": 1, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749487376535219, "dur": 20, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749487376535241, "dur": 1, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749487376535242, "dur": 25, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749487376535269, "dur": 1, "ph": "X", "name": "ProcessMessages 144", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749487376535271, "dur": 30, "ph": "X", "name": "ReadAsync 144", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749487376535304, "dur": 1, "ph": "X", "name": "ProcessMessages 144", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749487376535307, "dur": 26, "ph": "X", "name": "ReadAsync 144", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749487376535335, "dur": 1, "ph": "X", "name": "ProcessMessages 140", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749487376535337, "dur": 26, "ph": "X", "name": "ReadAsync 140", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749487376535365, "dur": 1, "ph": "X", "name": "ProcessMessages 84", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749487376535368, "dur": 20, "ph": "X", "name": "ReadAsync 84", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749487376535390, "dur": 1, "ph": "X", "name": "ProcessMessages 96", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749487376535392, "dur": 32, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749487376535426, "dur": 1, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749487376535428, "dur": 23, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749487376535453, "dur": 1, "ph": "X", "name": "ProcessMessages 128", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749487376535455, "dur": 19, "ph": "X", "name": "ReadAsync 128", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749487376535476, "dur": 21, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749487376535499, "dur": 1, "ph": "X", "name": "ProcessMessages 96", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749487376535501, "dur": 20, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749487376535522, "dur": 1, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749487376535524, "dur": 18, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749487376535544, "dur": 1, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749487376535547, "dur": 19, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749487376535570, "dur": 21, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749487376535592, "dur": 1, "ph": "X", "name": "ProcessMessages 96", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749487376535594, "dur": 19, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749487376535616, "dur": 23, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749487376535641, "dur": 1, "ph": "X", "name": "ProcessMessages 96", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749487376535643, "dur": 24, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749487376535671, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749487376535673, "dur": 37, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749487376535715, "dur": 29, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749487376535749, "dur": 36, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749487376535788, "dur": 29, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749487376535821, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749487376535823, "dur": 13012, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749487376548841, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749487376548843, "dur": 36, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749487376548882, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749487376548884, "dur": 775, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749487376549665, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749487376549667, "dur": 47, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749487376549719, "dur": 50, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749487376549774, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749487376549776, "dur": 25, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749487376549805, "dur": 52, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749487376549861, "dur": 29, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749487376549894, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749487376549896, "dur": 42, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749487376549942, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749487376549943, "dur": 128, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749487376550076, "dur": 20, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749487376550099, "dur": 17270, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749487376567377, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749487376567381, "dur": 128, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749487376567512, "dur": 461, "ph": "X", "name": "ProcessMessages 3004", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749487376567976, "dur": 65639, "ph": "X", "name": "ReadAsync 3004", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749487376633623, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749487376633626, "dur": 91, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749487376633723, "dur": 1837, "ph": "X", "name": "ProcessMessages 199", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749487376635563, "dur": 7172, "ph": "X", "name": "ReadAsync 199", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749487376642745, "dur": 3, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749487376642749, "dur": 36, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749487376642786, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749487376642788, "dur": 565, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749487376643358, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749487376643361, "dur": 30, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749487376643395, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749487376643398, "dur": 453, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749487376643855, "dur": 20, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749487376643878, "dur": 91, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749487376643972, "dur": 15, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749487376643990, "dur": 136, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749487376644129, "dur": 17, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749487376644150, "dur": 15, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749487376644168, "dur": 24, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749487376644195, "dur": 17, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749487376644215, "dur": 267, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749487376644486, "dur": 19, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749487376644508, "dur": 16, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749487376644527, "dur": 76, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749487376644608, "dur": 25, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749487376644636, "dur": 179, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749487376644818, "dur": 17, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749487376644838, "dur": 134, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749487376644976, "dur": 21, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749487376645001, "dur": 858, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749487376645863, "dur": 17, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749487376645884, "dur": 384, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749487376646271, "dur": 16, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749487376646290, "dur": 159, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749487376646453, "dur": 43, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749487376646500, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749487376646502, "dur": 181, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749487376646688, "dur": 21, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749487376646712, "dur": 270, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749487376646986, "dur": 13, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749487376647001, "dur": 53, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749487376647057, "dur": 17, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749487376647077, "dur": 100, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749487376647182, "dur": 16, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749487376647201, "dur": 109, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749487376647313, "dur": 16, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749487376647332, "dur": 213, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749487376647547, "dur": 16, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749487376647567, "dur": 16, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749487376647586, "dur": 144, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749487376647735, "dur": 24, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749487376647763, "dur": 143, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749487376647910, "dur": 17, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749487376647929, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749487376647931, "dur": 15, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749487376647949, "dur": 287, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749487376648241, "dur": 17, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749487376648261, "dur": 1498, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749487376649763, "dur": 20, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749487376649787, "dur": 97, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749487376649887, "dur": 39, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749487376649928, "dur": 74, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749487376650004, "dur": 62, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749487376650069, "dur": 16, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749487376650088, "dur": 74, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749487376650165, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749487376650167, "dur": 18, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749487376650187, "dur": 87, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749487376650279, "dur": 35, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749487376650316, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749487376650318, "dur": 137, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749487376650459, "dur": 17, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749487376650479, "dur": 30, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749487376650512, "dur": 23, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749487376650539, "dur": 24, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749487376650567, "dur": 19, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749487376650589, "dur": 42, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749487376650634, "dur": 16, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749487376650652, "dur": 15, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749487376650671, "dur": 27, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749487376650701, "dur": 19, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749487376650725, "dur": 22, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749487376650750, "dur": 20, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749487376650773, "dur": 15, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749487376650791, "dur": 16, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749487376650809, "dur": 17, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749487376650830, "dur": 16, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749487376650849, "dur": 15, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749487376650874, "dur": 17, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749487376650895, "dur": 15, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749487376650913, "dur": 18, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749487376650933, "dur": 16, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749487376650952, "dur": 16, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749487376650970, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749487376650973, "dur": 16, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749487376650992, "dur": 15, "ph": "X", "name": "ReadAsync 44", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749487376651010, "dur": 15, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749487376651028, "dur": 15, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749487376651046, "dur": 16, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749487376651065, "dur": 15, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749487376651082, "dur": 41, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749487376651127, "dur": 19, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749487376651150, "dur": 19, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749487376651171, "dur": 18, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749487376651192, "dur": 19, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749487376651214, "dur": 18, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749487376651235, "dur": 16, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749487376651254, "dur": 16, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749487376651273, "dur": 26, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749487376651302, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749487376651305, "dur": 26, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749487376651334, "dur": 20, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749487376651357, "dur": 18, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749487376651377, "dur": 18, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749487376651398, "dur": 16, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749487376651417, "dur": 21, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749487376651442, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749487376651443, "dur": 20, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749487376651466, "dur": 17, "ph": "X", "name": "ReadAsync 28", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749487376651486, "dur": 22, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749487376651511, "dur": 19, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749487376651533, "dur": 16, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749487376651552, "dur": 18, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749487376651573, "dur": 26, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749487376651601, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749487376651603, "dur": 20, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749487376651626, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749487376651628, "dur": 24, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749487376651655, "dur": 18, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749487376651676, "dur": 19, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749487376651699, "dur": 21, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749487376651723, "dur": 17, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749487376651743, "dur": 16, "ph": "X", "name": "ReadAsync 52", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749487376651761, "dur": 16, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749487376651780, "dur": 19, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749487376651802, "dur": 19, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749487376651823, "dur": 70, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749487376651900, "dur": 49, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749487376651952, "dur": 1, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749487376651954, "dur": 203, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749487376652162, "dur": 103, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749487376652270, "dur": 36, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749487376652308, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749487376652309, "dur": 200, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749487376652514, "dur": 136, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749487376652653, "dur": 1, "ph": "X", "name": "ProcessMessages 180", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749487376652655, "dur": 87, "ph": "X", "name": "ReadAsync 180", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749487376652745, "dur": 1, "ph": "X", "name": "ProcessMessages 144", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749487376652748, "dur": 108, "ph": "X", "name": "ReadAsync 144", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749487376652868, "dur": 65, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749487376652936, "dur": 1, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749487376652938, "dur": 128433, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749487376781381, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749487376781385, "dur": 27, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749487376781414, "dur": 3731, "ph": "X", "name": "ProcessMessages 353", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749487376785148, "dur": 57600, "ph": "X", "name": "ReadAsync 353", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749487376842757, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749487376842760, "dur": 40, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749487376842803, "dur": 539931, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749487377382742, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749487377382746, "dur": 40, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749487377382790, "dur": 3, "ph": "X", "name": "ProcessMessages 28", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749487377382794, "dur": 60364, "ph": "X", "name": "ReadAsync 28", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749487377443166, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749487377443169, "dur": 21, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749487377443191, "dur": 18, "ph": "X", "name": "ProcessMessages 429", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749487377443210, "dur": 5688, "ph": "X", "name": "ReadAsync 429", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749487377448906, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749487377448909, "dur": 41, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749487377448955, "dur": 25, "ph": "X", "name": "ProcessMessages 423", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749487377448982, "dur": 12898, "ph": "X", "name": "ReadAsync 423", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749487377461889, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749487377461892, "dur": 32, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749487377461927, "dur": 4196, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749487377466128, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749487377466130, "dur": 30, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749487377466163, "dur": 2, "ph": "X", "name": "ProcessMessages 8", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749487377466167, "dur": 1697, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749487377467868, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749487377467870, "dur": 31, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749487377467905, "dur": 16, "ph": "X", "name": "ProcessMessages 34", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749487377467922, "dur": 105748, "ph": "X", "name": "ReadAsync 34", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749487377573682, "dur": 3, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749487377573688, "dur": 101, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749487377573800, "dur": 3, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749487377573805, "dur": 475532, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749487378049353, "dur": 3, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749487378049358, "dur": 87, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749487378049459, "dur": 6, "ph": "X", "name": "ProcessMessages 8", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749487378049468, "dur": 2979, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749487378052455, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749487378052460, "dur": 132, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749487378052598, "dur": 25, "ph": "X", "name": "ProcessMessages 50", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749487378052624, "dur": 1214, "ph": "X", "name": "ReadAsync 50", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749487378053843, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749487378053845, "dur": 61, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749487378053912, "dur": 383, "ph": "X", "name": "ProcessMessages 13", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749487378054297, "dur": 9830, "ph": "X", "name": "ReadAsync 13", "args": {}}, {"pid": 18384, "tid": 771, "ts": 1749487378073761, "dur": 2010, "ph": "X", "name": "ReadEntireBinlogFromIpcAsync", "args": {}}, {"pid": 18384, "tid": 8589934592, "ph": "M", "name": "thread_name", "args": {"name": "WaitForBuildProgramInputDataBeingWrittenAndSendDagReadyMessageAsync"}}, {"pid": 18384, "tid": 8589934592, "ts": 1749487376508019, "dur": 90702, "ph": "X", "name": "await writeBuildProgramInputDataTask", "args": {}}, {"pid": 18384, "tid": 8589934592, "ts": 1749487376598722, "dur": 4, "ph": "X", "name": "WritePipe.WaitConnectionAsync", "args": {}}, {"pid": 18384, "tid": 8589934592, "ts": 1749487376598727, "dur": 878, "ph": "X", "name": "WriteDagReadyMessage", "args": {}}, {"pid": 18384, "tid": 771, "ts": 1749487378075773, "dur": 4, "ph": "X", "name": "WaitForBuildProgramInputDataBeingWrittenAndSendDagReadyMessageAsync", "args": {}}, {"pid": 18384, "tid": 4294967296, "ph": "M", "name": "thread_name", "args": {"name": "BuildAsync"}}, {"pid": 18384, "tid": 4294967296, "ts": 1749487376491865, "dur": 1573054, "ph": "X", "name": "RunBackend", "args": {}}, {"pid": 18384, "tid": 4294967296, "ts": 1749487376494667, "dur": 5707, "ph": "X", "name": "BackendProgram.Start", "args": {}}, {"pid": 18384, "tid": 4294967296, "ts": 1749487378064932, "dur": 2835, "ph": "X", "name": "await WaitForAndApplyScriptUpdaters", "args": {}}, {"pid": 18384, "tid": 4294967296, "ts": 1749487378066664, "dur": 69, "ph": "X", "name": "await <PERSON><PERSON>tUp<PERSON><PERSON>", "args": {}}, {"pid": 18384, "tid": 4294967296, "ts": 1749487378067917, "dur": 12, "ph": "X", "name": "await taskToReadBuildProgramOutput", "args": {}}, {"pid": 18384, "tid": 771, "ts": 1749487378075778, "dur": 7, "ph": "X", "name": "BuildAsync", "args": {}}, {"cat": "", "pid": 12345, "tid": 0, "ts": 0, "ph": "M", "name": "process_name", "args": {"name": "bee_backend"}}, {"pid": 12345, "tid": 0, "ts": 1749487376515893, "dur": 1615, "ph": "X", "name": "DriverInitData", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1749487376517517, "dur": 644, "ph": "X", "name": "RemoveStaleOutputs", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1749487376518267, "dur": 65, "ph": "X", "name": "<PERSON><PERSON>", "args": {"detail": "PrepareNodes"}}, {"pid": 12345, "tid": 0, "ts": 1749487376518332, "dur": 513, "ph": "X", "name": "BuildQueueInit", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1749487376519538, "dur": 488, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TerrainPhysicsModule.dll_D293A48779142A71.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1749487376520749, "dur": 1138, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Burst.Unsafe.dll_D2F5C6CBE8D78A56.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1749487376522374, "dur": 83, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.2D.Tilemap.Extras.rsp2"}}, {"pid": 12345, "tid": 0, "ts": 1749487376525278, "dur": 64, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.2D.Animation.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1749487376525560, "dur": 84, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.State.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 0, "ts": 1749487376518867, "dur": 15097, "ph": "X", "name": "EnqueueRequestedNodes", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1749487376533975, "dur": 1519895, "ph": "X", "name": "WaitForBuildFinished", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1749487378053872, "dur": 148, "ph": "X", "name": "JoinBuildThread", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1749487378054021, "dur": 106, "ph": "X", "name": "ThreadStateDestroy", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1749487378054139, "dur": 217, "ph": "X", "name": "ThreadStateDestroy", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1749487378054593, "dur": 70, "ph": "X", "name": "BuildQueueDestroyTail", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1749487378054693, "dur": 2156, "ph": "X", "name": "<PERSON><PERSON>", "args": {"detail": "Write AllBuiltNodes"}}, {"pid": 12345, "tid": 1, "ts": 1749487376518990, "dur": 15036, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1749487376534061, "dur": 112, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1749487376534229, "dur": 93, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Tools\\ScriptUpdater\\ApiUpdater.MovedFromExtractor.exe"}}, {"pid": 12345, "tid": 1, "ts": 1749487376534190, "dur": 133, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ClothModule.dll_FA85EF908E5A33BF.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1749487376534323, "dur": 77, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1749487376534408, "dur": 80, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.InputLegacyModule.dll"}}, {"pid": 12345, "tid": 1, "ts": 1749487376534406, "dur": 84, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.InputLegacyModule.dll_6FDD4B7A6EC84268.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1749487376534557, "dur": 51, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1749487376534613, "dur": 51, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.TLSModule.dll"}}, {"pid": 12345, "tid": 1, "ts": 1749487376534612, "dur": 54, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TLSModule.dll_277964AD70D626E0.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1749487376534727, "dur": 52, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.VideoModule.dll"}}, {"pid": 12345, "tid": 1, "ts": 1749487376534726, "dur": 55, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.VideoModule.dll_B8A7170076CF7080.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1749487376534782, "dur": 162, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1749487376534948, "dur": 129, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1749487376535083, "dur": 120, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting@1.9.4\\Editor\\VisualScripting.Core\\EditorAssetResources\\Unity.VisualScripting.TextureAssets.dll"}}, {"pid": 12345, "tid": 1, "ts": 1749487376535081, "dur": 124, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.VisualScripting.TextureAssets.dll_E1772EF4FEB70289.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1749487376535205, "dur": 58, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1749487376535271, "dur": 66, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Core.rsp"}}, {"pid": 12345, "tid": 1, "ts": 1749487376535395, "dur": 52, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1749487376535462, "dur": 60, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1749487376535535, "dur": 99, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1749487376535775, "dur": 175, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1749487376536041, "dur": 183, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1749487376536379, "dur": 85, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/3258459190130463912.rsp"}}, {"pid": 12345, "tid": 1, "ts": 1749487376536507, "dur": 59, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/9111662996337956171.rsp"}}, {"pid": 12345, "tid": 1, "ts": 1749487376536566, "dur": 58, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1749487376536626, "dur": 1508, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1749487376538135, "dur": 1435, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1749487376539571, "dur": 1122, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1749487376540694, "dur": 1235, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1749487376542511, "dur": 503, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.render-pipelines.universal@14.0.11\\Editor\\Converter\\ConverterItemInfo.cs"}}, {"pid": 12345, "tid": 1, "ts": 1749487376541929, "dur": 1592, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1749487376543521, "dur": 1492, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1749487376545013, "dur": 1323, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1749487376546337, "dur": 1388, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1749487376547726, "dur": 1451, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1749487376549178, "dur": 1241, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1749487376550419, "dur": 1327, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1749487376551746, "dur": 1616, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1749487376553363, "dur": 1010, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1749487376554374, "dur": 1312, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1749487376555687, "dur": 2301, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1749487376557989, "dur": 1264, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1749487376559253, "dur": 596, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1749487376559849, "dur": 450, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1749487376560300, "dur": 281, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.Mathematics.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1749487376561358, "dur": 96, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.mathematics@1.2.6\\Unity.Mathematics\\matrix.cs"}}, {"pid": 12345, "tid": 1, "ts": 1749487376560610, "dur": 961, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Mathematics.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1749487376561571, "dur": 130, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1749487376561714, "dur": 370, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1749487376562090, "dur": 215, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.2D.Common.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1749487376562348, "dur": 533, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.2D.Common.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1749487376562881, "dur": 100, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1749487376563043, "dur": 183, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.2D.Common.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1749487376563226, "dur": 127, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1749487376563841, "dur": 52, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1900b0aE.dag\\Unity.2D.Common.Runtime.ref.dll"}}, {"pid": 12345, "tid": 1, "ts": 1749487376563357, "dur": 556, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.2D.Common.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1749487376563913, "dur": 80, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1749487376564024, "dur": 77091, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1749487376641128, "dur": 69, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEditor.CoreModule.dll"}}, {"pid": 12345, "tid": 1, "ts": 1749487376641565, "dur": 138, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netfx\\System.ComponentModel.Composition.dll"}}, {"pid": 12345, "tid": 1, "ts": 1749487376641881, "dur": 57, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.IO.FileSystem.Primitives.dll"}}, {"pid": 12345, "tid": 1, "ts": 1749487376642433, "dur": 100, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Threading.dll"}}, {"pid": 12345, "tid": 1, "ts": 1749487376642661, "dur": 62, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\api-ms-win-core-datetime-l1-1-0.dll"}}, {"pid": 12345, "tid": 1, "ts": 1749487376643403, "dur": 66, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\System.ComponentModel.TypeConverter.dll"}}, {"pid": 12345, "tid": 1, "ts": 1749487376641117, "dur": 3743, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.2D.Tilemap.Extras.dll (+pdb)"}}, {"pid": 12345, "tid": 1, "ts": 1749487376644861, "dur": 667, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1749487376645553, "dur": 61, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEditor.dll"}}, {"pid": 12345, "tid": 1, "ts": 1749487376645925, "dur": 424, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.IO.UnmanagedMemoryStream.dll"}}, {"pid": 12345, "tid": 1, "ts": 1749487376645534, "dur": 2912, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.CollabProxy.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 1, "ts": 1749487376648446, "dur": 113, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1749487376648610, "dur": 134, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.AIModule.dll"}}, {"pid": 12345, "tid": 1, "ts": 1749487376649295, "dur": 1459, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\System.IO.Compression.FileSystem.dll"}}, {"pid": 12345, "tid": 1, "ts": 1749487376650920, "dur": 56, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\Microsoft.AspNetCore.Connections.Abstractions.dll"}}, {"pid": 12345, "tid": 1, "ts": 1749487376651047, "dur": 271, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\Microsoft.AspNetCore.Mvc.TagHelpers.dll"}}, {"pid": 12345, "tid": 1, "ts": 1749487376651716, "dur": 62, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\System.Globalization.Extensions.dll"}}, {"pid": 12345, "tid": 1, "ts": 1749487376652420, "dur": 190, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEditor.CoreModule.dll"}}, {"pid": 12345, "tid": 1, "ts": 1749487376652680, "dur": 121, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.JSONSerializeModule.dll"}}, {"pid": 12345, "tid": 1, "ts": 1749487376653182, "dur": 120, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Linq.Queryable.dll"}}, {"pid": 12345, "tid": 1, "ts": 1749487376648567, "dur": 4851, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.2D.Sprite.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 1, "ts": 1749487376653419, "dur": 75, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1749487376653529, "dur": 1400300, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1749487376519153, "dur": 14906, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1749487376534078, "dur": 97, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.AnimationModule.dll"}}, {"pid": 12345, "tid": 2, "ts": 1749487376534176, "dur": 66, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Tools\\ScriptUpdater\\ApiUpdater.MovedFromExtractor.exe"}}, {"pid": 12345, "tid": 2, "ts": 1749487376534066, "dur": 177, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AnimationModule.dll_C0FCC60981132629.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1749487376534442, "dur": 94, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1749487376534544, "dur": 66, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.TerrainModule.dll"}}, {"pid": 12345, "tid": 2, "ts": 1749487376534542, "dur": 70, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TerrainModule.dll_55536420766141FA.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1749487376534612, "dur": 56, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1749487376534701, "dur": 96, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.VehiclesModule.dll"}}, {"pid": 12345, "tid": 2, "ts": 1749487376534699, "dur": 100, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.VehiclesModule.dll_B283CA44A21F1336.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1749487376534799, "dur": 57, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1749487376534863, "dur": 79, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEditor.TextCoreTextEngineModule.dll"}}, {"pid": 12345, "tid": 2, "ts": 1749487376534861, "dur": 84, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.TextCoreTextEngineModule.dll_3F15606BE1109C0F.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1749487376534945, "dur": 101, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1749487376535052, "dur": 188, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.collab-proxy@2.5.2\\Lib\\Editor\\PlasticSCM\\Unity.Plastic.Antlr3.Runtime.dll"}}, {"pid": 12345, "tid": 2, "ts": 1749487376535051, "dur": 191, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Plastic.Antlr3.Runtime.dll_1B6116B393E72286.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1749487376535243, "dur": 60, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1749487376535428, "dur": 52, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1749487376535589, "dur": 55, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.2D.Animation.Runtime.rsp"}}, {"pid": 12345, "tid": 2, "ts": 1749487376535645, "dur": 55, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1749487376535760, "dur": 225, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1749487376536008, "dur": 56, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1749487376536159, "dur": 62, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1749487376536278, "dur": 58, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1749487376536373, "dur": 50, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1749487376536660, "dur": 1500, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1749487376538160, "dur": 1992, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1749487376540153, "dur": 1903, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1749487376542057, "dur": 1861, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1749487376544076, "dur": 632, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.shadergraph@14.0.11\\Editor\\Generation\\Enumerations\\KeywordScope.cs"}}, {"pid": 12345, "tid": 2, "ts": 1749487376543919, "dur": 1419, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1749487376545338, "dur": 1320, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1749487376546658, "dur": 612, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.burst@1.8.15\\Editor\\BurstAotCompiler.cs"}}, {"pid": 12345, "tid": 2, "ts": 1749487376546658, "dur": 2129, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1749487376548787, "dur": 1759, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1749487376550546, "dur": 1867, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1749487376552413, "dur": 1953, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1749487376554367, "dur": 869, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1749487376555236, "dur": 843, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1749487376556079, "dur": 1265, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1749487376557344, "dur": 1332, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1749487376558677, "dur": 706, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1749487376559383, "dur": 449, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1749487376559891, "dur": 435, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1749487376560327, "dur": 578, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.Timeline.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1749487376560905, "dur": 579, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1749487376561489, "dur": 637, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Timeline.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1749487376562126, "dur": 957, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1749487376563126, "dur": 195, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.Timeline.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1749487376563840, "dur": 52, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.timeline@1.7.6\\Editor\\inspectors\\DirectorNamedColorInspector.cs"}}, {"pid": 12345, "tid": 2, "ts": 1749487376563348, "dur": 908, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Timeline.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1749487376564257, "dur": 129, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1749487376564443, "dur": 76795, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1749487376644413, "dur": 88, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Data.Common.dll"}}, {"pid": 12345, "tid": 2, "ts": 1749487376641239, "dur": 3959, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.VisualScripting.Flow.dll (+pdb)"}}, {"pid": 12345, "tid": 2, "ts": 1749487376645198, "dur": 629, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1749487376645839, "dur": 2090, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.2D.SpriteShape.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 2, "ts": 1749487376647930, "dur": 654, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1749487376648609, "dur": 66, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEditor.dll"}}, {"pid": 12345, "tid": 2, "ts": 1749487376648590, "dur": 2883, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.InternalAPIEditorBridge.001.dll (+pdb)"}}, {"pid": 12345, "tid": 2, "ts": 1749487376651474, "dur": 243, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1749487376651730, "dur": 60, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1749487376651907, "dur": 382, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1749487376652390, "dur": 234, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1749487376652631, "dur": 69, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1749487376652807, "dur": 611, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1749487376653423, "dur": 99, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1749487376653526, "dur": 1400334, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1749487376519233, "dur": 14893, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1749487376534140, "dur": 54, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.AssetBundleModule.dll"}}, {"pid": 12345, "tid": 3, "ts": 1749487376534196, "dur": 92, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Tools\\ScriptUpdater\\ApiUpdater.MovedFromExtractor.exe"}}, {"pid": 12345, "tid": 3, "ts": 1749487376534134, "dur": 155, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AssetBundleModule.dll_DE98305BD6D68C77.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1749487376534290, "dur": 83, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1749487376534382, "dur": 58, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.ImageConversionModule.dll"}}, {"pid": 12345, "tid": 3, "ts": 1749487376534380, "dur": 63, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ImageConversionModule.dll_B5C2173E9AD0B5D6.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1749487376534499, "dur": 56, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.PropertiesModule.dll"}}, {"pid": 12345, "tid": 3, "ts": 1749487376534498, "dur": 59, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.PropertiesModule.dll_A964AEB46C39AB32.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1749487376534611, "dur": 59, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.TilemapModule.dll"}}, {"pid": 12345, "tid": 3, "ts": 1749487376534609, "dur": 63, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TilemapModule.dll_F44FCBA18E1E2303.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1749487376534673, "dur": 62, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1749487376534743, "dur": 59, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.VRModule.dll"}}, {"pid": 12345, "tid": 3, "ts": 1749487376534741, "dur": 64, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.VRModule.dll_387623EB1508B7EB.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1749487376534805, "dur": 60, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1749487376534876, "dur": 194, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEditor.QuickSearchModule.dll"}}, {"pid": 12345, "tid": 3, "ts": 1749487376534874, "dur": 198, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.QuickSearchModule.dll_9AAEFD6A746FD785.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1749487376535073, "dur": 83, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1749487376535202, "dur": 83, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/UnityEngine.TestRunner.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1749487376535286, "dur": 189, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1749487376535645, "dur": 111, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netfx\\System.ComponentModel.Composition.dll"}}, {"pid": 12345, "tid": 3, "ts": 1749487376535762, "dur": 55, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netfx\\System.Data.dll"}}, {"pid": 12345, "tid": 3, "ts": 1749487376535914, "dur": 63, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netfx\\System.IO.Compression.FileSystem.dll"}}, {"pid": 12345, "tid": 3, "ts": 1749487376535978, "dur": 52, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netfx\\System.Net.dll"}}, {"pid": 12345, "tid": 3, "ts": 1749487376536031, "dur": 55, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netfx\\System.Numerics.dll"}}, {"pid": 12345, "tid": 3, "ts": 1749487376536087, "dur": 74, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netfx\\System.Runtime.Serialization.dll"}}, {"pid": 12345, "tid": 3, "ts": 1749487376536163, "dur": 64, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netfx\\System.ServiceModel.Web.dll"}}, {"pid": 12345, "tid": 3, "ts": 1749487376536228, "dur": 55, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netfx\\System.Transactions.dll"}}, {"pid": 12345, "tid": 3, "ts": 1749487376536586, "dur": 152, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Buffers.dll"}}, {"pid": 12345, "tid": 3, "ts": 1749487376536788, "dur": 71, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Collections.dll"}}, {"pid": 12345, "tid": 3, "ts": 1749487376536861, "dur": 52, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Collections.NonGeneric.dll"}}, {"pid": 12345, "tid": 3, "ts": 1749487376536917, "dur": 215, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.ComponentModel.dll"}}, {"pid": 12345, "tid": 3, "ts": 1749487376537133, "dur": 63, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.ComponentModel.EventBasedAsync.dll"}}, {"pid": 12345, "tid": 3, "ts": 1749487376537198, "dur": 104, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.ComponentModel.Primitives.dll"}}, {"pid": 12345, "tid": 3, "ts": 1749487376537307, "dur": 60, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.ComponentModel.TypeConverter.dll"}}, {"pid": 12345, "tid": 3, "ts": 1749487376537420, "dur": 57, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Diagnostics.Contracts.dll"}}, {"pid": 12345, "tid": 3, "ts": 1749487376537527, "dur": 312, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Diagnostics.FileVersionInfo.dll"}}, {"pid": 12345, "tid": 3, "ts": 1749487376537843, "dur": 145, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Diagnostics.TextWriterTraceListener.dll"}}, {"pid": 12345, "tid": 3, "ts": 1749487376537990, "dur": 196, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Diagnostics.TraceSource.dll"}}, {"pid": 12345, "tid": 3, "ts": 1749487376538188, "dur": 55, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Diagnostics.Tracing.dll"}}, {"pid": 12345, "tid": 3, "ts": 1749487376538338, "dur": 51, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Globalization.dll"}}, {"pid": 12345, "tid": 3, "ts": 1749487376538390, "dur": 52, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Globalization.Extensions.dll"}}, {"pid": 12345, "tid": 3, "ts": 1749487376538489, "dur": 161, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.IO.Compression.ZipFile.dll"}}, {"pid": 12345, "tid": 3, "ts": 1749487376538844, "dur": 133, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Linq.Queryable.dll"}}, {"pid": 12345, "tid": 3, "ts": 1749487376538979, "dur": 322, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Net.NameResolution.dll"}}, {"pid": 12345, "tid": 3, "ts": 1749487376539313, "dur": 52, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Net.WebHeaderCollection.dll"}}, {"pid": 12345, "tid": 3, "ts": 1749487376539412, "dur": 244, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Numerics.Vectors.dll"}}, {"pid": 12345, "tid": 3, "ts": 1749487376539658, "dur": 52, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Reflection.DispatchProxy.dll"}}, {"pid": 12345, "tid": 3, "ts": 1749487376539711, "dur": 66, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Reflection.dll"}}, {"pid": 12345, "tid": 3, "ts": 1749487376539778, "dur": 75, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Reflection.Emit.dll"}}, {"pid": 12345, "tid": 3, "ts": 1749487376539855, "dur": 63, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Reflection.Emit.ILGeneration.dll"}}, {"pid": 12345, "tid": 3, "ts": 1749487376539968, "dur": 54, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Reflection.Extensions.dll"}}, {"pid": 12345, "tid": 3, "ts": 1749487376540025, "dur": 79, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Resources.Reader.dll"}}, {"pid": 12345, "tid": 3, "ts": 1749487376540111, "dur": 57, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Runtime.dll"}}, {"pid": 12345, "tid": 3, "ts": 1749487376540399, "dur": 206, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Runtime.Serialization.Json.dll"}}, {"pid": 12345, "tid": 3, "ts": 1749487376540613, "dur": 67, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Security.Cryptography.Csp.dll"}}, {"pid": 12345, "tid": 3, "ts": 1749487376540682, "dur": 71, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Security.Cryptography.Encoding.dll"}}, {"pid": 12345, "tid": 3, "ts": 1749487376540754, "dur": 175, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Security.Cryptography.Primitives.dll"}}, {"pid": 12345, "tid": 3, "ts": 1749487376540981, "dur": 177, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Text.Encoding.dll"}}, {"pid": 12345, "tid": 3, "ts": 1749487376541163, "dur": 74, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Threading.dll"}}, {"pid": 12345, "tid": 3, "ts": 1749487376541435, "dur": 234, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Xml.XmlSerializer.dll"}}, {"pid": 12345, "tid": 3, "ts": 1749487376541770, "dur": 129, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\NetStandard\\ref\\2.1.0\\netstandard.dll"}}, {"pid": 12345, "tid": 3, "ts": 1749487376541903, "dur": 64, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.ext.nunit@1.0.6\\net35\\unity-custom\\nunit.framework.dll"}}, {"pid": 12345, "tid": 3, "ts": 1749487376542017, "dur": 52, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.test-framework@1.1.33\\UnityEngine.TestRunner\\Assertions\\AllocatingGCMemoryConstraint.cs"}}, {"pid": 12345, "tid": 3, "ts": 1749487376542070, "dur": 58, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.test-framework@1.1.33\\UnityEngine.TestRunner\\Assertions\\ConstraintsExtensions.cs"}}, {"pid": 12345, "tid": 3, "ts": 1749487376542129, "dur": 51, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.test-framework@1.1.33\\UnityEngine.TestRunner\\Assertions\\InvalidSignatureException.cs"}}, {"pid": 12345, "tid": 3, "ts": 1749487376542181, "dur": 54, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.test-framework@1.1.33\\UnityEngine.TestRunner\\Assertions\\Is.cs"}}, {"pid": 12345, "tid": 3, "ts": 1749487376542236, "dur": 67, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.test-framework@1.1.33\\UnityEngine.TestRunner\\Assertions\\LogAssert.cs"}}, {"pid": 12345, "tid": 3, "ts": 1749487376542304, "dur": 59, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.test-framework@1.1.33\\UnityEngine.TestRunner\\Assertions\\LogScope\\ILogScope.cs"}}, {"pid": 12345, "tid": 3, "ts": 1749487376542364, "dur": 53, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.test-framework@1.1.33\\UnityEngine.TestRunner\\Assertions\\LogScope\\LogEvent.cs"}}, {"pid": 12345, "tid": 3, "ts": 1749487376542933, "dur": 331, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.test-framework@1.1.33\\UnityEngine.TestRunner\\NUnitExtensions\\Commands\\EnumerableTestState.cs"}}, {"pid": 12345, "tid": 3, "ts": 1749487376543266, "dur": 81, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.test-framework@1.1.33\\UnityEngine.TestRunner\\NUnitExtensions\\Commands\\ImmediateEnumerableCommand.cs"}}, {"pid": 12345, "tid": 3, "ts": 1749487376543348, "dur": 63, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.test-framework@1.1.33\\UnityEngine.TestRunner\\NUnitExtensions\\Commands\\OuterUnityTestActionCommand.cs"}}, {"pid": 12345, "tid": 3, "ts": 1749487376543412, "dur": 56, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.test-framework@1.1.33\\UnityEngine.TestRunner\\NUnitExtensions\\Commands\\SetUpTearDownCommand.cs"}}, {"pid": 12345, "tid": 3, "ts": 1749487376543515, "dur": 479, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.test-framework@1.1.33\\UnityEngine.TestRunner\\NUnitExtensions\\Commands\\TestCommandPcHelper.cs"}}, {"pid": 12345, "tid": 3, "ts": 1749487376544018, "dur": 50, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.test-framework@1.1.33\\UnityEngine.TestRunner\\NUnitExtensions\\ConstructDelegator.cs"}}, {"pid": 12345, "tid": 3, "ts": 1749487376544069, "dur": 712, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.test-framework@1.1.33\\UnityEngine.TestRunner\\NUnitExtensions\\Filters\\AssemblyNameFilter.cs"}}, {"pid": 12345, "tid": 3, "ts": 1749487376544783, "dur": 74, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.test-framework@1.1.33\\UnityEngine.TestRunner\\NUnitExtensions\\Filters\\CategoryFilterExtended.cs"}}, {"pid": 12345, "tid": 3, "ts": 1749487376544858, "dur": 62, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.test-framework@1.1.33\\UnityEngine.TestRunner\\NUnitExtensions\\Filters\\FullNameFilter.cs"}}, {"pid": 12345, "tid": 3, "ts": 1749487376544921, "dur": 56, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.test-framework@1.1.33\\UnityEngine.TestRunner\\NUnitExtensions\\IAsyncTestAssemblyBuilder.cs"}}, {"pid": 12345, "tid": 3, "ts": 1749487376544978, "dur": 210, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.test-framework@1.1.33\\UnityEngine.TestRunner\\NUnitExtensions\\IStateSerializer.cs"}}, {"pid": 12345, "tid": 3, "ts": 1749487376545231, "dur": 119, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.test-framework@1.1.33\\UnityEngine.TestRunner\\NUnitExtensions\\Runner\\CompositeWorkItem.cs"}}, {"pid": 12345, "tid": 3, "ts": 1749487376545435, "dur": 166, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.test-framework@1.1.33\\UnityEngine.TestRunner\\NUnitExtensions\\Runner\\PlaymodeWorkItemFactory.cs"}}, {"pid": 12345, "tid": 3, "ts": 1749487376545608, "dur": 55, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.test-framework@1.1.33\\UnityEngine.TestRunner\\NUnitExtensions\\Runner\\RestoreTestContextAfterDomainReload.cs"}}, {"pid": 12345, "tid": 3, "ts": 1749487376545664, "dur": 139, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.test-framework@1.1.33\\UnityEngine.TestRunner\\NUnitExtensions\\Runner\\TestCommandBuilder.cs"}}, {"pid": 12345, "tid": 3, "ts": 1749487376545805, "dur": 55, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.test-framework@1.1.33\\UnityEngine.TestRunner\\NUnitExtensions\\Runner\\UnityLogCheckDelegatingCommand.cs"}}, {"pid": 12345, "tid": 3, "ts": 1749487376546094, "dur": 54, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.test-framework@1.1.33\\UnityEngine.TestRunner\\NUnitExtensions\\TestExtensions.cs"}}, {"pid": 12345, "tid": 3, "ts": 1749487376546149, "dur": 50, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.test-framework@1.1.33\\UnityEngine.TestRunner\\NUnitExtensions\\TestResultExtensions.cs"}}, {"pid": 12345, "tid": 3, "ts": 1749487376546332, "dur": 317, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.test-framework@1.1.33\\UnityEngine.TestRunner\\TestRunner\\Callbacks\\RemoteTestResultSender.cs"}}, {"pid": 12345, "tid": 3, "ts": 1749487376546652, "dur": 241, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.test-framework@1.1.33\\UnityEngine.TestRunner\\TestRunner\\Callbacks\\TestResultRenderer.cs"}}, {"pid": 12345, "tid": 3, "ts": 1749487376546895, "dur": 58, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.test-framework@1.1.33\\UnityEngine.TestRunner\\TestRunner\\Callbacks\\TestResultRendererCallback.cs"}}, {"pid": 12345, "tid": 3, "ts": 1749487376546954, "dur": 55, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.test-framework@1.1.33\\UnityEngine.TestRunner\\TestRunner\\ITestRunnerListener.cs"}}, {"pid": 12345, "tid": 3, "ts": 1749487376547010, "dur": 51, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.test-framework@1.1.33\\UnityEngine.TestRunner\\TestRunner\\Messages\\IEditModeTestYieldInstruction.cs"}}, {"pid": 12345, "tid": 3, "ts": 1749487376547062, "dur": 322, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.test-framework@1.1.33\\UnityEngine.TestRunner\\TestRunner\\PlaymodeTestsController.cs"}}, {"pid": 12345, "tid": 3, "ts": 1749487376547386, "dur": 63, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.test-framework@1.1.33\\UnityEngine.TestRunner\\TestRunner\\PlaymodeTestsControllerSettings.cs"}}, {"pid": 12345, "tid": 3, "ts": 1749487376547450, "dur": 65, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.test-framework@1.1.33\\UnityEngine.TestRunner\\TestRunner\\RemoteHelpers\\IRemoteTestResultDataFactory.cs"}}, {"pid": 12345, "tid": 3, "ts": 1749487376547599, "dur": 189, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.test-framework@1.1.33\\UnityEngine.TestRunner\\TestRunner\\RemoteHelpers\\RemoteTestResultData.cs"}}, {"pid": 12345, "tid": 3, "ts": 1749487376547790, "dur": 63, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.test-framework@1.1.33\\UnityEngine.TestRunner\\TestRunner\\RemoteHelpers\\RemoteTestResultDataFactory.cs"}}, {"pid": 12345, "tid": 3, "ts": 1749487376547855, "dur": 52, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.test-framework@1.1.33\\UnityEngine.TestRunner\\TestRunner\\RemoteHelpers\\RemoteTestResultDataWithTestData.cs"}}, {"pid": 12345, "tid": 3, "ts": 1749487376547908, "dur": 53, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.test-framework@1.1.33\\UnityEngine.TestRunner\\TestRunner\\RuntimeTestRunnerFilter.cs"}}, {"pid": 12345, "tid": 3, "ts": 1749487376548009, "dur": 130, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.test-framework@1.1.33\\UnityEngine.TestRunner\\TestRunner\\TestEnumeratorWrapper.cs"}}, {"pid": 12345, "tid": 3, "ts": 1749487376548141, "dur": 134, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.test-framework@1.1.33\\UnityEngine.TestRunner\\TestRunner\\TestListenerWrapper.cs"}}, {"pid": 12345, "tid": 3, "ts": 1749487376548276, "dur": 161, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.test-framework@1.1.33\\UnityEngine.TestRunner\\TestRunner\\TestPlatform.cs"}}, {"pid": 12345, "tid": 3, "ts": 1749487376548486, "dur": 312, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.test-framework@1.1.33\\UnityEngine.TestRunner\\Utils\\AssemblyProvider\\AssemblyWrapper.cs"}}, {"pid": 12345, "tid": 3, "ts": 1749487376548799, "dur": 157, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.test-framework@1.1.33\\UnityEngine.TestRunner\\Utils\\AssemblyProvider\\IAssemblyLoadProxy.cs"}}, {"pid": 12345, "tid": 3, "ts": 1749487376549073, "dur": 184, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.test-framework@1.1.33\\UnityEngine.TestRunner\\Utils\\AttributeHelper.cs"}}, {"pid": 12345, "tid": 3, "ts": 1749487376549258, "dur": 70, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.test-framework@1.1.33\\UnityEngine.TestRunner\\Utils\\ColorEqualityComparer.cs"}}, {"pid": 12345, "tid": 3, "ts": 1749487376549329, "dur": 50, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.test-framework@1.1.33\\UnityEngine.TestRunner\\Utils\\CoroutineRunner.cs"}}, {"pid": 12345, "tid": 3, "ts": 1749487376549425, "dur": 328, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.test-framework@1.1.33\\UnityEngine.TestRunner\\Utils\\IOuterUnityTestAction.cs"}}, {"pid": 12345, "tid": 3, "ts": 1749487376550025, "dur": 181, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.test-framework@1.1.33\\UnityEngine.TestRunner\\Utils\\Vector2ComparerWithEqualsOperator.cs"}}, {"pid": 12345, "tid": 3, "ts": 1749487376535484, "dur": 14845, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/UnityEngine.TestRunner.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1749487376550329, "dur": 318, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1749487376550685, "dur": 164, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1749487376550863, "dur": 92, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/UnityEditor.TestRunner.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1749487376550955, "dur": 118, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1749487376551536, "dur": 169, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.ComponentModel.EventBasedAsync.dll"}}, {"pid": 12345, "tid": 3, "ts": 1749487376551754, "dur": 184, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Console.dll"}}, {"pid": 12345, "tid": 3, "ts": 1749487376552033, "dur": 384, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Diagnostics.Process.dll"}}, {"pid": 12345, "tid": 3, "ts": 1749487376552419, "dur": 54, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Diagnostics.StackTrace.dll"}}, {"pid": 12345, "tid": 3, "ts": 1749487376552662, "dur": 51, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Dynamic.Runtime.dll"}}, {"pid": 12345, "tid": 3, "ts": 1749487376552718, "dur": 246, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Globalization.Calendars.dll"}}, {"pid": 12345, "tid": 3, "ts": 1749487376552966, "dur": 60, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Globalization.dll"}}, {"pid": 12345, "tid": 3, "ts": 1749487376553027, "dur": 57, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Globalization.Extensions.dll"}}, {"pid": 12345, "tid": 3, "ts": 1749487376553085, "dur": 53, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.IO.Compression.ZipFile.dll"}}, {"pid": 12345, "tid": 3, "ts": 1749487376553278, "dur": 51, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.IO.FileSystem.Primitives.dll"}}, {"pid": 12345, "tid": 3, "ts": 1749487376553330, "dur": 344, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.IO.FileSystem.Watcher.dll"}}, {"pid": 12345, "tid": 3, "ts": 1749487376553676, "dur": 60, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.IO.IsolatedStorage.dll"}}, {"pid": 12345, "tid": 3, "ts": 1749487376553738, "dur": 265, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.IO.MemoryMappedFiles.dll"}}, {"pid": 12345, "tid": 3, "ts": 1749487376554004, "dur": 160, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.IO.Pipes.dll"}}, {"pid": 12345, "tid": 3, "ts": 1749487376554165, "dur": 126, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.IO.UnmanagedMemoryStream.dll"}}, {"pid": 12345, "tid": 3, "ts": 1749487376554292, "dur": 77, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Linq.dll"}}, {"pid": 12345, "tid": 3, "ts": 1749487376554370, "dur": 50, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Linq.Expressions.dll"}}, {"pid": 12345, "tid": 3, "ts": 1749487376554422, "dur": 63, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Linq.Parallel.dll"}}, {"pid": 12345, "tid": 3, "ts": 1749487376554487, "dur": 134, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Linq.Queryable.dll"}}, {"pid": 12345, "tid": 3, "ts": 1749487376554682, "dur": 250, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Net.NetworkInformation.dll"}}, {"pid": 12345, "tid": 3, "ts": 1749487376554934, "dur": 118, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Net.Ping.dll"}}, {"pid": 12345, "tid": 3, "ts": 1749487376555134, "dur": 134, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Net.WebHeaderCollection.dll"}}, {"pid": 12345, "tid": 3, "ts": 1749487376555269, "dur": 96, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Net.WebSockets.Client.dll"}}, {"pid": 12345, "tid": 3, "ts": 1749487376555366, "dur": 295, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Net.WebSockets.dll"}}, {"pid": 12345, "tid": 3, "ts": 1749487376555738, "dur": 402, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Reflection.Emit.ILGeneration.dll"}}, {"pid": 12345, "tid": 3, "ts": 1749487376556142, "dur": 57, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Reflection.Emit.Lightweight.dll"}}, {"pid": 12345, "tid": 3, "ts": 1749487376556248, "dur": 54, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Reflection.Primitives.dll"}}, {"pid": 12345, "tid": 3, "ts": 1749487376556349, "dur": 125, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Resources.ResourceManager.dll"}}, {"pid": 12345, "tid": 3, "ts": 1749487376556547, "dur": 230, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Runtime.Extensions.dll"}}, {"pid": 12345, "tid": 3, "ts": 1749487376556778, "dur": 137, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Runtime.Handles.dll"}}, {"pid": 12345, "tid": 3, "ts": 1749487376556920, "dur": 301, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Runtime.InteropServices.dll"}}, {"pid": 12345, "tid": 3, "ts": 1749487376557265, "dur": 156, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Runtime.Numerics.dll"}}, {"pid": 12345, "tid": 3, "ts": 1749487376557450, "dur": 162, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Runtime.Serialization.Json.dll"}}, {"pid": 12345, "tid": 3, "ts": 1749487376557788, "dur": 132, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Security.Cryptography.X509Certificates.dll"}}, {"pid": 12345, "tid": 3, "ts": 1749487376557921, "dur": 103, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Security.Principal.dll"}}, {"pid": 12345, "tid": 3, "ts": 1749487376558025, "dur": 164, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Security.SecureString.dll"}}, {"pid": 12345, "tid": 3, "ts": 1749487376558191, "dur": 54, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.ServiceModel.Duplex.dll"}}, {"pid": 12345, "tid": 3, "ts": 1749487376558246, "dur": 54, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.ServiceModel.Http.dll"}}, {"pid": 12345, "tid": 3, "ts": 1749487376558301, "dur": 120, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.ServiceModel.NetTcp.dll"}}, {"pid": 12345, "tid": 3, "ts": 1749487376558422, "dur": 189, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.ServiceModel.Primitives.dll"}}, {"pid": 12345, "tid": 3, "ts": 1749487376558661, "dur": 209, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Text.Encoding.Extensions.dll"}}, {"pid": 12345, "tid": 3, "ts": 1749487376558872, "dur": 79, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Text.RegularExpressions.dll"}}, {"pid": 12345, "tid": 3, "ts": 1749487376558953, "dur": 89, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Threading.dll"}}, {"pid": 12345, "tid": 3, "ts": 1749487376559043, "dur": 59, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Threading.Overlapped.dll"}}, {"pid": 12345, "tid": 3, "ts": 1749487376559103, "dur": 54, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Threading.Tasks.dll"}}, {"pid": 12345, "tid": 3, "ts": 1749487376559158, "dur": 50, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Threading.Tasks.Extensions.dll"}}, {"pid": 12345, "tid": 3, "ts": 1749487376559304, "dur": 65, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Threading.ThreadPool.dll"}}, {"pid": 12345, "tid": 3, "ts": 1749487376551081, "dur": 8631, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/UnityEditor.TestRunner.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1749487376559713, "dur": 60, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1749487376559879, "dur": 59, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/UnityEditor.UI.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1749487376559957, "dur": 230, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/UnityEditor.UI.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1749487376560187, "dur": 50, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1749487376560300, "dur": 134, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1749487376560544, "dur": 52, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.GridModule.dll"}}, {"pid": 12345, "tid": 3, "ts": 1749487376560462, "dur": 786, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1749487376561249, "dur": 434, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1749487376561751, "dur": 158, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.CodeGen.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1749487376561958, "dur": 475, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.CodeGen.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1749487376562434, "dur": 308, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1749487376562755, "dur": 711, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPP-Configuration Library/ilpp-configuration.nevergeneratedoutput"}}, {"pid": 12345, "tid": 3, "ts": 1749487376563499, "dur": 121, "ph": "X", "name": "StoreTimestampsOfNonGeneratedInputFiles", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1749487376564532, "dur": 70049, "ph": "X", "name": "ILPP-Configuration", "args": {"detail": "Library/ilpp-configuration.nevergeneratedoutput"}}, {"pid": 12345, "tid": 3, "ts": 1749487376641051, "dur": 2262, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.2D.IK.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 3, "ts": 1749487376643314, "dur": 1574, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1749487376645555, "dur": 50, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\System.Data.dll"}}, {"pid": 12345, "tid": 3, "ts": 1749487376644894, "dur": 2719, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.Core.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 3, "ts": 1749487376647613, "dur": 83, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1749487376648610, "dur": 50, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\api-ms-win-core-localization-l1-2-0.dll"}}, {"pid": 12345, "tid": 3, "ts": 1749487376648811, "dur": 54, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\Microsoft.AspNetCore.DataProtection.dll"}}, {"pid": 12345, "tid": 3, "ts": 1749487376647702, "dur": 2554, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Searcher.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 3, "ts": 1749487376650256, "dur": 1677, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1749487376652011, "dur": 50, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1749487376652069, "dur": 69, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1749487376652145, "dur": 62, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1749487376652215, "dur": 482, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1749487376652705, "dur": 59, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1749487376652768, "dur": 50, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1749487376652818, "dur": 641, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1749487376653496, "dur": 1400296, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1749487376519089, "dur": 14961, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1749487376534062, "dur": 114, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1749487376534222, "dur": 98, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Tools\\ScriptUpdater\\ApiUpdater.MovedFromExtractor.exe"}}, {"pid": 12345, "tid": 4, "ts": 1749487376534186, "dur": 135, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AudioModule.dll_939D9295B43D54DB.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1749487376534322, "dur": 166, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1749487376534497, "dur": 71, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.ProfilerModule.dll"}}, {"pid": 12345, "tid": 4, "ts": 1749487376534495, "dur": 76, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ProfilerModule.dll_6D0709FBC14ECAA8.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1749487376534572, "dur": 56, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1749487376534636, "dur": 85, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.UnityAnalyticsCommonModule.dll"}}, {"pid": 12345, "tid": 4, "ts": 1749487376534633, "dur": 91, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityAnalyticsCommonModule.dll_21357EA18D8E171B.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1749487376534724, "dur": 91, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1749487376534823, "dur": 64, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.AIModule.dll"}}, {"pid": 12345, "tid": 4, "ts": 1749487376534822, "dur": 67, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AIModule.dll_EA305F3817072CC7.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1749487376534890, "dur": 58, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1749487376534958, "dur": 109, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1749487376535075, "dur": 77, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1749487376535152, "dur": 50, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/UnityEditor.TestRunner.rsp2"}}, {"pid": 12345, "tid": 4, "ts": 1749487376535401, "dur": 74, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Flow.rsp"}}, {"pid": 12345, "tid": 4, "ts": 1749487376535651, "dur": 88, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1749487376535758, "dur": 66, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1749487376535881, "dur": 143, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1749487376536031, "dur": 57, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1749487376536098, "dur": 92, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1749487376536235, "dur": 52, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1749487376536292, "dur": 52, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1749487376536355, "dur": 56, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/8899139255040401798.rsp"}}, {"pid": 12345, "tid": 4, "ts": 1749487376536498, "dur": 51, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1749487376536587, "dur": 60, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1749487376536651, "dur": 1637, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1749487376538288, "dur": 1542, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1749487376539974, "dur": 569, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.2d.psdimporter@8.0.5\\Editor\\PSDPlugin\\PsdFile\\ImageResources\\VersionInfo.cs"}}, {"pid": 12345, "tid": 4, "ts": 1749487376542229, "dur": 563, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.2d.psdimporter@8.0.5\\Editor\\PSDPlugin\\PsdFile\\Compression\\ImageData.cs"}}, {"pid": 12345, "tid": 4, "ts": 1749487376539831, "dur": 3356, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1749487376544103, "dur": 638, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting@1.9.4\\Editor\\VisualScripting.Flow\\Framework\\Control\\SelectUnitDescriptor.cs"}}, {"pid": 12345, "tid": 4, "ts": 1749487376543188, "dur": 3060, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1749487376546248, "dur": 2032, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1749487376548280, "dur": 1410, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1749487376549690, "dur": 1896, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1749487376551586, "dur": 1390, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1749487376552976, "dur": 1715, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1749487376554691, "dur": 2216, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1749487376556907, "dur": 1541, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1749487376558448, "dur": 1050, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1749487376559498, "dur": 361, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1749487376559859, "dur": 483, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1749487376560344, "dur": 124, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.Rider.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1749487376560696, "dur": 70, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Tools\\ScriptUpdater\\ApiUpdater.MovedFromExtractor.exe"}}, {"pid": 12345, "tid": 4, "ts": 1749487376560525, "dur": 244, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Shaders.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1749487376560769, "dur": 64, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1749487376560839, "dur": 702, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Rider.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1749487376561541, "dur": 539, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1749487376562090, "dur": 57, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1749487376562156, "dur": 51, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1749487376562211, "dur": 149, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.CollabProxy.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1749487376562410, "dur": 126, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.2D.Aseprite.Common.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1749487376562580, "dur": 479, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.2D.Aseprite.Common.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1749487376563060, "dur": 164, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1749487376563233, "dur": 185, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1749487376563422, "dur": 518, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1749487376563940, "dur": 36671, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1749487376600612, "dur": 4263, "ph": "X", "name": "CheckDagSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1749487376604876, "dur": 36230, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1749487376641108, "dur": 2458, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.VisualScripting.Core.dll (+pdb)"}}, {"pid": 12345, "tid": 4, "ts": 1749487376643567, "dur": 138, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1749487376645072, "dur": 140, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\System.Private.Xml.dll"}}, {"pid": 12345, "tid": 4, "ts": 1749487376643719, "dur": 2542, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/UnityEngine.UI.dll (+pdb)"}}, {"pid": 12345, "tid": 4, "ts": 1749487376646262, "dur": 5056, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1749487376651353, "dur": 115, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1749487376651478, "dur": 118, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1749487376651606, "dur": 70, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1749487376651739, "dur": 86, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1749487376651835, "dur": 128, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1749487376652025, "dur": 214, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1749487376652248, "dur": 106, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1749487376652366, "dur": 56, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1749487376652479, "dur": 54, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1749487376652608, "dur": 92, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1749487376652711, "dur": 79, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1749487376652794, "dur": 614, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1749487376653413, "dur": 88, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1749487376653527, "dur": 1400348, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1749487376519207, "dur": 14861, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1749487376534087, "dur": 91, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.ARModule.dll"}}, {"pid": 12345, "tid": 5, "ts": 1749487376534179, "dur": 83, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Tools\\ScriptUpdater\\ApiUpdater.MovedFromExtractor.exe"}}, {"pid": 12345, "tid": 5, "ts": 1749487376534075, "dur": 188, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ARModule.dll_A3A3243B74248B90.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1749487376534377, "dur": 73, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1749487376534458, "dur": 85, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.PerformanceReportingModule.dll"}}, {"pid": 12345, "tid": 5, "ts": 1749487376534457, "dur": 88, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.PerformanceReportingModule.dll_85F6C15D73A578BD.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1749487376534546, "dur": 52, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1749487376534607, "dur": 56, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.TextCoreTextEngineModule.dll"}}, {"pid": 12345, "tid": 5, "ts": 1749487376534605, "dur": 60, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TextCoreTextEngineModule.dll_BD74DBBEF40E5ADA.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1749487376534665, "dur": 66, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1749487376534738, "dur": 67, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.VirtualTexturingModule.dll"}}, {"pid": 12345, "tid": 5, "ts": 1749487376534737, "dur": 71, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.VirtualTexturingModule.dll_609DF0C9F4BC7F1C.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1749487376534808, "dur": 57, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1749487376534871, "dur": 82, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEditor.SceneViewModule.dll"}}, {"pid": 12345, "tid": 5, "ts": 1749487376534870, "dur": 86, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.SceneViewModule.dll_31272CE89BD91E91.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1749487376534956, "dur": 155, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1749487376535213, "dur": 152, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1749487376535406, "dur": 72, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1749487376535659, "dur": 73, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1749487376535752, "dur": 168, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1749487376535925, "dur": 159, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1749487376536157, "dur": 107, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1749487376536275, "dur": 59, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1749487376536435, "dur": 59, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/1723042715960067801.rsp"}}, {"pid": 12345, "tid": 5, "ts": 1749487376536494, "dur": 59, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1749487376536587, "dur": 55, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1749487376536646, "dur": 1602, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1749487376538248, "dur": 2056, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1749487376540304, "dur": 1563, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1749487376542724, "dur": 571, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.render-pipelines.universal@14.0.11\\Editor\\GlobalSettings\\UniversalRenderPipelineGlobalSettingsProvider.cs"}}, {"pid": 12345, "tid": 5, "ts": 1749487376541867, "dur": 2304, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1749487376544214, "dur": 515, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.shadergraph@14.0.11\\Editor\\Drawing\\Views\\Slots\\MultiIntegerSlotControlView.cs"}}, {"pid": 12345, "tid": 5, "ts": 1749487376544171, "dur": 1663, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1749487376545835, "dur": 820, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1749487376546655, "dur": 1778, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1749487376548434, "dur": 2071, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1749487376550506, "dur": 1342, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1749487376551848, "dur": 1347, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1749487376553195, "dur": 2113, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1749487376555309, "dur": 540, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.render-pipelines.core@14.0.11\\Runtime\\PostProcessing\\LensFlareComponentSRP.cs"}}, {"pid": 12345, "tid": 5, "ts": 1749487376555309, "dur": 1449, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1749487376556759, "dur": 1462, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1749487376558221, "dur": 1081, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1749487376559303, "dur": 548, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1749487376559851, "dur": 484, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1749487376560337, "dur": 155, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualStudio.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1749487376560493, "dur": 851, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1749487376561351, "dur": 628, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualStudio.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1749487376561980, "dur": 179, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1749487376562173, "dur": 606, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1749487376562783, "dur": 157, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.Collections.DocCodeSamples.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1749487376562941, "dur": 52, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1749487376563051, "dur": 57, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.DirectorModule.dll"}}, {"pid": 12345, "tid": 5, "ts": 1749487376562996, "dur": 555, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Collections.DocCodeSamples.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1749487376563552, "dur": 196, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1749487376563753, "dur": 160, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1749487376563914, "dur": 136, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Config.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1749487376564083, "dur": 351, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Config.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1749487376564434, "dur": 72, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1749487376564531, "dur": 54, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1749487376564606, "dur": 381, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1749487376564987, "dur": 80, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1749487376565094, "dur": 60, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.2D.Aseprite.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1749487376565180, "dur": 299, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.2D.Aseprite.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1749487376565479, "dur": 56, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1749487376565560, "dur": 75557, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1749487376642477, "dur": 56, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\api-ms-win-core-localization-l1-2-0.dll"}}, {"pid": 12345, "tid": 5, "ts": 1749487376643464, "dur": 52, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\System.Net.ServicePoint.dll"}}, {"pid": 12345, "tid": 5, "ts": 1749487376641118, "dur": 3884, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.Core.ShaderLibrary.dll (+pdb)"}}, {"pid": 12345, "tid": 5, "ts": 1749487376645003, "dur": 2280, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1749487376648611, "dur": 55, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEditor.TextCoreFontEngineModule.dll"}}, {"pid": 12345, "tid": 5, "ts": 1749487376647290, "dur": 2139, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/PsdPlugin.dll (+pdb)"}}, {"pid": 12345, "tid": 5, "ts": 1749487376649430, "dur": 1334, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1749487376651596, "dur": 76, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\clrjit.dll"}}, {"pid": 12345, "tid": 5, "ts": 1749487376650775, "dur": 2277, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.TextMeshPro.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 5, "ts": 1749487376653053, "dur": 155, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1749487376653256, "dur": 133639, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1749487376786898, "dur": 593923, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1900b0aE.dag\\Assembly-CSharp.dll"}}, {"pid": 12345, "tid": 5, "ts": 1749487376786897, "dur": 595015, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Assembly-CSharp.dll (+pdb)"}}, {"pid": 12345, "tid": 5, "ts": 1749487377383119, "dur": 135, "ph": "X", "name": "StoreTimestampsOfNonGeneratedInputFiles", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1749487377383714, "dur": 66118, "ph": "X", "name": "ILPostProcess", "args": {"detail": "Library/Bee/artifacts/1900b0aE.dag/post-processed/Assembly-CSharp.dll (+pdb)"}}, {"pid": 12345, "tid": 5, "ts": 1749487377466898, "dur": 583327, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1900b0aE.dag\\post-processed\\Assembly-CSharp.dll"}}, {"pid": 12345, "tid": 5, "ts": 1749487377466897, "dur": 583331, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Assembly-CSharp.dll"}}, {"pid": 12345, "tid": 5, "ts": 1749487378050267, "dur": 3166, "ph": "X", "name": "CopyFiles", "args": {"detail": "Library/ScriptAssemblies/Assembly-CSharp.dll"}}, {"pid": 12345, "tid": 6, "ts": 1749487376519269, "dur": 14969, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1749487376534398, "dur": 96, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1749487376534503, "dur": 73, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.RuntimeInitializeOnLoadManagerInitializerModule.dll"}}, {"pid": 12345, "tid": 6, "ts": 1749487376534501, "dur": 77, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.RuntimeInitializeOnLoadManagerInitializerModule.dll_E586C88FEB060A87.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1749487376534579, "dur": 56, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1749487376534663, "dur": 64, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.UnityCurlModule.dll"}}, {"pid": 12345, "tid": 6, "ts": 1749487376534661, "dur": 68, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityCurlModule.dll_F419673AF1271816.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1749487376534730, "dur": 61, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1749487376534797, "dur": 63, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEditor.CoreModule.dll"}}, {"pid": 12345, "tid": 6, "ts": 1749487376534796, "dur": 67, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.CoreModule.dll_4EAE93D6B17EFAD3.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1749487376534863, "dur": 57, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1749487376534928, "dur": 152, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\PlaybackEngines\\WindowsStandaloneSupport\\UnityEditor.WindowsStandalone.Extensions.dll"}}, {"pid": 12345, "tid": 6, "ts": 1749487376534926, "dur": 156, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.WindowsStandalone.Extensions.dll_9F8263619ADFDEFD.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1749487376535178, "dur": 129, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1749487376535315, "dur": 62, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1749487376535396, "dur": 79, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1749487376535536, "dur": 53, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1749487376535684, "dur": 132, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1749487376535861, "dur": 148, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1749487376536084, "dur": 96, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1749487376536215, "dur": 112, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1749487376536365, "dur": 56, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1749487376536615, "dur": 2030, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1749487376538645, "dur": 1125, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1749487376539770, "dur": 964, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1749487376540734, "dur": 867, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1749487376541601, "dur": 1662, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1749487376543263, "dur": 2678, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1749487376545942, "dur": 1427, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1749487376547370, "dur": 1778, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1749487376549149, "dur": 1867, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1749487376551016, "dur": 1279, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1749487376552296, "dur": 1765, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1749487376554061, "dur": 1092, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1749487376555153, "dur": 1569, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1749487376556723, "dur": 1257, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1749487376557981, "dur": 846, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1749487376558828, "dur": 58, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1749487376558926, "dur": 916, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1749487376559842, "dur": 464, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1749487376560307, "dur": 291, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.Searcher.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1749487376560629, "dur": 492, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Searcher.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1749487376561121, "dur": 946, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1749487376562076, "dur": 56, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1749487376562136, "dur": 479, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.2D.Tilemap.Extras.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1749487376562615, "dur": 271, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1749487376562942, "dur": 151, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.2D.Tilemap.Extras.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1749487376563144, "dur": 579, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.2D.Tilemap.Extras.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1749487376563724, "dur": 185, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1749487376563920, "dur": 53, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1749487376563975, "dur": 40910, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1749487376604886, "dur": 36178, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1749487376641623, "dur": 56, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Runtime.Handles.dll"}}, {"pid": 12345, "tid": 6, "ts": 1749487376642371, "dur": 51, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\api-ms-win-core-fibers-l1-1-0.dll"}}, {"pid": 12345, "tid": 6, "ts": 1749487376643554, "dur": 51, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\Unity.AspNetCore.NamedPipeSupport.dll"}}, {"pid": 12345, "tid": 6, "ts": 1749487376643715, "dur": 74, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.LocalizationModule.dll"}}, {"pid": 12345, "tid": 6, "ts": 1749487376641065, "dur": 3778, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Burst.dll (+pdb)"}}, {"pid": 12345, "tid": 6, "ts": 1749487376644844, "dur": 136, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1749487376645553, "dur": 110, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Xml.XPath.dll"}}, {"pid": 12345, "tid": 6, "ts": 1749487376644993, "dur": 2767, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.2D.Tilemap.Extras.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 6, "ts": 1749487376647761, "dur": 1185, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1749487376648951, "dur": 2024, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.2D.PixelPerfect.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 6, "ts": 1749487376650976, "dur": 543, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1749487376651537, "dur": 862, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1749487376652412, "dur": 74, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1749487376652492, "dur": 126, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1749487376652681, "dur": 53, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1749487376652748, "dur": 61, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1749487376652824, "dur": 58, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1749487376652890, "dur": 735, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1749487376653626, "dur": 1400288, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1749487376519312, "dur": 14941, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1749487376534263, "dur": 131, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.ClusterRendererModule.dll"}}, {"pid": 12345, "tid": 7, "ts": 1749487376534258, "dur": 141, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ClusterRendererModule.dll_49D0198E18ABFDE2.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1749487376534401, "dur": 122, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1749487376534727, "dur": 139, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1749487376534909, "dur": 201, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1749487376535131, "dur": 51, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1749487376535317, "dur": 155, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1749487376535520, "dur": 53, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1749487376535577, "dur": 60, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/4747638433968585886.rsp"}}, {"pid": 12345, "tid": 7, "ts": 1749487376535637, "dur": 50, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1749487376535932, "dur": 145, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1749487376536083, "dur": 163, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1749487376536251, "dur": 52, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1749487376536496, "dur": 63, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1749487376536591, "dur": 60, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1749487376536657, "dur": 1392, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1749487376538049, "dur": 1395, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1749487376539445, "dur": 1189, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1749487376540635, "dur": 1164, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1749487376541799, "dur": 1563, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1749487376543362, "dur": 772, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1749487376544173, "dur": 569, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.shadergraph@14.0.11\\Editor\\Generation\\Collections\\KeywordCollection.cs"}}, {"pid": 12345, "tid": 7, "ts": 1749487376544134, "dur": 1654, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1749487376546444, "dur": 589, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.shadergraph@14.0.11\\Editor\\Data\\Nodes\\Input\\Lighting\\BakedGINode.cs"}}, {"pid": 12345, "tid": 7, "ts": 1749487376545789, "dur": 3035, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1749487376548824, "dur": 1642, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1749487376551115, "dur": 523, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting@1.9.4\\Editor\\VisualScripting.Core\\Plugin\\Changelogs\\LegacyLudiqGraphs\\Changelog_1_4_7.cs"}}, {"pid": 12345, "tid": 7, "ts": 1749487376550467, "dur": 1947, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1749487376552414, "dur": 1182, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1749487376555359, "dur": 532, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting@1.9.4\\Runtime\\VisualScripting.Core\\Graphs\\IGraphRoot.cs"}}, {"pid": 12345, "tid": 7, "ts": 1749487376553596, "dur": 2864, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1749487376556461, "dur": 1034, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1749487376557495, "dur": 1437, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1749487376558933, "dur": 902, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1749487376559836, "dur": 466, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1749487376560303, "dur": 167, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1749487376560533, "dur": 84, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEditor.EditorToolbarModule.dll"}}, {"pid": 12345, "tid": 7, "ts": 1749487376560514, "dur": 939, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1749487376561453, "dur": 749, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1749487376562211, "dur": 1677, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1749487376563915, "dur": 112, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipeline.Universal.ShaderLibrary.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1749487376564068, "dur": 340, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipeline.Universal.ShaderLibrary.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1749487376564409, "dur": 77, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1749487376564520, "dur": 76590, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1749487376641691, "dur": 63, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Runtime.Serialization.Xml.dll"}}, {"pid": 12345, "tid": 7, "ts": 1749487376642247, "dur": 168, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Threading.ThreadPool.dll"}}, {"pid": 12345, "tid": 7, "ts": 1749487376642647, "dur": 97, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\api-ms-win-crt-filesystem-l1-1-0.dll"}}, {"pid": 12345, "tid": 7, "ts": 1749487376642812, "dur": 193, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\dbgshim.dll"}}, {"pid": 12345, "tid": 7, "ts": 1749487376643387, "dur": 107, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\Microsoft.Extensions.Localization.Abstractions.dll"}}, {"pid": 12345, "tid": 7, "ts": 1749487376641111, "dur": 3475, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Collections.dll (+pdb)"}}, {"pid": 12345, "tid": 7, "ts": 1749487376644587, "dur": 621, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1749487376645555, "dur": 50, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Runtime.InteropServices.dll"}}, {"pid": 12345, "tid": 7, "ts": 1749487376647480, "dur": 51, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.IO.IsolatedStorage.dll"}}, {"pid": 12345, "tid": 7, "ts": 1749487376645215, "dur": 2867, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/PPv2URPConverters.dll (+pdb)"}}, {"pid": 12345, "tid": 7, "ts": 1749487376648082, "dur": 239, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1749487376648609, "dur": 135, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.ComponentModel.dll"}}, {"pid": 12345, "tid": 7, "ts": 1749487376650739, "dur": 61, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\System.IO.Compression.FileSystem.dll"}}, {"pid": 12345, "tid": 7, "ts": 1749487376648332, "dur": 2519, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.2D.Psdimporter.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 7, "ts": 1749487376650855, "dur": 329, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1749487376651662, "dur": 53, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Net.WebSockets.Client.dll"}}, {"pid": 12345, "tid": 7, "ts": 1749487376651956, "dur": 107, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Security.Cryptography.Algorithms.dll"}}, {"pid": 12345, "tid": 7, "ts": 1749487376652235, "dur": 101, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Collections.Concurrent.dll"}}, {"pid": 12345, "tid": 7, "ts": 1749487376652425, "dur": 63, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Net.Http.Rtc.dll"}}, {"pid": 12345, "tid": 7, "ts": 1749487376652665, "dur": 54, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\System.dll"}}, {"pid": 12345, "tid": 7, "ts": 1749487376651190, "dur": 2574, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.2D.Tilemap.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 7, "ts": 1749487376653847, "dur": 1400001, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1749487376519355, "dur": 14910, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1749487376534442, "dur": 98, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.ParticleSystemModule.dll"}}, {"pid": 12345, "tid": 8, "ts": 1749487376534441, "dur": 101, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ParticleSystemModule.dll_D218FBB45FD12FE4.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1749487376534543, "dur": 80, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1749487376534631, "dur": 65, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.UmbraModule.dll"}}, {"pid": 12345, "tid": 8, "ts": 1749487376534630, "dur": 69, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UmbraModule.dll_0E723EAE4164004E.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1749487376534699, "dur": 69, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1749487376534775, "dur": 61, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEditor.DeviceSimulatorModule.dll"}}, {"pid": 12345, "tid": 8, "ts": 1749487376534774, "dur": 64, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.DeviceSimulatorModule.dll_4423571CA8F0F05E.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1749487376534838, "dur": 56, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1749487376534902, "dur": 53, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEditor.GraphViewModule.dll"}}, {"pid": 12345, "tid": 8, "ts": 1749487376534900, "dur": 57, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.GraphViewModule.dll_F27749E0A6B65183.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1749487376534958, "dur": 142, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1749487376535176, "dur": 162, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1749487376535379, "dur": 100, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1749487376535571, "dur": 50, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Runtime.rsp"}}, {"pid": 12345, "tid": 8, "ts": 1749487376535870, "dur": 183, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1749487376536057, "dur": 51, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.PlasticSCM.Editor.rsp"}}, {"pid": 12345, "tid": 8, "ts": 1749487376536109, "dur": 61, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1749487376536211, "dur": 65, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1749487376536287, "dur": 119, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1749487376536432, "dur": 59, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/13445476680238899994.rsp"}}, {"pid": 12345, "tid": 8, "ts": 1749487376536540, "dur": 114, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/6841316898876630997.rsp"}}, {"pid": 12345, "tid": 8, "ts": 1749487376536654, "dur": 77, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1749487376536735, "dur": 1494, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1749487376538229, "dur": 1195, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1749487376539425, "dur": 823, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1749487376540248, "dur": 2208, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1749487376542456, "dur": 2505, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1749487376544961, "dur": 2472, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1749487376547434, "dur": 686, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1749487376548121, "dur": 1527, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1749487376549648, "dur": 1295, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1749487376550944, "dur": 2518, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1749487376553462, "dur": 1839, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1749487376555302, "dur": 1292, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1749487376556594, "dur": 1174, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1749487376557769, "dur": 865, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1749487376558634, "dur": 833, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1749487376559468, "dur": 385, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1749487376559853, "dur": 476, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1749487376560337, "dur": 360, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.PlasticSCM.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1749487376560698, "dur": 61, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1749487376560761, "dur": 1208, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.PlasticSCM.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": 1749487376561969, "dur": 153, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1749487376562128, "dur": 155, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.Mathematics.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1749487376562283, "dur": 277, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1749487376562564, "dur": 474, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Mathematics.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": 1749487376563038, "dur": 93, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1749487376563177, "dur": 737, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1749487376563915, "dur": 142, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1749487376564058, "dur": 52, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1749487376564114, "dur": 462, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": 1749487376564576, "dur": 65, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1749487376564668, "dur": 56, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.ShaderGraph.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1749487376564747, "dur": 902, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.ShaderGraph.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": 1749487376565650, "dur": 69, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1749487376565747, "dur": 53, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1749487376565820, "dur": 398, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": 1749487376566218, "dur": 52, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1749487376566301, "dur": 54, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.2D.PixelPerfect.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1749487376566376, "dur": 198, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.2D.PixelPerfect.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": 1749487376566574, "dur": 59, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1749487376566660, "dur": 74458, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1749487376642036, "dur": 215, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Globalization.dll"}}, {"pid": 12345, "tid": 8, "ts": 1749487376642839, "dur": 52, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\Microsoft.AspNetCore.Connections.Abstractions.dll"}}, {"pid": 12345, "tid": 8, "ts": 1749487376643902, "dur": 57, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEditor.UIBuilderModule.dll"}}, {"pid": 12345, "tid": 8, "ts": 1749487376641119, "dur": 3880, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.Universal.Config.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 8, "ts": 1749487376645000, "dur": 490, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1749487376645559, "dur": 59, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEditor.UnityConnectModule.dll"}}, {"pid": 12345, "tid": 8, "ts": 1749487376645503, "dur": 2416, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.ShaderGraph.Utilities.dll (+pdb)"}}, {"pid": 12345, "tid": 8, "ts": 1749487376647920, "dur": 72, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1749487376648610, "dur": 52, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\api-ms-win-core-file-l2-1-0.dll"}}, {"pid": 12345, "tid": 8, "ts": 1749487376648012, "dur": 2398, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.2D.Aseprite.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 8, "ts": 1749487376650411, "dur": 1357, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1749487376651777, "dur": 56, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1749487376651839, "dur": 73, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1749487376651919, "dur": 54, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1749487376651979, "dur": 77, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1749487376652116, "dur": 57, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1749487376652179, "dur": 67, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1749487376652258, "dur": 149, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1749487376652414, "dur": 106, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1749487376652533, "dur": 54, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1749487376652633, "dur": 62, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1749487376652702, "dur": 56, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1749487376652766, "dur": 66, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1749487376652835, "dur": 674, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1749487376653509, "dur": 1400273, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1749487376519382, "dur": 14955, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1749487376534360, "dur": 85, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.DSPGraphModule.dll"}}, {"pid": 12345, "tid": 9, "ts": 1749487376534348, "dur": 99, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.DSPGraphModule.dll_4012322E9A5614BC.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1749487376534447, "dur": 52, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1749487376534508, "dur": 62, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.SharedInternalsModule.dll"}}, {"pid": 12345, "tid": 9, "ts": 1749487376534507, "dur": 66, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.SharedInternalsModule.dll_39D3561411FEB126.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1749487376534573, "dur": 88, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1749487376534694, "dur": 101, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.UnityWebRequestWWWModule.dll"}}, {"pid": 12345, "tid": 9, "ts": 1749487376534693, "dur": 105, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestWWWModule.dll_8837211AE3CE1746.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1749487376534799, "dur": 157, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1749487376534965, "dur": 107, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.collab-proxy@2.5.2\\Lib\\Editor\\PlasticSCM\\Unity.Plastic.Newtonsoft.Json.dll"}}, {"pid": 12345, "tid": 9, "ts": 1749487376534963, "dur": 111, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Plastic.Newtonsoft.Json.dll_7880F7755F74374F.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1749487376535075, "dur": 74, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1749487376535188, "dur": 59, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1749487376535295, "dur": 112, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1749487376535424, "dur": 60, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1749487376535506, "dur": 50, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1749487376535666, "dur": 54, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1749487376535928, "dur": 140, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1749487376536175, "dur": 90, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1749487376536281, "dur": 87, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1749487376536372, "dur": 109, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/9870516708743257357.rsp"}}, {"pid": 12345, "tid": 9, "ts": 1749487376536481, "dur": 52, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1749487376536634, "dur": 1624, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1749487376538627, "dur": 658, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.timeline@1.7.6\\Editor\\treeview\\TimelineClipUnion.cs"}}, {"pid": 12345, "tid": 9, "ts": 1749487376538258, "dur": 1826, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1749487376540084, "dur": 805, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1749487376540890, "dur": 1683, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1749487376542574, "dur": 794, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1749487376544092, "dur": 647, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.shadergraph@14.0.11\\Editor\\Serialization\\JsonData.cs"}}, {"pid": 12345, "tid": 9, "ts": 1749487376543368, "dur": 1957, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1749487376545326, "dur": 2133, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1749487376547459, "dur": 1183, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1749487376548642, "dur": 1800, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1749487376550443, "dur": 1352, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1749487376551796, "dur": 1601, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1749487376553397, "dur": 1167, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1749487376554565, "dur": 1646, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1749487376556211, "dur": 878, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1749487376557089, "dur": 1320, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1749487376558410, "dur": 1055, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1749487376559465, "dur": 374, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1749487376559840, "dur": 471, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1749487376560312, "dur": 482, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.2D.Tilemap.Extras.dll.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1749487376560794, "dur": 1296, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1749487376562094, "dur": 107, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.Collections.dll.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1749487376562236, "dur": 423, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Collections.dll (+2 others)"}}, {"pid": 12345, "tid": 9, "ts": 1749487376562660, "dur": 78, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1749487376562771, "dur": 153, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.Collections.BurstCompatibilityGen.dll.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1749487376562925, "dur": 56, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1749487376562985, "dur": 554, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Collections.BurstCompatibilityGen.dll (+2 others)"}}, {"pid": 12345, "tid": 9, "ts": 1749487376563540, "dur": 550, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1749487376564095, "dur": 77028, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1749487376642909, "dur": 57, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEditor.GraphViewModule.dll"}}, {"pid": 12345, "tid": 9, "ts": 1749487376643350, "dur": 60, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Runtime.Extensions.dll"}}, {"pid": 12345, "tid": 9, "ts": 1749487376641124, "dur": 2734, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.ShaderGraph.ShaderGraphLibrary.dll (+pdb)"}}, {"pid": 12345, "tid": 9, "ts": 1749487376643859, "dur": 497, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1749487376645941, "dur": 53, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netfx\\System.Drawing.dll"}}, {"pid": 12345, "tid": 9, "ts": 1749487376644367, "dur": 2405, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Timeline.dll (+pdb)"}}, {"pid": 12345, "tid": 9, "ts": 1749487376646773, "dur": 1218, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1749487376648530, "dur": 50, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Text.RegularExpressions.dll"}}, {"pid": 12345, "tid": 9, "ts": 1749487376648610, "dur": 135, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Xml.XPath.dll"}}, {"pid": 12345, "tid": 9, "ts": 1749487376648892, "dur": 179, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.IO.FileSystem.Primitives.dll"}}, {"pid": 12345, "tid": 9, "ts": 1749487376648005, "dur": 3164, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.2D.IK.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 9, "ts": 1749487376651173, "dur": 405, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1749487376651586, "dur": 60, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1749487376651816, "dur": 429, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1749487376652284, "dur": 69, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1749487376652361, "dur": 242, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1749487376652611, "dur": 60, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1749487376652679, "dur": 64, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1749487376652751, "dur": 151, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1749487376652906, "dur": 757, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1749487376653663, "dur": 1400156, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1749487376519498, "dur": 14897, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1749487376534403, "dur": 69, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.IMGUIModule.dll"}}, {"pid": 12345, "tid": 10, "ts": 1749487376534400, "dur": 73, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.IMGUIModule.dll_CBFD31FC7A6EE8BC.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1749487376534889, "dur": 186, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1749487376535079, "dur": 69, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1749487376535163, "dur": 50, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1749487376535266, "dur": 53, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1749487376535773, "dur": 216, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1749487376536030, "dur": 93, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1749487376536418, "dur": 60, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/17525389461119239690.rsp"}}, {"pid": 12345, "tid": 10, "ts": 1749487376536601, "dur": 1398, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1749487376538000, "dur": 807, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1749487376538807, "dur": 1816, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1749487376540623, "dur": 1383, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1749487376542773, "dur": 530, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.render-pipelines.universal@14.0.11\\Editor\\Camera\\UniversalRenderPipelineCameraUI.Drawers.cs"}}, {"pid": 12345, "tid": 10, "ts": 1749487376544063, "dur": 660, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.render-pipelines.universal@14.0.11\\Editor\\AssetPostProcessors\\ModelPostProcessor.cs"}}, {"pid": 12345, "tid": 10, "ts": 1749487376542006, "dur": 2803, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1749487376544809, "dur": 957, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1749487376545766, "dur": 1539, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1749487376547305, "dur": 1223, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1749487376548530, "dur": 1441, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1749487376549971, "dur": 1826, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1749487376551797, "dur": 1568, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1749487376553365, "dur": 1626, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1749487376554992, "dur": 1383, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1749487376556375, "dur": 1559, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1749487376557934, "dur": 1524, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1749487376559459, "dur": 393, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1749487376559852, "dur": 646, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1749487376560498, "dur": 367, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.Cursor.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1749487376560865, "dur": 108, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1749487376560979, "dur": 677, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Cursor.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 10, "ts": 1749487376561657, "dur": 125, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1749487376561786, "dur": 173, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1749487376561959, "dur": 99, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1749487376562065, "dur": 570, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 10, "ts": 1749487376562635, "dur": 90, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1749487376562732, "dur": 223, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1749487376562959, "dur": 84, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1749487376563044, "dur": 230, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.2D.Animation.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1749487376563275, "dur": 72, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1749487376563352, "dur": 481, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.2D.Animation.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 10, "ts": 1749487376563834, "dur": 146, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1749487376564039, "dur": 77072, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1749487376641112, "dur": 2579, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Mathematics.dll (+pdb)"}}, {"pid": 12345, "tid": 10, "ts": 1749487376643692, "dur": 72, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1749487376644308, "dur": 58, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.IO.IsolatedStorage.dll"}}, {"pid": 12345, "tid": 10, "ts": 1749487376645555, "dur": 58, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Reflection.Emit.dll"}}, {"pid": 12345, "tid": 10, "ts": 1749487376643769, "dur": 2503, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.2D.PixelPerfect.dll (+pdb)"}}, {"pid": 12345, "tid": 10, "ts": 1749487376646273, "dur": 1158, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1749487376648180, "dur": 113, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.IO.Pipes.dll"}}, {"pid": 12345, "tid": 10, "ts": 1749487376648612, "dur": 174, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\api-ms-win-core-profile-l1-1-0.dll"}}, {"pid": 12345, "tid": 10, "ts": 1749487376649065, "dur": 79, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\Microsoft.AspNetCore.DataProtection.Extensions.dll"}}, {"pid": 12345, "tid": 10, "ts": 1749487376650875, "dur": 81, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting@1.9.4\\Editor\\VisualScripting.Core\\Dependencies\\YamlDotNet\\Unity.VisualScripting.YamlDotNet.dll"}}, {"pid": 12345, "tid": 10, "ts": 1749487376647445, "dur": 3531, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Collections.DocCodeSamples.dll (+pdb)"}}, {"pid": 12345, "tid": 10, "ts": 1749487376650977, "dur": 82, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1749487376651541, "dur": 91, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Data.Common.dll"}}, {"pid": 12345, "tid": 10, "ts": 1749487376652199, "dur": 109, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\Microsoft.Extensions.Diagnostics.HealthChecks.Abstractions.dll"}}, {"pid": 12345, "tid": 10, "ts": 1749487376652309, "dur": 84, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\Microsoft.Extensions.Diagnostics.HealthChecks.dll"}}, {"pid": 12345, "tid": 10, "ts": 1749487376651068, "dur": 2362, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Burst.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 10, "ts": 1749487376653430, "dur": 147, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1749487376653635, "dur": 1400188, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1749487376519596, "dur": 14821, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1749487376534423, "dur": 51, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.InputModule.dll_FA135A462A75797F.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1749487376534529, "dur": 50, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1749487376534579, "dur": 108, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ScreenCaptureModule.dll_116A6FADE34C1D2F.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1749487376534727, "dur": 134, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1749487376534984, "dur": 106, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1749487376535094, "dur": 63, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.NVIDIAModule.dll"}}, {"pid": 12345, "tid": 11, "ts": 1749487376535093, "dur": 67, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.NVIDIAModule.dll_882D400E4D49662A.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1749487376535160, "dur": 132, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1749487376535302, "dur": 65, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1749487376535458, "dur": 127, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1749487376535596, "dur": 54, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1749487376535709, "dur": 121, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1749487376535934, "dur": 129, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1749487376536603, "dur": 2089, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1749487376538693, "dur": 2172, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1749487376540866, "dur": 1286, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1749487376542152, "dur": 1967, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1749487376544160, "dur": 560, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.shadergraph@14.0.11\\Editor\\Generation\\Descriptors\\PassDescriptor.cs"}}, {"pid": 12345, "tid": 11, "ts": 1749487376546027, "dur": 652, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.shadergraph@14.0.11\\Editor\\Generation\\Data\\ConditionalField.cs"}}, {"pid": 12345, "tid": 11, "ts": 1749487376544119, "dur": 3396, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1749487376547516, "dur": 1871, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1749487376549387, "dur": 1365, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1749487376550785, "dur": 846, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1749487376551632, "dur": 1754, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1749487376553387, "dur": 1448, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1749487376554835, "dur": 2178, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1749487376557014, "dur": 1142, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1749487376558191, "dur": 1150, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1749487376559342, "dur": 502, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1749487376559844, "dur": 464, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1749487376560334, "dur": 153, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.2D.Common.Path.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1749487376560542, "dur": 59, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEditor.dll"}}, {"pid": 12345, "tid": 11, "ts": 1749487376560527, "dur": 669, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.2D.Common.Path.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 11, "ts": 1749487376561197, "dur": 430, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1749487376562037, "dur": 266, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Security.Principal.dll"}}, {"pid": 12345, "tid": 11, "ts": 1749487376561695, "dur": 667, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Shaders.dll (+2 others)"}}, {"pid": 12345, "tid": 11, "ts": 1749487376562362, "dur": 552, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1749487376562951, "dur": 141, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1749487376563092, "dur": 173, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1749487376563265, "dur": 130, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Core.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1749487376563395, "dur": 60, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1749487376563458, "dur": 1260, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Core.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 11, "ts": 1749487376564719, "dur": 84, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1749487376564876, "dur": 76191, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1749487376642581, "dur": 89, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\System.Data.dll"}}, {"pid": 12345, "tid": 11, "ts": 1749487376641070, "dur": 4092, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipeline.Universal.ShaderLibrary.dll (+pdb)"}}, {"pid": 12345, "tid": 11, "ts": 1749487376645162, "dur": 4092, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1749487376649375, "dur": 185, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.JSONSerializeModule.dll"}}, {"pid": 12345, "tid": 11, "ts": 1749487376650875, "dur": 170, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\System.Data.DataSetExtensions.dll"}}, {"pid": 12345, "tid": 11, "ts": 1749487376651046, "dur": 86, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\System.Data.dll"}}, {"pid": 12345, "tid": 11, "ts": 1749487376651543, "dur": 58, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.ClusterRendererModule.dll"}}, {"pid": 12345, "tid": 11, "ts": 1749487376649259, "dur": 3037, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Mathematics.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 11, "ts": 1749487376652297, "dur": 509, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1749487376652819, "dur": 333, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1749487376653160, "dur": 660, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1749487376653865, "dur": 1399997, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1749487376519627, "dur": 14798, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1749487376534433, "dur": 113, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.LocalizationModule.dll"}}, {"pid": 12345, "tid": 12, "ts": 1749487376534428, "dur": 119, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.LocalizationModule.dll_A1665A0073A9D8C8.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1749487376534571, "dur": 68, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.LocalizationModule.dll_A1665A0073A9D8C8.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1749487376534642, "dur": 51, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.UnityAnalyticsModule.dll"}}, {"pid": 12345, "tid": 12, "ts": 1749487376534640, "dur": 55, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityAnalyticsModule.dll_F1B86C7E0BF881E0.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1749487376534696, "dur": 60, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1749487376534806, "dur": 276, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1749487376535436, "dur": 56, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1749487376535531, "dur": 91, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1749487376535632, "dur": 155, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1749487376535887, "dur": 227, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1749487376536402, "dur": 86, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/4014436084419441659.rsp"}}, {"pid": 12345, "tid": 12, "ts": 1749487376536647, "dur": 954, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1749487376537601, "dur": 1418, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1749487376539019, "dur": 1863, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1749487376540883, "dur": 1499, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1749487376542383, "dur": 1972, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1749487376544355, "dur": 2300, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1749487376547066, "dur": 995, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.burst@1.8.15\\Editor\\BurstPlatformAotSettings.cs"}}, {"pid": 12345, "tid": 12, "ts": 1749487376546655, "dur": 2262, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1749487376548918, "dur": 1540, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1749487376550459, "dur": 1791, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1749487376553166, "dur": 582, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.render-pipelines.core@14.0.11\\Editor\\CoreEditorUtils.cs"}}, {"pid": 12345, "tid": 12, "ts": 1749487376553805, "dur": 755, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.render-pipelines.core@14.0.11\\Editor\\CoreEditorDrawers.cs"}}, {"pid": 12345, "tid": 12, "ts": 1749487376552250, "dur": 2474, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1749487376554724, "dur": 1643, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1749487376556367, "dur": 1667, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1749487376558034, "dur": 263, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1749487376558297, "dur": 1098, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1749487376559395, "dur": 435, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1749487376559886, "dur": 625, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1749487376560512, "dur": 153, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.ShaderLibrary.dll.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1749487376560666, "dur": 1020, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1749487376562037, "dur": 486, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Runtime.Serialization.Json.dll"}}, {"pid": 12345, "tid": 12, "ts": 1749487376561695, "dur": 971, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.ShaderLibrary.dll (+2 others)"}}, {"pid": 12345, "tid": 12, "ts": 1749487376562667, "dur": 116, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1749487376562828, "dur": 53, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1749487376562883, "dur": 227, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1749487376563110, "dur": 810, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1749487376563920, "dur": 1085, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1749487376565009, "dur": 129, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Flow.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1749487376565139, "dur": 55, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1749487376565199, "dur": 651, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Flow.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 12, "ts": 1749487376565851, "dur": 50, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1749487376565928, "dur": 54, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.State.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1749487376566000, "dur": 261, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.State.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 12, "ts": 1749487376566261, "dur": 56, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1749487376566347, "dur": 52, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Shared.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1749487376566417, "dur": 195, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Shared.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 12, "ts": 1749487376566612, "dur": 55, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1749487376566696, "dur": 64, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp.dll.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1749487376566780, "dur": 192, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp.dll (+2 others)"}}, {"pid": 12345, "tid": 12, "ts": 1749487376567265, "dur": 214997, "ph": "X", "name": "Csc", "args": {"detail": "Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp.dll (+2 others)"}}, {"pid": 12345, "tid": 12, "ts": 1749487376787142, "dur": 56388, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1900b0aE.dag\\Assembly-CSharp.ref.dll"}}, {"pid": 12345, "tid": 12, "ts": 1749487376786890, "dur": 56696, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp-Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 12, "ts": 1749487376843587, "dur": 94, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1749487376843700, "dur": 537117, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1900b0aE.dag\\Assembly-CSharp.dll"}}, {"pid": 12345, "tid": 12, "ts": 1749487376843698, "dur": 538203, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Assembly-CSharp-Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 12, "ts": 1749487377383600, "dur": 129, "ph": "X", "name": "StoreTimestampsOfNonGeneratedInputFiles", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1749487377383738, "dur": 60392, "ph": "X", "name": "ILPostProcess", "args": {"detail": "Library/Bee/artifacts/1900b0aE.dag/post-processed/Assembly-CSharp-Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 12, "ts": 1749487377462663, "dur": 99, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1900b0aE.dag\\post-processed\\Assembly-CSharp-Editor.pdb"}}, {"pid": 12345, "tid": 12, "ts": 1749487377462662, "dur": 101, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Assembly-CSharp-Editor.pdb"}}, {"pid": 12345, "tid": 12, "ts": 1749487377462803, "dur": 591101, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1749487376519692, "dur": 14883, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1749487376534590, "dur": 52, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.TerrainPhysicsModule.dll"}}, {"pid": 12345, "tid": 13, "ts": 1749487376534587, "dur": 58, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TerrainPhysicsModule.dll_D293A48779142A71.mvfrm"}}, {"pid": 12345, "tid": 13, "ts": 1749487376534740, "dur": 77, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1749487376534839, "dur": 208, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEditor.UIElementsSamplesModule.dll"}}, {"pid": 12345, "tid": 13, "ts": 1749487376534838, "dur": 211, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UIElementsSamplesModule.dll_7406CA3F32B5E22E.mvfrm"}}, {"pid": 12345, "tid": 13, "ts": 1749487376535104, "dur": 63, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/UnityEditor.TestRunner.rsp"}}, {"pid": 12345, "tid": 13, "ts": 1749487376535167, "dur": 62, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1749487376535234, "dur": 52, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.burst@1.8.15\\Unity.Burst.CodeGen\\Unity.Burst.Cecil.dll"}}, {"pid": 12345, "tid": 13, "ts": 1749487376535233, "dur": 55, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Burst.Cecil.dll_62273FC84B3E0F2D.mvfrm"}}, {"pid": 12345, "tid": 13, "ts": 1749487376535367, "dur": 50, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1749487376535454, "dur": 57, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1749487376535515, "dur": 65, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1749487376535590, "dur": 77, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1749487376535774, "dur": 166, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1749487376536122, "dur": 117, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1749487376536250, "dur": 50, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1749487376536386, "dur": 83, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/5803490648119114145.rsp"}}, {"pid": 12345, "tid": 13, "ts": 1749487376536554, "dur": 127, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1749487376536684, "dur": 1150, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1749487376537834, "dur": 1286, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1749487376539120, "dur": 1494, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1749487376540615, "dur": 952, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1749487376541568, "dur": 1994, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1749487376543562, "dur": 1297, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1749487376544859, "dur": 1481, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1749487376546341, "dur": 1889, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1749487376548231, "dur": 1200, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1749487376549431, "dur": 1594, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1749487376551026, "dur": 1637, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1749487376552664, "dur": 1093, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1749487376553758, "dur": 1698, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1749487376555457, "dur": 1090, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1749487376556547, "dur": 2266, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1749487376558813, "dur": 70, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1749487376558883, "dur": 120, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1749487376559003, "dur": 843, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1749487376559847, "dur": 460, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1749487376560308, "dur": 168, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.ShaderGraph.Utilities.dll.mvfrm"}}, {"pid": 12345, "tid": 13, "ts": 1749487376560476, "dur": 117, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1749487376560599, "dur": 515, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.ShaderGraph.Utilities.dll (+2 others)"}}, {"pid": 12345, "tid": 13, "ts": 1749487376561114, "dur": 1946, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1749487376563109, "dur": 830, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1749487376563939, "dur": 2762, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1749487376566702, "dur": 72, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp-Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 13, "ts": 1749487376566804, "dur": 74309, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1749487376641114, "dur": 2627, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.VisualScripting.State.dll (+pdb)"}}, {"pid": 12345, "tid": 13, "ts": 1749487376643742, "dur": 616, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1749487376645555, "dur": 55, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\Microsoft.Extensions.FileProviders.Abstractions.dll"}}, {"pid": 12345, "tid": 13, "ts": 1749487376644371, "dur": 2385, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.2D.Common.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 13, "ts": 1749487376646756, "dur": 114, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1749487376648541, "dur": 113, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netfx\\System.Data.dll"}}, {"pid": 12345, "tid": 13, "ts": 1749487376648982, "dur": 316, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Diagnostics.Debug.dll"}}, {"pid": 12345, "tid": 13, "ts": 1749487376646878, "dur": 2772, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Collections.BurstCompatibilityGen.dll (+pdb)"}}, {"pid": 12345, "tid": 13, "ts": 1749487376649651, "dur": 2187, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1749487376651938, "dur": 63, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1749487376652069, "dur": 127, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1749487376652283, "dur": 75, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1749487376652490, "dur": 762, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1749487376653293, "dur": 809413, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1749487377462708, "dur": 111786, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1900b0aE.dag\\post-processed\\Assembly-CSharp-Editor.dll"}}, {"pid": 12345, "tid": 13, "ts": 1749487377462707, "dur": 111790, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Assembly-CSharp-Editor.dll"}}, {"pid": 12345, "tid": 13, "ts": 1749487377574498, "dur": 107, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1749487377574626, "dur": 479170, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1749487376519649, "dur": 14813, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1749487376534663, "dur": 93, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UIElementsModule.dll_D441A9CFEC32007A.mvfrm"}}, {"pid": 12345, "tid": 14, "ts": 1749487376534913, "dur": 167, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\PlaybackEngines\\WebGLSupport\\UnityEditor.WebGL.Extensions.dll"}}, {"pid": 12345, "tid": 14, "ts": 1749487376534912, "dur": 169, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.WebGL.Extensions.dll_C8504B54661C208D.mvfrm"}}, {"pid": 12345, "tid": 14, "ts": 1749487376535082, "dur": 52, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1749487376535168, "dur": 126, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1749487376535414, "dur": 57, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Config.Runtime.rsp"}}, {"pid": 12345, "tid": 14, "ts": 1749487376535472, "dur": 69, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1749487376535546, "dur": 128, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1749487376535735, "dur": 56, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.2D.Animation.Editor.rsp"}}, {"pid": 12345, "tid": 14, "ts": 1749487376536038, "dur": 74, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1749487376536121, "dur": 115, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1749487376536316, "dur": 61, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1749487376536591, "dur": 202, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1749487376536808, "dur": 1292, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1749487376538101, "dur": 1274, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1749487376539376, "dur": 1459, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1749487376540835, "dur": 1747, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1749487376543911, "dur": 811, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting@1.9.4\\Editor\\VisualScripting.Flow\\Plugin\\BoltFlowManifest.cs"}}, {"pid": 12345, "tid": 14, "ts": 1749487376542582, "dur": 2557, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1749487376545139, "dur": 1498, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1749487376546638, "dur": 1776, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1749487376548414, "dur": 1781, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1749487376550195, "dur": 1868, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1749487376552063, "dur": 1578, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1749487376553642, "dur": 1249, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1749487376554891, "dur": 1704, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1749487376556596, "dur": 904, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1749487376557500, "dur": 1240, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1749487376558842, "dur": 96, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1749487376558938, "dur": 919, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1749487376559858, "dur": 459, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1749487376560319, "dur": 486, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.2D.Tilemap.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 14, "ts": 1749487376560806, "dur": 52, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1749487376560861, "dur": 806, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.2D.Tilemap.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 14, "ts": 1749487376561667, "dur": 128, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1749487376561799, "dur": 164, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/PsdPlugin.dll.mvfrm"}}, {"pid": 12345, "tid": 14, "ts": 1749487376562036, "dur": 55, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEditor.QuickSearchModule.dll"}}, {"pid": 12345, "tid": 14, "ts": 1749487376562016, "dur": 502, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/PsdPlugin.dll (+2 others)"}}, {"pid": 12345, "tid": 14, "ts": 1749487376562518, "dur": 143, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1749487376562664, "dur": 216, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1749487376562934, "dur": 112, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1749487376563047, "dur": 164, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.2D.SpriteShape.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 14, "ts": 1749487376563397, "dur": 64, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netfx\\System.Xml.dll"}}, {"pid": 12345, "tid": 14, "ts": 1749487376563236, "dur": 468, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.2D.SpriteShape.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 14, "ts": 1749487376563704, "dur": 72, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1749487376563812, "dur": 105, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1749487376563917, "dur": 757, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1749487376564675, "dur": 106, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.2D.SpriteShape.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 14, "ts": 1749487376564781, "dur": 60, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1749487376564846, "dur": 445, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.2D.SpriteShape.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 14, "ts": 1749487376565292, "dur": 171, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1749487376565517, "dur": 75603, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1749487376641215, "dur": 231, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEditor.UIElementsModule.dll"}}, {"pid": 12345, "tid": 14, "ts": 1749487376643909, "dur": 177, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\System.Xml.XmlDocument.dll"}}, {"pid": 12345, "tid": 14, "ts": 1749487376641122, "dur": 3644, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.InternalAPIEngineBridge.001.dll (+pdb)"}}, {"pid": 12345, "tid": 14, "ts": 1749487376644766, "dur": 98, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1749487376645948, "dur": 374, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\Microsoft.AspNetCore.Mvc.Razor.dll"}}, {"pid": 12345, "tid": 14, "ts": 1749487376644873, "dur": 2893, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.VisualScripting.SettingsProvider.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 14, "ts": 1749487376647766, "dur": 905, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1749487376648978, "dur": 52, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Buffers.dll"}}, {"pid": 12345, "tid": 14, "ts": 1749487376648685, "dur": 2101, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.Universal.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 14, "ts": 1749487376650787, "dur": 200, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1749487376651303, "dur": 83, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Net.Security.dll"}}, {"pid": 12345, "tid": 14, "ts": 1749487376650993, "dur": 2247, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.VisualScripting.State.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 14, "ts": 1749487376653241, "dur": 393, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1749487376653687, "dur": 1400148, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1749487376519669, "dur": 14801, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1749487376534503, "dur": 123, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.PhysicsModule.dll"}}, {"pid": 12345, "tid": 15, "ts": 1749487376534492, "dur": 138, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.PhysicsModule.dll_3B0CE8B1A0A2F90C.mvfrm"}}, {"pid": 12345, "tid": 15, "ts": 1749487376534658, "dur": 92, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.PhysicsModule.dll_3B0CE8B1A0A2F90C.mvfrm"}}, {"pid": 12345, "tid": 15, "ts": 1749487376534752, "dur": 150, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.WindModule.dll"}}, {"pid": 12345, "tid": 15, "ts": 1749487376534751, "dur": 155, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.WindModule.dll_E81E4251EABAE467.mvfrm"}}, {"pid": 12345, "tid": 15, "ts": 1749487376534964, "dur": 154, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1749487376535133, "dur": 65, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1749487376535216, "dur": 60, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1749487376535326, "dur": 54, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1749487376535395, "dur": 87, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1749487376535493, "dur": 51, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1749487376535676, "dur": 122, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1749487376535856, "dur": 142, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1749487376536004, "dur": 120, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1749487376536127, "dur": 51, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualStudio.Editor.rsp"}}, {"pid": 12345, "tid": 15, "ts": 1749487376536179, "dur": 226, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1749487376536622, "dur": 1335, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1749487376537958, "dur": 759, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1749487376538718, "dur": 847, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1749487376539565, "dur": 1711, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1749487376541276, "dur": 1799, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1749487376544025, "dur": 733, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting@1.9.4\\Editor\\VisualScripting.Flow\\Framework\\InputSystemWidget.cs"}}, {"pid": 12345, "tid": 15, "ts": 1749487376543076, "dur": 1712, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1749487376544788, "dur": 1091, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1749487376545880, "dur": 1421, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1749487376547302, "dur": 1604, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1749487376548907, "dur": 1700, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1749487376550608, "dur": 1754, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1749487376552758, "dur": 505, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.collections@1.2.4\\Unity.Collections\\UTF8ArrayUnsafeUtility.cs"}}, {"pid": 12345, "tid": 15, "ts": 1749487376552362, "dur": 1929, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1749487376554292, "dur": 1102, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1749487376555395, "dur": 1347, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1749487376556742, "dur": 2148, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1749487376558922, "dur": 912, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1749487376559835, "dur": 489, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1749487376560331, "dur": 407, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.TextMeshPro.dll.mvfrm"}}, {"pid": 12345, "tid": 15, "ts": 1749487376560739, "dur": 569, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1749487376561314, "dur": 657, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.TextMeshPro.dll (+2 others)"}}, {"pid": 12345, "tid": 15, "ts": 1749487376561971, "dur": 925, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1749487376562907, "dur": 753, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1749487376563663, "dur": 66, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.TextMeshPro.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 15, "ts": 1749487376563840, "dur": 52, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.SubstanceModule.dll"}}, {"pid": 12345, "tid": 15, "ts": 1749487376563777, "dur": 504, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.TextMeshPro.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 15, "ts": 1749487376564282, "dur": 134, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1749487376564450, "dur": 76602, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1749487376641053, "dur": 3522, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.2D.Animation.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 15, "ts": 1749487376644575, "dur": 605, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1749487376646314, "dur": 815, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\Microsoft.Extensions.Configuration.FileExtensions.dll"}}, {"pid": 12345, "tid": 15, "ts": 1749487376647478, "dur": 77, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\System.Reflection.Metadata.dll"}}, {"pid": 12345, "tid": 15, "ts": 1749487376647679, "dur": 78, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\System.ValueTuple.dll"}}, {"pid": 12345, "tid": 15, "ts": 1749487376648609, "dur": 137, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\System.Net.Http.dll"}}, {"pid": 12345, "tid": 15, "ts": 1749487376645184, "dur": 3617, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/UnityEditor.UI.dll (+pdb)"}}, {"pid": 12345, "tid": 15, "ts": 1749487376648801, "dur": 2083, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1749487376651047, "dur": 269, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.UnityWebRequestWWWModule.dll"}}, {"pid": 12345, "tid": 15, "ts": 1749487376651602, "dur": 55, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\PlaybackEngines\\WebGLSupport\\UnityEditor.WebGL.Extensions.dll"}}, {"pid": 12345, "tid": 15, "ts": 1749487376652770, "dur": 63, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netfx\\System.ServiceModel.Web.dll"}}, {"pid": 12345, "tid": 15, "ts": 1749487376650894, "dur": 2435, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.PlasticSCM.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 15, "ts": 1749487376653329, "dur": 68, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1749487376653407, "dur": 54, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1749487376653466, "dur": 1400402, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1749487376519788, "dur": 14900, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1749487376534720, "dur": 75, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestTextureModule.dll_1CE9B2E479BEC5CA.mvfrm"}}, {"pid": 12345, "tid": 16, "ts": 1749487376534935, "dur": 171, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1749487376535110, "dur": 123, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1749487376535237, "dur": 114, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.burst@1.8.15\\Unity.Burst.CodeGen\\Unity.Burst.Cecil.Mdb.dll"}}, {"pid": 12345, "tid": 16, "ts": 1749487376535236, "dur": 116, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Burst.Cecil.Mdb.dll_CB8452BAE2BF35AA.mvfrm"}}, {"pid": 12345, "tid": 16, "ts": 1749487376535429, "dur": 74, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Core.Editor.rsp"}}, {"pid": 12345, "tid": 16, "ts": 1749487376535555, "dur": 133, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1749487376535760, "dur": 53, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1749487376535817, "dur": 56, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.2D.Aseprite.Common.rsp"}}, {"pid": 12345, "tid": 16, "ts": 1749487376536114, "dur": 89, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1749487376536276, "dur": 73, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1749487376536356, "dur": 141, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/5916180605834378387.rsp"}}, {"pid": 12345, "tid": 16, "ts": 1749487376536498, "dur": 74, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1749487376536640, "dur": 1678, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1749487376538318, "dur": 808, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1749487376539126, "dur": 1760, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1749487376540886, "dur": 1393, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1749487376542279, "dur": 1079, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1749487376543358, "dur": 859, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1749487376544217, "dur": 1874, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1749487376546091, "dur": 1687, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1749487376547779, "dur": 1430, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1749487376549209, "dur": 1089, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1749487376550298, "dur": 1428, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1749487376551726, "dur": 1666, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1749487376553416, "dur": 625, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting@1.9.4\\Runtime\\VisualScripting.Core\\Reflection\\Operators\\AmbiguousOperatorException.cs"}}, {"pid": 12345, "tid": 16, "ts": 1749487376553392, "dur": 1936, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1749487376555328, "dur": 663, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.render-pipelines.core@14.0.11\\Runtime\\Lighting\\ProbeVolume\\ProbeReferenceVolume.cs"}}, {"pid": 12345, "tid": 16, "ts": 1749487376555328, "dur": 1793, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1749487376557122, "dur": 1679, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1749487376558919, "dur": 923, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1749487376559843, "dur": 458, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1749487376560302, "dur": 522, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Core.dll.mvfrm"}}, {"pid": 12345, "tid": 16, "ts": 1749487376561140, "dur": 70, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.WindModule.dll"}}, {"pid": 12345, "tid": 16, "ts": 1749487376561211, "dur": 61, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.XRModule.dll"}}, {"pid": 12345, "tid": 16, "ts": 1749487376561872, "dur": 59, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting@1.9.4\\Runtime\\VisualScripting.Core\\EditorBinding\\AllowsNullAttribute.cs"}}, {"pid": 12345, "tid": 16, "ts": 1749487376562143, "dur": 52, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting@1.9.4\\Runtime\\VisualScripting.Core\\Input\\PressState.cs"}}, {"pid": 12345, "tid": 16, "ts": 1749487376560864, "dur": 1796, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Core.dll (+2 others)"}}, {"pid": 12345, "tid": 16, "ts": 1749487376562661, "dur": 480, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1749487376563157, "dur": 95, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1749487376563258, "dur": 114, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Flow.dll.mvfrm"}}, {"pid": 12345, "tid": 16, "ts": 1749487376563373, "dur": 514, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1749487376563889, "dur": 929, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Flow.dll (+2 others)"}}, {"pid": 12345, "tid": 16, "ts": 1749487376564819, "dur": 134, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1749487376564988, "dur": 60, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.State.dll.mvfrm"}}, {"pid": 12345, "tid": 16, "ts": 1749487376565070, "dur": 254, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.State.dll (+2 others)"}}, {"pid": 12345, "tid": 16, "ts": 1749487376565324, "dur": 66, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1749487376565448, "dur": 75660, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1749487376643075, "dur": 81, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\Microsoft.Extensions.Http.dll"}}, {"pid": 12345, "tid": 16, "ts": 1749487376643178, "dur": 169, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\Microsoft.Extensions.Logging.dll"}}, {"pid": 12345, "tid": 16, "ts": 1749487376643746, "dur": 76, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\System.Runtime.CompilerServices.VisualC.dll"}}, {"pid": 12345, "tid": 16, "ts": 1749487376644615, "dur": 57, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Resources.Reader.dll"}}, {"pid": 12345, "tid": 16, "ts": 1749487376645205, "dur": 51, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Security.Cryptography.Primitives.dll"}}, {"pid": 12345, "tid": 16, "ts": 1749487376641110, "dur": 4319, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.TextMeshPro.dll (+pdb)"}}, {"pid": 12345, "tid": 16, "ts": 1749487376645430, "dur": 62, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1749487376646303, "dur": 74, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.collab-proxy@2.5.2\\Lib\\Editor\\PlasticSCM\\Unity.Plastic.Antlr3.Runtime.dll"}}, {"pid": 12345, "tid": 16, "ts": 1749487376647118, "dur": 376, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\System.Threading.ThreadPool.dll"}}, {"pid": 12345, "tid": 16, "ts": 1749487376648613, "dur": 60, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\System.Drawing.dll"}}, {"pid": 12345, "tid": 16, "ts": 1749487376645506, "dur": 3220, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.VisualScripting.Flow.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 16, "ts": 1749487376648727, "dur": 522, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1749487376649749, "dur": 94, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Runtime.Numerics.dll"}}, {"pid": 12345, "tid": 16, "ts": 1749487376650342, "dur": 85, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\api-ms-win-core-console-l1-2-0.dll"}}, {"pid": 12345, "tid": 16, "ts": 1749487376651707, "dur": 534, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.ComponentModel.EventBasedAsync.dll"}}, {"pid": 12345, "tid": 16, "ts": 1749487376652276, "dur": 70, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Diagnostics.Debug.dll"}}, {"pid": 12345, "tid": 16, "ts": 1749487376652584, "dur": 60, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Xml.XPath.XDocument.dll"}}, {"pid": 12345, "tid": 16, "ts": 1749487376652743, "dur": 67, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.collections@1.2.4\\Unity.Collections.LowLevel.ILSupport\\Unity.Collections.LowLevel.ILSupport.dll"}}, {"pid": 12345, "tid": 16, "ts": 1749487376649256, "dur": 3592, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Cursor.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 16, "ts": 1749487376652848, "dur": 544, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1749487376653439, "dur": 1400346, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1749487376519709, "dur": 14890, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1749487376534615, "dur": 99, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.TextCoreFontEngineModule.dll"}}, {"pid": 12345, "tid": 17, "ts": 1749487376534606, "dur": 111, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TextCoreFontEngineModule.dll_758CA54D648AA1BC.mvfrm"}}, {"pid": 12345, "tid": 17, "ts": 1749487376534717, "dur": 147, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1749487376534966, "dur": 84, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.collections@1.2.4\\Unity.Collections.LowLevel.ILSupport\\Unity.Collections.LowLevel.ILSupport.dll"}}, {"pid": 12345, "tid": 17, "ts": 1749487376534965, "dur": 86, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Collections.LowLevel.ILSupport.dll_2E40CE80E145E521.mvfrm"}}, {"pid": 12345, "tid": 17, "ts": 1749487376535263, "dur": 106, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1749487376535426, "dur": 66, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1749487376535507, "dur": 59, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1749487376535635, "dur": 84, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1749487376535724, "dur": 255, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1749487376536008, "dur": 93, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1749487376536636, "dur": 1166, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1749487376537802, "dur": 1290, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1749487376539093, "dur": 1547, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1749487376540640, "dur": 1763, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1749487376543141, "dur": 555, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting@1.9.4\\Editor\\VisualScripting.Flow\\XFlowGraph.cs"}}, {"pid": 12345, "tid": 17, "ts": 1749487376543987, "dur": 759, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting@1.9.4\\Editor\\VisualScripting.Flow\\Units\\UnitInspector.cs"}}, {"pid": 12345, "tid": 17, "ts": 1749487376542403, "dur": 3046, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1749487376545449, "dur": 1020, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1749487376546469, "dur": 2181, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1749487376548650, "dur": 1158, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1749487376549808, "dur": 1391, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1749487376551199, "dur": 886, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1749487376552085, "dur": 1377, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1749487376553463, "dur": 1222, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1749487376554685, "dur": 1892, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1749487376556577, "dur": 1192, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1749487376557770, "dur": 1430, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1749487376559200, "dur": 633, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1749487376559833, "dur": 472, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1749487376560306, "dur": 168, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.InternalAPIEngineBridge.001.dll.mvfrm"}}, {"pid": 12345, "tid": 17, "ts": 1749487376560475, "dur": 118, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1749487376560698, "dur": 123, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.AssetBundleModule.dll"}}, {"pid": 12345, "tid": 17, "ts": 1749487376560604, "dur": 654, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.InternalAPIEngineBridge.001.dll (+2 others)"}}, {"pid": 12345, "tid": 17, "ts": 1749487376561258, "dur": 84, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1749487376561376, "dur": 100, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.2D.IK.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 17, "ts": 1749487376561477, "dur": 50, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1749487376561529, "dur": 399, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.2D.IK.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 17, "ts": 1749487376561928, "dur": 70, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1749487376562033, "dur": 81, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.2D.Tilemap.Editor.ref.dll_436C0E2610862891.mvfrm"}}, {"pid": 12345, "tid": 17, "ts": 1749487376562277, "dur": 371, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1749487376562649, "dur": 498, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.CollabProxy.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 17, "ts": 1749487376563147, "dur": 77, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1749487376563230, "dur": 701, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1749487376563931, "dur": 2395, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1749487376566327, "dur": 74777, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1749487376641239, "dur": 50, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.ClothModule.dll"}}, {"pid": 12345, "tid": 17, "ts": 1749487376641682, "dur": 51, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Linq.Parallel.dll"}}, {"pid": 12345, "tid": 17, "ts": 1749487376643634, "dur": 67, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\System.Threading.Tasks.Extensions.dll"}}, {"pid": 12345, "tid": 17, "ts": 1749487376644632, "dur": 62, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Net.Sockets.dll"}}, {"pid": 12345, "tid": 17, "ts": 1749487376644716, "dur": 224, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Reflection.dll"}}, {"pid": 12345, "tid": 17, "ts": 1749487376641106, "dur": 4222, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.Universal.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 17, "ts": 1749487376645328, "dur": 2739, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1749487376648611, "dur": 52, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Text.RegularExpressions.dll"}}, {"pid": 12345, "tid": 17, "ts": 1749487376648075, "dur": 2983, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.2D.Animation.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 17, "ts": 1749487376651059, "dur": 116, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1749487376651542, "dur": 103, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Linq.Queryable.dll"}}, {"pid": 12345, "tid": 17, "ts": 1749487376651186, "dur": 2327, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.2D.Aseprite.Common.dll (+pdb)"}}, {"pid": 12345, "tid": 17, "ts": 1749487376653514, "dur": 61, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1749487376653627, "dur": 1400186, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1749487376519732, "dur": 14919, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1749487376534681, "dur": 80, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.UnityConnectModule.dll"}}, {"pid": 12345, "tid": 18, "ts": 1749487376534668, "dur": 96, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityConnectModule.dll_D0DC9EA7252E13B4.mvfrm"}}, {"pid": 12345, "tid": 18, "ts": 1749487376534764, "dur": 60, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1749487376534831, "dur": 83, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.AccessibilityModule.dll"}}, {"pid": 12345, "tid": 18, "ts": 1749487376534829, "dur": 87, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AccessibilityModule.dll_976B38ECCFA49043.mvfrm"}}, {"pid": 12345, "tid": 18, "ts": 1749487376534916, "dur": 133, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1749487376535088, "dur": 105, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1749487376535356, "dur": 88, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1749487376535532, "dur": 79, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1749487376535682, "dur": 235, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1749487376535935, "dur": 211, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1749487376536357, "dur": 55, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1749487376536507, "dur": 87, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1749487376536617, "dur": 50, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1749487376536671, "dur": 1643, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1749487376538314, "dur": 1511, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1749487376539826, "dur": 855, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1749487376540682, "dur": 1544, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1749487376542227, "dur": 1154, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1749487376543382, "dur": 1766, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1749487376546124, "dur": 540, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.shadergraph@14.0.11\\Editor\\Data\\Nodes\\Math\\Vector\\TransformNode.cs"}}, {"pid": 12345, "tid": 18, "ts": 1749487376545148, "dur": 2607, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1749487376547755, "dur": 845, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1749487376548601, "dur": 1233, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1749487376549834, "dur": 1784, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1749487376551619, "dur": 924, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1749487376552543, "dur": 1719, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1749487376554263, "dur": 1262, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1749487376555525, "dur": 1297, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1749487376556822, "dur": 714, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1749487376557536, "dur": 1055, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1749487376558591, "dur": 977, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1749487376559568, "dur": 277, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1749487376559846, "dur": 457, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1749487376560304, "dur": 178, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.2D.Sprite.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 18, "ts": 1749487376560697, "dur": 59, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.UnityCurlModule.dll"}}, {"pid": 12345, "tid": 18, "ts": 1749487376560520, "dur": 668, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.2D.Sprite.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 18, "ts": 1749487376561188, "dur": 1168, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1749487376562393, "dur": 111, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.InternalAPIEditorBridge.001.dll.mvfrm"}}, {"pid": 12345, "tid": 18, "ts": 1749487376562877, "dur": 86, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.collab-proxy@2.5.2\\Lib\\Editor\\PlasticSCM\\Unity.Plastic.Newtonsoft.Json.dll"}}, {"pid": 12345, "tid": 18, "ts": 1749487376562532, "dur": 467, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.InternalAPIEditorBridge.001.dll (+2 others)"}}, {"pid": 12345, "tid": 18, "ts": 1749487376562999, "dur": 1636, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1749487376564667, "dur": 71, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.2D.Animation.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 18, "ts": 1749487376564767, "dur": 59, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.2D.Animation.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 18, "ts": 1749487376564827, "dur": 440, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.2D.Animation.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 18, "ts": 1749487376565268, "dur": 63, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1749487376565362, "dur": 64, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.2D.Psdimporter.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 18, "ts": 1749487376565449, "dur": 255, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.2D.Psdimporter.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 18, "ts": 1749487376565704, "dur": 59, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1749487376565787, "dur": 75267, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1749487376641055, "dur": 2917, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.Universal.Shaders.dll (+pdb)"}}, {"pid": 12345, "tid": 18, "ts": 1749487376643974, "dur": 1165, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1749487376645554, "dur": 54, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Security.Cryptography.X509Certificates.dll"}}, {"pid": 12345, "tid": 18, "ts": 1749487376647425, "dur": 66, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.IO.Pipes.dll"}}, {"pid": 12345, "tid": 18, "ts": 1749487376645149, "dur": 2969, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.VisualScripting.Shared.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 18, "ts": 1749487376648119, "dur": 73, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1749487376648309, "dur": 68, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.dll"}}, {"pid": 12345, "tid": 18, "ts": 1749487376648495, "dur": 55, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.VideoModule.dll"}}, {"pid": 12345, "tid": 18, "ts": 1749487376648198, "dur": 2782, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Timeline.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 18, "ts": 1749487376650981, "dur": 197, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1749487376651203, "dur": 2193, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.2D.Common.Path.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 18, "ts": 1749487376653397, "dur": 51, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1749487376653475, "dur": 1400312, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1749487376519750, "dur": 14926, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1749487376534692, "dur": 62, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.UnityTestProtocolModule.dll"}}, {"pid": 12345, "tid": 19, "ts": 1749487376534683, "dur": 74, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityTestProtocolModule.dll_2FC980318CB13784.mvfrm"}}, {"pid": 12345, "tid": 19, "ts": 1749487376534803, "dur": 103, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.SpriteMaskModule.dll"}}, {"pid": 12345, "tid": 19, "ts": 1749487376534802, "dur": 108, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.SpriteMaskModule.dll_6555E2061ABDDC70.mvfrm"}}, {"pid": 12345, "tid": 19, "ts": 1749487376534911, "dur": 146, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1749487376535064, "dur": 70, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting@1.9.4\\Editor\\VisualScripting.Core\\Dependencies\\DotNetZip\\Unity.VisualScripting.IonicZip.dll"}}, {"pid": 12345, "tid": 19, "ts": 1749487376535063, "dur": 73, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.VisualScripting.IonicZip.dll_840FAE5C8A141D5B.mvfrm"}}, {"pid": 12345, "tid": 19, "ts": 1749487376535136, "dur": 197, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1749487376535343, "dur": 84, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/UnityEngine.UI.dll.mvfrm"}}, {"pid": 12345, "tid": 19, "ts": 1749487376535657, "dur": 104, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netfx\\System.Core.dll"}}, {"pid": 12345, "tid": 19, "ts": 1749487376535762, "dur": 156, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netfx\\System.Data.dll"}}, {"pid": 12345, "tid": 19, "ts": 1749487376535922, "dur": 67, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netfx\\System.IO.Compression.FileSystem.dll"}}, {"pid": 12345, "tid": 19, "ts": 1749487376535990, "dur": 52, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netfx\\System.Net.dll"}}, {"pid": 12345, "tid": 19, "ts": 1749487376536089, "dur": 67, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netfx\\System.Runtime.Serialization.dll"}}, {"pid": 12345, "tid": 19, "ts": 1749487376536230, "dur": 89, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netfx\\System.Web.dll"}}, {"pid": 12345, "tid": 19, "ts": 1749487376536376, "dur": 161, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netfx\\System.Xml.Linq.dll"}}, {"pid": 12345, "tid": 19, "ts": 1749487376536603, "dur": 50, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Buffers.dll"}}, {"pid": 12345, "tid": 19, "ts": 1749487376536654, "dur": 132, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Collections.Concurrent.dll"}}, {"pid": 12345, "tid": 19, "ts": 1749487376536788, "dur": 53, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Collections.dll"}}, {"pid": 12345, "tid": 19, "ts": 1749487376536917, "dur": 227, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.ComponentModel.dll"}}, {"pid": 12345, "tid": 19, "ts": 1749487376537145, "dur": 94, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.ComponentModel.EventBasedAsync.dll"}}, {"pid": 12345, "tid": 19, "ts": 1749487376537263, "dur": 73, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.ComponentModel.TypeConverter.dll"}}, {"pid": 12345, "tid": 19, "ts": 1749487376537371, "dur": 270, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Data.Common.dll"}}, {"pid": 12345, "tid": 19, "ts": 1749487376537646, "dur": 61, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Diagnostics.FileVersionInfo.dll"}}, {"pid": 12345, "tid": 19, "ts": 1749487376537708, "dur": 52, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Diagnostics.Process.dll"}}, {"pid": 12345, "tid": 19, "ts": 1749487376537904, "dur": 287, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Diagnostics.TraceSource.dll"}}, {"pid": 12345, "tid": 19, "ts": 1749487376538243, "dur": 129, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Dynamic.Runtime.dll"}}, {"pid": 12345, "tid": 19, "ts": 1749487376538424, "dur": 111, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Globalization.Extensions.dll"}}, {"pid": 12345, "tid": 19, "ts": 1749487376538623, "dur": 238, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.IO.FileSystem.Primitives.dll"}}, {"pid": 12345, "tid": 19, "ts": 1749487376538943, "dur": 175, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Net.NameResolution.dll"}}, {"pid": 12345, "tid": 19, "ts": 1749487376539350, "dur": 204, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Net.WebSockets.dll"}}, {"pid": 12345, "tid": 19, "ts": 1749487376539650, "dur": 60, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Reflection.DispatchProxy.dll"}}, {"pid": 12345, "tid": 19, "ts": 1749487376539711, "dur": 63, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Reflection.dll"}}, {"pid": 12345, "tid": 19, "ts": 1749487376539775, "dur": 69, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Reflection.Emit.dll"}}, {"pid": 12345, "tid": 19, "ts": 1749487376539845, "dur": 123, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Reflection.Emit.ILGeneration.dll"}}, {"pid": 12345, "tid": 19, "ts": 1749487376540181, "dur": 172, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Runtime.InteropServices.dll"}}, {"pid": 12345, "tid": 19, "ts": 1749487376540512, "dur": 168, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Security.Cryptography.Csp.dll"}}, {"pid": 12345, "tid": 19, "ts": 1749487376540708, "dur": 191, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Security.Cryptography.Primitives.dll"}}, {"pid": 12345, "tid": 19, "ts": 1749487376540969, "dur": 124, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Text.Encoding.dll"}}, {"pid": 12345, "tid": 19, "ts": 1749487376541239, "dur": 207, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Threading.Tasks.Parallel.dll"}}, {"pid": 12345, "tid": 19, "ts": 1749487376541458, "dur": 75, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Xml.XmlSerializer.dll"}}, {"pid": 12345, "tid": 19, "ts": 1749487376541534, "dur": 174, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Xml.XPath.dll"}}, {"pid": 12345, "tid": 19, "ts": 1749487376541814, "dur": 416, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.collab-proxy@2.5.2\\Lib\\Editor\\PlasticSCM\\unityplastic.dll"}}, {"pid": 12345, "tid": 19, "ts": 1749487376542526, "dur": 189, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.ugui@1.0.0\\Runtime\\EventSystem\\InputModules\\BaseInput.cs"}}, {"pid": 12345, "tid": 19, "ts": 1749487376542715, "dur": 563, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.ugui@1.0.0\\Runtime\\EventSystem\\InputModules\\BaseInputModule.cs"}}, {"pid": 12345, "tid": 19, "ts": 1749487376543371, "dur": 287, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.ugui@1.0.0\\Runtime\\EventSystem\\InputModules\\TouchInputModule.cs"}}, {"pid": 12345, "tid": 19, "ts": 1749487376543684, "dur": 196, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.ugui@1.0.0\\Runtime\\EventSystem\\RaycasterManager.cs"}}, {"pid": 12345, "tid": 19, "ts": 1749487376543945, "dur": 214, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.ugui@1.0.0\\Runtime\\EventSystem\\RaycastResult.cs"}}, {"pid": 12345, "tid": 19, "ts": 1749487376544343, "dur": 342, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.ugui@1.0.0\\Runtime\\UI\\Core\\Culling\\ClipperRegistry.cs"}}, {"pid": 12345, "tid": 19, "ts": 1749487376544755, "dur": 69, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.ugui@1.0.0\\Runtime\\UI\\Core\\Culling\\RectangularVertexClipper.cs"}}, {"pid": 12345, "tid": 19, "ts": 1749487376544893, "dur": 212, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.ugui@1.0.0\\Runtime\\UI\\Core\\FontData.cs"}}, {"pid": 12345, "tid": 19, "ts": 1749487376545284, "dur": 171, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.ugui@1.0.0\\Runtime\\UI\\Core\\InputField.cs"}}, {"pid": 12345, "tid": 19, "ts": 1749487376545506, "dur": 50, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.ugui@1.0.0\\Runtime\\UI\\Core\\Layout\\CanvasScaler.cs"}}, {"pid": 12345, "tid": 19, "ts": 1749487376545557, "dur": 313, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.ugui@1.0.0\\Runtime\\UI\\Core\\Layout\\ContentSizeFitter.cs"}}, {"pid": 12345, "tid": 19, "ts": 1749487376545871, "dur": 50, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.ugui@1.0.0\\Runtime\\UI\\Core\\Layout\\GridLayoutGroup.cs"}}, {"pid": 12345, "tid": 19, "ts": 1749487376546061, "dur": 53, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.ugui@1.0.0\\Runtime\\UI\\Core\\Layout\\LayoutElement.cs"}}, {"pid": 12345, "tid": 19, "ts": 1749487376546115, "dur": 52, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.ugui@1.0.0\\Runtime\\UI\\Core\\Layout\\LayoutGroup.cs"}}, {"pid": 12345, "tid": 19, "ts": 1749487376546341, "dur": 51, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.ugui@1.0.0\\Runtime\\UI\\Core\\MaskableGraphic.cs"}}, {"pid": 12345, "tid": 19, "ts": 1749487376546436, "dur": 613, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.ugui@1.0.0\\Runtime\\UI\\Core\\MaterialModifiers\\IMaterialModifier.cs"}}, {"pid": 12345, "tid": 19, "ts": 1749487376547072, "dur": 59, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.ugui@1.0.0\\Runtime\\UI\\Core\\MultipleDisplayUtilities.cs"}}, {"pid": 12345, "tid": 19, "ts": 1749487376547178, "dur": 219, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.ugui@1.0.0\\Runtime\\UI\\Core\\RectMask2D.cs"}}, {"pid": 12345, "tid": 19, "ts": 1749487376547467, "dur": 357, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.ugui@1.0.0\\Runtime\\UI\\Core\\SetPropertyUtility.cs"}}, {"pid": 12345, "tid": 19, "ts": 1749487376547848, "dur": 152, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.ugui@1.0.0\\Runtime\\UI\\Core\\SpecializedCollections\\IndexedSet.cs"}}, {"pid": 12345, "tid": 19, "ts": 1749487376548102, "dur": 133, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.ugui@1.0.0\\Runtime\\UI\\Core\\Utility\\ReflectionMethodsCache.cs"}}, {"pid": 12345, "tid": 19, "ts": 1749487376548236, "dur": 252, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.ugui@1.0.0\\Runtime\\UI\\Core\\Utility\\VertexHelper.cs"}}, {"pid": 12345, "tid": 19, "ts": 1749487376548489, "dur": 182, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.ugui@1.0.0\\Runtime\\UI\\Core\\VertexModifiers\\BaseMeshEffect.cs"}}, {"pid": 12345, "tid": 19, "ts": 1749487376548672, "dur": 180, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.ugui@1.0.0\\Runtime\\UI\\Core\\VertexModifiers\\IMeshModifier.cs"}}, {"pid": 12345, "tid": 19, "ts": 1749487376548853, "dur": 164, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.ugui@1.0.0\\Runtime\\UI\\Core\\VertexModifiers\\Outline.cs"}}, {"pid": 12345, "tid": 19, "ts": 1749487376549052, "dur": 268, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.ugui@1.0.0\\Runtime\\UI\\Core\\VertexModifiers\\Shadow.cs"}}, {"pid": 12345, "tid": 19, "ts": 1749487376549345, "dur": 134, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Tools\\Unity.SourceGenerators\\Unity.SourceGenerators.dll"}}, {"pid": 12345, "tid": 19, "ts": 1749487376535477, "dur": 14004, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/UnityEngine.UI.dll (+2 others)"}}, {"pid": 12345, "tid": 19, "ts": 1749487376549481, "dur": 325, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1749487376549887, "dur": 1338, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1749487376551225, "dur": 1392, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1749487376552618, "dur": 1295, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1749487376553913, "dur": 1500, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1749487376555414, "dur": 1526, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1749487376556941, "dur": 1505, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1749487376558447, "dur": 1101, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1749487376559548, "dur": 306, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1749487376559855, "dur": 698, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1749487376560554, "dur": 629, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.ShaderGraph.ShaderGraphLibrary.dll.mvfrm"}}, {"pid": 12345, "tid": 19, "ts": 1749487376561217, "dur": 396, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.ShaderGraph.ShaderGraphLibrary.dll (+2 others)"}}, {"pid": 12345, "tid": 19, "ts": 1749487376561613, "dur": 534, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1749487376562286, "dur": 595, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1749487376562881, "dur": 230, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1749487376563111, "dur": 818, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1749487376563929, "dur": 2002, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1749487376565932, "dur": 53, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.SettingsProvider.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 19, "ts": 1749487376566018, "dur": 211, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.SettingsProvider.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 19, "ts": 1749487376566229, "dur": 59, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1749487376566315, "dur": 54, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/PPv2URPConverters.dll.mvfrm"}}, {"pid": 12345, "tid": 19, "ts": 1749487376566389, "dur": 204, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/PPv2URPConverters.dll (+2 others)"}}, {"pid": 12345, "tid": 19, "ts": 1749487376566593, "dur": 61, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1749487376566656, "dur": 74434, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1749487376641546, "dur": 105, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.VehiclesModule.dll"}}, {"pid": 12345, "tid": 19, "ts": 1749487376641710, "dur": 78, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netfx\\System.IO.Compression.FileSystem.dll"}}, {"pid": 12345, "tid": 19, "ts": 1749487376643899, "dur": 428, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\System.Threading.Thread.dll"}}, {"pid": 12345, "tid": 19, "ts": 1749487376641111, "dur": 4249, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.2D.SpriteShape.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 19, "ts": 1749487376645360, "dur": 615, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1749487376645986, "dur": 2701, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.VisualScripting.Core.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 19, "ts": 1749487376648687, "dur": 230, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1749487376650874, "dur": 185, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Xml.XDocument.dll"}}, {"pid": 12345, "tid": 19, "ts": 1749487376648928, "dur": 2467, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.ShaderGraph.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 19, "ts": 1749487376651396, "dur": 416, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1749487376651822, "dur": 53, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1749487376651936, "dur": 50, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1749487376651994, "dur": 76, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1749487376652077, "dur": 78, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1749487376652164, "dur": 64, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1749487376652233, "dur": 676, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1749487376652912, "dur": 748, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1749487376653685, "dur": 1400135, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1749487376519769, "dur": 14914, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1749487376534686, "dur": 51, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestAssetBundleModule.dll_EC1D9C7602E10CB1.mvfrm"}}, {"pid": 12345, "tid": 20, "ts": 1749487376534737, "dur": 86, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1749487376534826, "dur": 109, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.AndroidJNIModule.dll"}}, {"pid": 12345, "tid": 20, "ts": 1749487376534826, "dur": 111, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AndroidJNIModule.dll_51DC255B330D5605.mvfrm"}}, {"pid": 12345, "tid": 20, "ts": 1749487376534938, "dur": 105, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1749487376535104, "dur": 196, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1749487376535308, "dur": 156, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1749487376535508, "dur": 72, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1749487376535629, "dur": 61, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.ShaderGraph.Editor.rsp"}}, {"pid": 12345, "tid": 20, "ts": 1749487376535768, "dur": 148, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1749487376536033, "dur": 112, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1749487376536179, "dur": 56, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1749487376536628, "dur": 1240, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1749487376537869, "dur": 1636, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1749487376539747, "dur": 550, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.ide.cursor@2c0153a9ba\\Editor\\ProjectGeneration\\AssemblyNameProvider.cs"}}, {"pid": 12345, "tid": 20, "ts": 1749487376539505, "dur": 2059, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1749487376541565, "dur": 1381, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1749487376542982, "dur": 587, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting@1.9.4\\Editor\\VisualScripting.Flow\\Linker\\LinkerCreator.cs"}}, {"pid": 12345, "tid": 20, "ts": 1749487376542947, "dur": 1817, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1749487376546650, "dur": 575, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.shadergraph@14.0.11\\Editor\\Drawing\\Inspector\\PropertyDrawers\\PositionNodePropertyDrawer.cs"}}, {"pid": 12345, "tid": 20, "ts": 1749487376544764, "dur": 2625, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1749487376547390, "dur": 1212, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1749487376548602, "dur": 1127, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1749487376549730, "dur": 1025, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1749487376550759, "dur": 143, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1749487376550906, "dur": 1290, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1749487376552196, "dur": 2100, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1749487376554297, "dur": 1371, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1749487376555669, "dur": 947, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1749487376556617, "dur": 1881, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1749487376558498, "dur": 1005, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1749487376559503, "dur": 337, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1749487376559841, "dur": 468, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1749487376560311, "dur": 129, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.2D.PixelPerfect.dll.mvfrm"}}, {"pid": 12345, "tid": 20, "ts": 1749487376560829, "dur": 62, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Diagnostics.TextWriterTraceListener.dll"}}, {"pid": 12345, "tid": 20, "ts": 1749487376561076, "dur": 126, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Runtime.Serialization.Primitives.dll"}}, {"pid": 12345, "tid": 20, "ts": 1749487376560487, "dur": 817, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.2D.PixelPerfect.dll (+2 others)"}}, {"pid": 12345, "tid": 20, "ts": 1749487376561305, "dur": 883, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1749487376562194, "dur": 1190, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1749487376563387, "dur": 537, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1749487376563925, "dur": 1446, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1749487376565372, "dur": 117, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.2D.IK.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 20, "ts": 1749487376565490, "dur": 60, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1749487376565556, "dur": 237, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.2D.IK.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 20, "ts": 1749487376565793, "dur": 54, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1749487376565871, "dur": 75190, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1749487376641530, "dur": 127, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.VFXModule.dll"}}, {"pid": 12345, "tid": 20, "ts": 1749487376642237, "dur": 117, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Diagnostics.Process.dll"}}, {"pid": 12345, "tid": 20, "ts": 1749487376643053, "dur": 114, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\Microsoft.AspNetCore.Http.Abstractions.dll"}}, {"pid": 12345, "tid": 20, "ts": 1749487376643612, "dur": 118, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\System.IO.FileSystem.DriveInfo.dll"}}, {"pid": 12345, "tid": 20, "ts": 1749487376643909, "dur": 409, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\System.Runtime.Extensions.dll"}}, {"pid": 12345, "tid": 20, "ts": 1749487376641064, "dur": 4373, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.Core.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 20, "ts": 1749487376645438, "dur": 174, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1749487376647112, "dur": 58, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\mscorrc.dll"}}, {"pid": 12345, "tid": 20, "ts": 1749487376647479, "dur": 320, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\System.Threading.Thread.dll"}}, {"pid": 12345, "tid": 20, "ts": 1749487376647828, "dur": 52, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\System.Xml.XPath.dll"}}, {"pid": 12345, "tid": 20, "ts": 1749487376648610, "dur": 136, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.ServiceModel.Duplex.dll"}}, {"pid": 12345, "tid": 20, "ts": 1749487376645618, "dur": 3282, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.2D.Common.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 20, "ts": 1749487376648901, "dur": 2376, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1749487376651288, "dur": 293, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1749487376651589, "dur": 458, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1749487376652120, "dur": 229, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1749487376652415, "dur": 79, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1749487376652563, "dur": 103, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1749487376652675, "dur": 65, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1749487376652787, "dur": 513, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1749487376653301, "dur": 813643, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1749487377466957, "dur": 153, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1900b0aE.dag\\post-processed\\Assembly-CSharp.pdb"}}, {"pid": 12345, "tid": 20, "ts": 1749487377466952, "dur": 159, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Assembly-CSharp.pdb"}}, {"pid": 12345, "tid": 20, "ts": 1749487377467155, "dur": 1722, "ph": "X", "name": "CopyFiles", "args": {"detail": "Library/ScriptAssemblies/Assembly-CSharp.pdb"}}, {"pid": 12345, "tid": 20, "ts": 1749487377468881, "dur": 584962, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1749487378061420, "dur": 3313, "ph": "X", "name": "ProfilerWriteOutput"}, {"pid": 18384, "tid": 771, "ts": 1749487378076243, "dur": 4597, "ph": "X", "name": "Wait for external events", "args": {"First to finish": "backend1.traceevents"}}, {"pid": 18384, "tid": 771, "ts": 1749487378080906, "dur": 2205, "ph": "X", "name": "backend1.traceevents", "args": {}}, {"pid": 18384, "tid": 771, "ts": 1749487378071957, "dur": 11969, "ph": "X", "name": "Write chrome-trace events", "args": {}}, {}]}